{"ast": null, "code": "/**\n * @typedef {import('micromark-util-types').Code} Code\n */\n\nimport { markdownLineEndingOrSpace, unicodePunctuation, unicodeWhitespace } from 'micromark-util-character';\nimport { codes } from 'micromark-util-symbol/codes.js';\nimport { constants } from 'micromark-util-symbol/constants.js';\n\n/**\n * Classify whether a code represents whitespace, punctuation, or something\n * else.\n *\n * Used for attention (emphasis, strong), whose sequences can open or close\n * based on the class of surrounding characters.\n *\n * > 👉 **Note**: eof (`null`) is seen as whitespace.\n *\n * @param {Code} code\n *   Code.\n * @returns {typeof constants.characterGroupWhitespace | typeof constants.characterGroupPunctuation | undefined}\n *   Group.\n */\nexport function classifyCharacter(code) {\n  if (code === codes.eof || markdownLineEndingOrSpace(code) || unicodeWhitespace(code)) {\n    return constants.characterGroupWhitespace;\n  }\n  if (unicodePunctuation(code)) {\n    return constants.characterGroupPunctuation;\n  }\n}", "map": {"version": 3, "names": ["markdownLineEndingOrSpace", "unicodePunctuation", "unicodeWhitespace", "codes", "constants", "classifyCharacter", "code", "eof", "characterGroupWhitespace", "characterGroupPunctuation"], "sources": ["C:/Users/<USER>/PycharmProjects/智能数据分析系统/chatdb/frontend/node_modules/micromark-util-classify-character/dev/index.js"], "sourcesContent": ["/**\n * @typedef {import('micromark-util-types').Code} Code\n */\n\nimport {\n  markdownLineEndingOrSpace,\n  unicodePunctuation,\n  unicodeWhitespace\n} from 'micromark-util-character'\nimport {codes} from 'micromark-util-symbol/codes.js'\nimport {constants} from 'micromark-util-symbol/constants.js'\n\n/**\n * Classify whether a code represents whitespace, punctuation, or something\n * else.\n *\n * Used for attention (emphasis, strong), whose sequences can open or close\n * based on the class of surrounding characters.\n *\n * > 👉 **Note**: eof (`null`) is seen as whitespace.\n *\n * @param {Code} code\n *   Code.\n * @returns {typeof constants.characterGroupWhitespace | typeof constants.characterGroupPunctuation | undefined}\n *   Group.\n */\nexport function classifyCharacter(code) {\n  if (\n    code === codes.eof ||\n    markdownLineEndingOrSpace(code) ||\n    unicodeWhitespace(code)\n  ) {\n    return constants.characterGroupWhitespace\n  }\n\n  if (unicodePunctuation(code)) {\n    return constants.characterGroupPunctuation\n  }\n}\n"], "mappings": "AAAA;AACA;AACA;;AAEA,SACEA,yBAAyB,EACzBC,kBAAkB,EAClBC,iBAAiB,QACZ,0BAA0B;AACjC,SAAQC,KAAK,QAAO,gCAAgC;AACpD,SAAQC,SAAS,QAAO,oCAAoC;;AAE5D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,iBAAiBA,CAACC,IAAI,EAAE;EACtC,IACEA,IAAI,KAAKH,KAAK,CAACI,GAAG,IAClBP,yBAAyB,CAACM,IAAI,CAAC,IAC/BJ,iBAAiB,CAACI,IAAI,CAAC,EACvB;IACA,OAAOF,SAAS,CAACI,wBAAwB;EAC3C;EAEA,IAAIP,kBAAkB,CAACK,IAAI,CAAC,EAAE;IAC5B,OAAOF,SAAS,CAACK,yBAAyB;EAC5C;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}