from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

from app.core.config import settings

def get_database_url():
    """根据配置返回相应的数据库连接URL"""
    if settings.DATABASE_TYPE.lower() == "sqlite":
        # SQLite连接字符串
        return f"sqlite:///{settings.SQLITE_DB_PATH}"
    elif settings.DATABASE_TYPE.lower() == "mysql":
        # MySQL连接字符串
        return (
            f"mysql+pymysql://{settings.MYSQL_USER}:{settings.MYSQL_PASSWORD}@"
            f"{settings.MYSQL_SERVER}:{settings.MYSQL_PORT}/{settings.MYSQL_DB}"
        )
    else:
        raise ValueError(f"Unsupported database type: {settings.DATABASE_TYPE}")

# 动态获取数据库连接URL
SQLALCHEMY_DATABASE_URL = get_database_url()

# 为SQLite配置特殊参数和连接池优化
if settings.DATABASE_TYPE.lower() == "sqlite":
    engine = create_engine(
        SQLALCHEMY_DATABASE_URL,
        connect_args={
            "check_same_thread": False,  # SQLite特有配置
            "timeout": 30,  # 连接超时时间
        },
        echo=False,  # 可以设置为True来调试SQL语句
        pool_size=10,  # 连接池大小
        max_overflow=20,  # 最大溢出连接数
        pool_timeout=30,  # 获取连接的超时时间
        pool_recycle=3600,  # 连接回收时间（1小时）
        pool_pre_ping=True,  # 连接前ping检查
    )
else:
    engine = create_engine(
        SQLALCHEMY_DATABASE_URL,
        pool_size=10,
        max_overflow=20,
        pool_timeout=30,
        pool_recycle=3600,
        pool_pre_ping=True,
    )

SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# 应用SQLite优化设置
if settings.DATABASE_TYPE.lower() == "sqlite":
    from app.utils.sqlite_utils import apply_sqlite_optimizations
    apply_sqlite_optimizations(engine)


# Dependency to get DB session
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()
