{"ast": null, "code": "var formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: 'sekunddan kam',\n    other: '{{count}} sekunddan kam'\n  },\n  xSeconds: {\n    one: '1 sekund',\n    other: '{{count}} sekund'\n  },\n  halfAMinute: 'yarim minut',\n  lessThanXMinutes: {\n    one: 'bir minutdan kam',\n    other: '{{count}} minutdan kam'\n  },\n  xMinutes: {\n    one: '1 minut',\n    other: '{{count}} minut'\n  },\n  aboutXHours: {\n    one: 'tahminan 1 soat',\n    other: 'tahminan {{count}} soat'\n  },\n  xHours: {\n    one: '1 soat',\n    other: '{{count}} soat'\n  },\n  xDays: {\n    one: '1 kun',\n    other: '{{count}} kun'\n  },\n  aboutXWeeks: {\n    one: 'tahminan 1 hafta',\n    other: 'tahminan {{count}} hafta'\n  },\n  xWeeks: {\n    one: '1 hafta',\n    other: '{{count}} hafta'\n  },\n  aboutXMonths: {\n    one: 'tahminan 1 oy',\n    other: 'tahminan {{count}} oy'\n  },\n  xMonths: {\n    one: '1 oy',\n    other: '{{count}} oy'\n  },\n  aboutXYears: {\n    one: 'tahminan 1 yil',\n    other: 'tahminan {{count}} yil'\n  },\n  xYears: {\n    one: '1 yil',\n    other: '{{count}} yil'\n  },\n  overXYears: {\n    one: \"1 yildan ko'p\",\n    other: \"{{count}} yildan ko'p\"\n  },\n  almostXYears: {\n    one: 'deyarli 1 yil',\n    other: 'deyarli {{count}} yil'\n  }\n};\nvar formatDistance = function formatDistance(token, count, options) {\n  var result;\n  var tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === 'string') {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace('{{count}}', String(count));\n  }\n  if (options !== null && options !== void 0 && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return result + ' dan keyin';\n    } else {\n      return result + ' oldin';\n    }\n  }\n  return result;\n};\nexport default formatDistance;", "map": {"version": 3, "names": ["formatDistanceLocale", "lessThanXSeconds", "one", "other", "xSeconds", "halfAMinute", "lessThanXMinutes", "xMinutes", "aboutXHours", "xHours", "xDays", "aboutXWeeks", "xWeeks", "aboutXMonths", "xMonths", "aboutXYears", "xYears", "overXYears", "almostXYears", "formatDistance", "token", "count", "options", "result", "tokenValue", "replace", "String", "addSuffix", "comparison"], "sources": ["C:/Users/<USER>/PycharmProjects/智能数据分析系统/chatdb/frontend/node_modules/date-fns/esm/locale/uz/_lib/formatDistance/index.js"], "sourcesContent": ["var formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: 'sekunddan kam',\n    other: '{{count}} sekunddan kam'\n  },\n  xSeconds: {\n    one: '1 sekund',\n    other: '{{count}} sekund'\n  },\n  halfAMinute: 'yarim minut',\n  lessThanXMinutes: {\n    one: 'bir minutdan kam',\n    other: '{{count}} minutdan kam'\n  },\n  xMinutes: {\n    one: '1 minut',\n    other: '{{count}} minut'\n  },\n  aboutXHours: {\n    one: 'tahminan 1 soat',\n    other: 'tahminan {{count}} soat'\n  },\n  xHours: {\n    one: '1 soat',\n    other: '{{count}} soat'\n  },\n  xDays: {\n    one: '1 kun',\n    other: '{{count}} kun'\n  },\n  aboutXWeeks: {\n    one: 'tahminan 1 hafta',\n    other: 'tahminan {{count}} hafta'\n  },\n  xWeeks: {\n    one: '1 hafta',\n    other: '{{count}} hafta'\n  },\n  aboutXMonths: {\n    one: 'tahminan 1 oy',\n    other: 'tahminan {{count}} oy'\n  },\n  xMonths: {\n    one: '1 oy',\n    other: '{{count}} oy'\n  },\n  aboutXYears: {\n    one: 'tahminan 1 yil',\n    other: 'tahminan {{count}} yil'\n  },\n  xYears: {\n    one: '1 yil',\n    other: '{{count}} yil'\n  },\n  overXYears: {\n    one: \"1 yildan ko'p\",\n    other: \"{{count}} yildan ko'p\"\n  },\n  almostXYears: {\n    one: 'deyarli 1 yil',\n    other: 'deyarli {{count}} yil'\n  }\n};\nvar formatDistance = function formatDistance(token, count, options) {\n  var result;\n  var tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === 'string') {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace('{{count}}', String(count));\n  }\n  if (options !== null && options !== void 0 && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return result + ' dan keyin';\n    } else {\n      return result + ' oldin';\n    }\n  }\n  return result;\n};\nexport default formatDistance;"], "mappings": "AAAA,IAAIA,oBAAoB,GAAG;EACzBC,gBAAgB,EAAE;IAChBC,GAAG,EAAE,eAAe;IACpBC,KAAK,EAAE;EACT,CAAC;EACDC,QAAQ,EAAE;IACRF,GAAG,EAAE,UAAU;IACfC,KAAK,EAAE;EACT,CAAC;EACDE,WAAW,EAAE,aAAa;EAC1BC,gBAAgB,EAAE;IAChBJ,GAAG,EAAE,kBAAkB;IACvBC,KAAK,EAAE;EACT,CAAC;EACDI,QAAQ,EAAE;IACRL,GAAG,EAAE,SAAS;IACdC,KAAK,EAAE;EACT,CAAC;EACDK,WAAW,EAAE;IACXN,GAAG,EAAE,iBAAiB;IACtBC,KAAK,EAAE;EACT,CAAC;EACDM,MAAM,EAAE;IACNP,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE;EACT,CAAC;EACDO,KAAK,EAAE;IACLR,GAAG,EAAE,OAAO;IACZC,KAAK,EAAE;EACT,CAAC;EACDQ,WAAW,EAAE;IACXT,GAAG,EAAE,kBAAkB;IACvBC,KAAK,EAAE;EACT,CAAC;EACDS,MAAM,EAAE;IACNV,GAAG,EAAE,SAAS;IACdC,KAAK,EAAE;EACT,CAAC;EACDU,YAAY,EAAE;IACZX,GAAG,EAAE,eAAe;IACpBC,KAAK,EAAE;EACT,CAAC;EACDW,OAAO,EAAE;IACPZ,GAAG,EAAE,MAAM;IACXC,KAAK,EAAE;EACT,CAAC;EACDY,WAAW,EAAE;IACXb,GAAG,EAAE,gBAAgB;IACrBC,KAAK,EAAE;EACT,CAAC;EACDa,MAAM,EAAE;IACNd,GAAG,EAAE,OAAO;IACZC,KAAK,EAAE;EACT,CAAC;EACDc,UAAU,EAAE;IACVf,GAAG,EAAE,eAAe;IACpBC,KAAK,EAAE;EACT,CAAC;EACDe,YAAY,EAAE;IACZhB,GAAG,EAAE,eAAe;IACpBC,KAAK,EAAE;EACT;AACF,CAAC;AACD,IAAIgB,cAAc,GAAG,SAASA,cAAcA,CAACC,KAAK,EAAEC,KAAK,EAAEC,OAAO,EAAE;EAClE,IAAIC,MAAM;EACV,IAAIC,UAAU,GAAGxB,oBAAoB,CAACoB,KAAK,CAAC;EAC5C,IAAI,OAAOI,UAAU,KAAK,QAAQ,EAAE;IAClCD,MAAM,GAAGC,UAAU;EACrB,CAAC,MAAM,IAAIH,KAAK,KAAK,CAAC,EAAE;IACtBE,MAAM,GAAGC,UAAU,CAACtB,GAAG;EACzB,CAAC,MAAM;IACLqB,MAAM,GAAGC,UAAU,CAACrB,KAAK,CAACsB,OAAO,CAAC,WAAW,EAAEC,MAAM,CAACL,KAAK,CAAC,CAAC;EAC/D;EACA,IAAIC,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,IAAIA,OAAO,CAACK,SAAS,EAAE;IAC/D,IAAIL,OAAO,CAACM,UAAU,IAAIN,OAAO,CAACM,UAAU,GAAG,CAAC,EAAE;MAChD,OAAOL,MAAM,GAAG,YAAY;IAC9B,CAAC,MAAM;MACL,OAAOA,MAAM,GAAG,QAAQ;IAC1B;EACF;EACA,OAAOA,MAAM;AACf,CAAC;AACD,eAAeJ,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}