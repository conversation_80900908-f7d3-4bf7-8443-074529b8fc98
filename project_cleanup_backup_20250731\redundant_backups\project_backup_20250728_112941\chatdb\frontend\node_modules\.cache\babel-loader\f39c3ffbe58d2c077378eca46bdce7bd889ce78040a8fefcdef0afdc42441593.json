{"ast": null, "code": "/** converting camel-cased strings to be lowercase and link it with Separator */\nexport function toLowercaseSeparator(key) {\n  return key.replace(/([A-Z])/g, '-$1').toLowerCase();\n}\nexport function getStyleStr(style) {\n  return Object.keys(style).map(key => `${toLowercaseSeparator(key)}: ${style[key]};`).join(' ');\n}\n/** Returns the ratio of the device's physical pixel resolution to the css pixel resolution */\nexport function getPixelRatio() {\n  return window.devicePixelRatio || 1;\n}\n/** Whether to re-render the watermark */\nexport const reRendering = (mutation, isWatermarkEle) => {\n  let flag = false;\n  // Whether to delete the watermark node\n  if (mutation.removedNodes.length) {\n    flag = Array.from(mutation.removedNodes).some(node => isWatermarkEle(node));\n  }\n  // Whether the watermark dom property value has been modified\n  if (mutation.type === 'attributes' && isWatermarkEle(mutation.target)) {\n    flag = true;\n  }\n  return flag;\n};", "map": {"version": 3, "names": ["toLowercaseSeparator", "key", "replace", "toLowerCase", "getStyleStr", "style", "Object", "keys", "map", "join", "getPixelRatio", "window", "devicePixelRatio", "reRendering", "mutation", "isWatermarkEle", "flag", "removedNodes", "length", "Array", "from", "some", "node", "type", "target"], "sources": ["C:/Users/<USER>/PycharmProjects/智能数据分析系统/chatdb/frontend/node_modules/antd/es/watermark/utils.js"], "sourcesContent": ["/** converting camel-cased strings to be lowercase and link it with Separator */\nexport function toLowercaseSeparator(key) {\n  return key.replace(/([A-Z])/g, '-$1').toLowerCase();\n}\nexport function getStyleStr(style) {\n  return Object.keys(style).map(key => `${toLowercaseSeparator(key)}: ${style[key]};`).join(' ');\n}\n/** Returns the ratio of the device's physical pixel resolution to the css pixel resolution */\nexport function getPixelRatio() {\n  return window.devicePixelRatio || 1;\n}\n/** Whether to re-render the watermark */\nexport const reRendering = (mutation, isWatermarkEle) => {\n  let flag = false;\n  // Whether to delete the watermark node\n  if (mutation.removedNodes.length) {\n    flag = Array.from(mutation.removedNodes).some(node => isWatermarkEle(node));\n  }\n  // Whether the watermark dom property value has been modified\n  if (mutation.type === 'attributes' && isWatermarkEle(mutation.target)) {\n    flag = true;\n  }\n  return flag;\n};"], "mappings": "AAAA;AACA,OAAO,SAASA,oBAAoBA,CAACC,GAAG,EAAE;EACxC,OAAOA,GAAG,CAACC,OAAO,CAAC,UAAU,EAAE,KAAK,CAAC,CAACC,WAAW,CAAC,CAAC;AACrD;AACA,OAAO,SAASC,WAAWA,CAACC,KAAK,EAAE;EACjC,OAAOC,MAAM,CAACC,IAAI,CAACF,KAAK,CAAC,CAACG,GAAG,CAACP,GAAG,IAAI,GAAGD,oBAAoB,CAACC,GAAG,CAAC,KAAKI,KAAK,CAACJ,GAAG,CAAC,GAAG,CAAC,CAACQ,IAAI,CAAC,GAAG,CAAC;AAChG;AACA;AACA,OAAO,SAASC,aAAaA,CAAA,EAAG;EAC9B,OAAOC,MAAM,CAACC,gBAAgB,IAAI,CAAC;AACrC;AACA;AACA,OAAO,MAAMC,WAAW,GAAGA,CAACC,QAAQ,EAAEC,cAAc,KAAK;EACvD,IAAIC,IAAI,GAAG,KAAK;EAChB;EACA,IAAIF,QAAQ,CAACG,YAAY,CAACC,MAAM,EAAE;IAChCF,IAAI,GAAGG,KAAK,CAACC,IAAI,CAACN,QAAQ,CAACG,YAAY,CAAC,CAACI,IAAI,CAACC,IAAI,IAAIP,cAAc,CAACO,IAAI,CAAC,CAAC;EAC7E;EACA;EACA,IAAIR,QAAQ,CAACS,IAAI,KAAK,YAAY,IAAIR,cAAc,CAACD,QAAQ,CAACU,MAAM,CAAC,EAAE;IACrER,IAAI,GAAG,IAAI;EACb;EACA,OAAOA,IAAI;AACb,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}