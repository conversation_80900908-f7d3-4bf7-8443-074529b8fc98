#!/usr/bin/env python3
"""
调试字段映射优先级问题
检查系统当前状态和配置
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import sqlite3
try:
    from app.services.text2sql_utils import (
        SEMANTIC_FIELD_GROUPS,
        select_preferred_field,
        apply_field_priority_corrections,
        process_sql_with_value_mappings
    )
    from app.services.enhanced_prompt_service import EnhancedPromptService
    from app.core.config import settings
except ImportError as e:
    print(f"导入模块失败: {e}")
    print("请确保在正确的环境中运行此脚本")
    sys.exit(1)

def check_database_mappings():
    """检查数据库中的字段映射配置"""
    print("🔍 检查数据库中的字段映射配置")
    print("=" * 60)
    
    try:
        # 连接到数据库
        conn = sqlite3.connect(settings.SQLITE_DB_PATH)
        cursor = conn.cursor()
        
        # 检查valuemapping表中的映射
        print("📋 当前数据库中的字段映射:")
        
        # 查询公司相关映射
        cursor.execute('''
            SELECT vm.nl_term, vm.db_value, sc.column_name, st.table_name
            FROM valuemapping vm
            JOIN schemacolumn sc ON vm.column_id = sc.id
            JOIN schematable st ON sc.table_id = st.id
            WHERE st.table_name = 'financial_data' 
            AND sc.column_name IN ('accounting_unit_name', 'accounting_organization')
            ORDER BY sc.column_name, vm.nl_term
        ''')
        
        company_mappings = cursor.fetchall()
        if company_mappings:
            print("\n🏢 公司相关字段映射:")
            for nl_term, db_value, column_name, table_name in company_mappings:
                print(f"  '{nl_term}' → {column_name} (值: {db_value})")
        else:
            print("\n❌ 未找到公司相关字段映射")
        
        # 查询科目相关映射
        cursor.execute('''
            SELECT vm.nl_term, vm.db_value, sc.column_name, st.table_name
            FROM valuemapping vm
            JOIN schemacolumn sc ON vm.column_id = sc.id
            JOIN schematable st ON sc.table_id = st.id
            WHERE st.table_name = 'financial_data' 
            AND sc.column_name IN ('account_full_name', 'account_name')
            ORDER BY sc.column_name, vm.nl_term
        ''')
        
        account_mappings = cursor.fetchall()
        if account_mappings:
            print("\n📊 科目相关字段映射:")
            for nl_term, db_value, column_name, table_name in account_mappings:
                print(f"  '{nl_term}' → {column_name} (值: {db_value})")
        else:
            print("\n❌ 未找到科目相关字段映射")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 检查数据库映射失败: {e}")

def check_semantic_groups():
    """检查语义分组配置"""
    print("\n🧠 检查语义分组配置")
    print("=" * 60)
    
    print("当前语义分组配置:")
    for group_name, config in SEMANTIC_FIELD_GROUPS.items():
        print(f"\n🔸 {group_name}:")
        print(f"   首选字段: {config['preferred_field']}")
        print(f"   备选字段: {config['alternative_fields']}")
        print(f"   自然语言术语: {config['natural_language_terms']}")

def test_field_selection():
    """测试字段选择逻辑"""
    print("\n🎯 测试字段选择逻辑")
    print("=" * 60)
    
    test_cases = [
        {
            "term": "公司",
            "fields": ["accounting_unit_name", "accounting_organization"],
            "expected": "accounting_unit_name"
        },
        {
            "term": "科目名称",
            "fields": ["account_full_name", "account_name"],
            "expected": "account_full_name"
        }
    ]
    
    for case in test_cases:
        result = select_preferred_field(case["term"], case["fields"])
        status = "✅" if result == case["expected"] else "❌"
        print(f"术语: '{case['term']}' → 选择: '{result}' (期望: '{case['expected']}') {status}")

def simulate_value_mappings_processing():
    """模拟值映射处理过程"""
    print("\n🔄 模拟值映射处理过程")
    print("=" * 60)

    try:
        # 模拟值映射数据（基于数据库中可能存在的映射）
        mock_value_mappings = {
            "financial_data.accounting_unit_name": {
                "公司": "accounting_unit_name",
                "企业": "accounting_unit_name"
            },
            "financial_data.accounting_organization": {
                "公司": "accounting_organization",
                "企业": "accounting_organization"
            },
            "financial_data.account_full_name": {
                "科目名称": "account_full_name",
                "会计科目名称": "account_full_name"
            },
            "financial_data.account_name": {
                "科目名称": "account_name",
                "科目": "account_name"
            }
        }

        print("📋 模拟的值映射数据:")
        for table_col, mappings in mock_value_mappings.items():
            print(f"\n🔸 {table_col}:")
            for nl_term, db_value in mappings.items():
                print(f"   '{nl_term}' → '{db_value}'")

        # 测试SQL处理
        test_sql = "SELECT accounting_organization, account_name FROM financial_data WHERE account_name LIKE '%管理费用%'"
        print(f"\n🧪 测试SQL处理:")
        print(f"原始SQL: {test_sql}")

        processed_sql = process_sql_with_value_mappings(test_sql, mock_value_mappings)
        print(f"处理后SQL: {processed_sql}")

        # 检查是否使用了优先字段
        uses_preferred_company = "accounting_unit_name" in processed_sql
        uses_preferred_account = "account_full_name" in processed_sql

        print(f"\n📊 字段使用检查:")
        print(f"使用优先公司字段 (accounting_unit_name): {'✅' if uses_preferred_company else '❌'}")
        print(f"使用优先科目字段 (account_full_name): {'✅' if uses_preferred_account else '❌'}")

    except Exception as e:
        print(f"❌ 模拟值映射处理失败: {e}")
        import traceback
        traceback.print_exc()

def check_enhanced_prompt_service():
    """检查增强提示服务"""
    print("\n🎨 检查增强提示服务")
    print("=" * 60)
    
    try:
        prompt_service = EnhancedPromptService()
        
        # 测试字段映射指导
        guidance = prompt_service.get_field_mapping_guidance()
        
        print("📝 字段映射指导内容:")
        print(guidance[:500] + "..." if len(guidance) > 500 else guidance)
        
        # 检查关键内容
        key_checks = [
            ("包含accounting_unit_name优先级", "accounting_unit_name" in guidance),
            ("包含account_full_name优先级", "account_full_name" in guidance),
            ("包含优先级规则", "优先级" in guidance or "首选" in guidance),
            ("包含禁用字段警告", "禁止" in guidance or "禁用" in guidance)
        ]
        
        print("\n📋 关键内容检查:")
        for check_name, result in key_checks:
            status = "✅" if result else "❌"
            print(f"  {check_name}: {status}")
            
    except Exception as e:
        print(f"❌ 检查增强提示服务失败: {e}")

def check_cache_status():
    """检查缓存状态"""
    print("\n💾 检查缓存状态")
    print("=" * 60)
    
    print("缓存配置信息:")
    print(f"- 值映射缓存TTL: 3600秒 (1小时)")
    print(f"- Schema上下文缓存TTL: 3600秒 (1小时)")
    print(f"- 财务元数据缓存TTL: 7200秒 (2小时)")
    
    print("\n⚠️ 缓存可能导致的问题:")
    print("1. 如果数据库中的映射已更新，但缓存未过期，系统仍使用旧映射")
    print("2. 代码修改后，缓存的函数结果可能不会立即生效")
    print("3. 需要等待缓存过期或手动清除缓存")

def provide_solutions():
    """提供解决方案"""
    print("\n🔧 解决方案建议")
    print("=" * 60)
    
    print("立即修复步骤:")
    print("1. 清除缓存 - 重启后端服务")
    print("2. 检查数据库映射 - 确保映射数据正确")
    print("3. 验证代码加载 - 确保修改的代码被正确加载")
    print("4. 测试端到端流程 - 验证整个流程")
    
    print("\n具体操作:")
    print("```bash")
    print("# 1. 重启后端服务清除缓存")
    print("cd chatdb/backend")
    print("# 停止当前服务，然后重新启动")
    print("")
    print("# 2. 运行调试脚本")
    print("python debug_field_mapping_issue.py")
    print("")
    print("# 3. 测试字段映射修复")
    print("python test_field_mapping_fix.py")
    print("```")

def main():
    """主函数"""
    print("🚀 字段映射优先级问题调试")
    print("=" * 80)
    
    try:
        check_semantic_groups()
        test_field_selection()
        check_database_mappings()
        simulate_value_mappings_processing()
        check_enhanced_prompt_service()
        check_cache_status()
        provide_solutions()
        
        print("\n" + "=" * 80)
        print("✅ 调试检查完成！")
        print("\n💡 如果发现问题，请按照解决方案建议进行修复。")
        
    except Exception as e:
        print(f"\n❌ 调试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
