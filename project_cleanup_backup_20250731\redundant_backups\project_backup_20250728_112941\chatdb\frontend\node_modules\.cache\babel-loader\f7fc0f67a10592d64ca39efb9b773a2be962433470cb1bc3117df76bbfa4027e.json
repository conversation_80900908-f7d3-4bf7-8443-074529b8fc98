{"ast": null, "code": "import { defaultPrefixCls } from '../../config-provider';\nexport const TARGET_CLS = `${defaultPrefixCls}-wave-target`;", "map": {"version": 3, "names": ["defaultPrefixCls", "TARGET_CLS"], "sources": ["C:/Users/<USER>/PycharmProjects/智能数据分析系统/chatdb/frontend/node_modules/antd/es/_util/wave/interface.js"], "sourcesContent": ["import { defaultPrefixCls } from '../../config-provider';\nexport const TARGET_CLS = `${defaultPrefixCls}-wave-target`;"], "mappings": "AAAA,SAASA,gBAAgB,QAAQ,uBAAuB;AACxD,OAAO,MAAMC,UAAU,GAAG,GAAGD,gBAAgB,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}