#!/usr/bin/env python3
"""
撤销错误的管理费用映射配置
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def remove_incorrect_mappings():
    """删除错误的管理费用映射"""
    print("🗑️ 删除错误的管理费用映射")
    print("=" * 60)
    
    try:
        from app.db.session import SessionLocal
        from sqlalchemy import text
        
        db = SessionLocal()
        
        # 要删除的错误映射
        incorrect_mappings = [
            '管理费用查询',
            '管理费用分析', 
            '管理费用统计',
            '管理费用明细',
            '管理费用汇总'
        ]
        
        deleted_count = 0
        
        for nl_term in incorrect_mappings:
            result = db.execute(text("""
                DELETE FROM valuemapping 
                WHERE nl_term = :nl_term
            """), {"nl_term": nl_term})
            
            if result.rowcount > 0:
                print(f"✅ 删除映射: '{nl_term}'")
                deleted_count += result.rowcount
            else:
                print(f"⚠️ 映射不存在: '{nl_term}'")
        
        # 提交更改
        db.commit()
        print(f"\n💾 成功删除 {deleted_count} 个错误映射")
        
        db.close()
        return True
        
    except Exception as e:
        print(f"❌ 删除错误映射失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 撤销错误的映射配置")
    print("=" * 80)
    
    success = remove_incorrect_mappings()
    
    if success:
        print("\n✅ 错误映射已撤销")
    else:
        print("\n❌ 撤销失败")

if __name__ == "__main__":
    main()
