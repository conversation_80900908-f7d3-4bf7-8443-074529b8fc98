"""
系统健康检查端点
提供全面的系统状态监控和诊断信息
"""
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
import psutil
import time
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, List

from app.api import deps
from app.services.monitoring_service import monitoring_service, perform_health_checks
from app.services.cache_service import cache_service
from app.services.performance_optimizer import performance_optimizer
from app.core.config import settings
from app.db.session import engine

router = APIRouter()
logger = logging.getLogger(__name__)

# 系统启动时间
SYSTEM_START_TIME = time.time()

@router.get("/")
async def basic_health_check():
    """基础健康检查"""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "uptime": time.time() - SYSTEM_START_TIME,
        "version": "1.0.0"
    }

@router.get("/detailed")
async def detailed_health_check(db: Session = Depends(deps.get_db)):
    """详细健康检查"""
    try:
        # 执行所有健康检查
        await perform_health_checks()
        
        # 收集系统信息
        system_info = _get_system_info()
        
        # 收集数据库信息
        database_info = _get_database_info(db)
        
        # 收集缓存信息
        cache_info = cache_service.get_stats()
        
        # 收集性能信息
        performance_info = performance_optimizer.get_performance_stats()
        
        # 收集监控信息
        monitoring_info = monitoring_service.get_system_metrics()
        
        # 健康状态
        health_status = monitoring_service.get_health_status()
        
        return {
            "status": "healthy" if health_status["overall_healthy"] else "unhealthy",
            "timestamp": datetime.now().isoformat(),
            "uptime": time.time() - SYSTEM_START_TIME,
            "system": system_info,
            "database": database_info,
            "cache": cache_info,
            "performance": performance_info,
            "monitoring": monitoring_info,
            "health_checks": health_status,
            "configuration": _get_configuration_info()
        }
        
    except Exception as e:
        logger.error(f"健康检查失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"健康检查失败: {str(e)}")

@router.get("/performance")
async def performance_health_check():
    """性能健康检查"""
    try:
        performance_health = performance_optimizer.check_performance_health()
        performance_stats = performance_optimizer.get_performance_stats()
        
        return {
            "status": "healthy" if performance_health["overall_healthy"] else "degraded",
            "timestamp": datetime.now().isoformat(),
            "health_check": performance_health,
            "statistics": performance_stats,
            "recommendations": performance_health.get("recommendations", [])
        }
        
    except Exception as e:
        logger.error(f"性能健康检查失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"性能健康检查失败: {str(e)}")

@router.get("/database")
async def database_health_check(db: Session = Depends(deps.get_db)):
    """数据库健康检查"""
    try:
        start_time = time.time()
        
        # 测试数据库连接
        db.execute("SELECT 1")
        
        connection_time = time.time() - start_time
        
        # 获取数据库信息
        database_info = _get_database_info(db)
        
        return {
            "status": "healthy",
            "timestamp": datetime.now().isoformat(),
            "connection_time": connection_time,
            "database_info": database_info
        }
        
    except Exception as e:
        logger.error(f"数据库健康检查失败: {str(e)}")
        return {
            "status": "unhealthy",
            "timestamp": datetime.now().isoformat(),
            "error": str(e)
        }

@router.get("/cache")
async def cache_health_check():
    """缓存健康检查"""
    try:
        # 测试缓存读写
        test_key = "health_check_test"
        test_value = f"test_{int(time.time())}"
        
        cache_service.set(test_key, test_value, ttl=10)
        retrieved_value = cache_service.get(test_key)
        cache_service.delete(test_key)
        
        cache_working = retrieved_value == test_value
        cache_stats = cache_service.get_stats()
        
        return {
            "status": "healthy" if cache_working else "unhealthy",
            "timestamp": datetime.now().isoformat(),
            "cache_working": cache_working,
            "statistics": cache_stats
        }
        
    except Exception as e:
        logger.error(f"缓存健康检查失败: {str(e)}")
        return {
            "status": "unhealthy",
            "timestamp": datetime.now().isoformat(),
            "error": str(e)
        }

@router.get("/metrics")
async def get_system_metrics():
    """获取系统指标"""
    try:
        return {
            "timestamp": datetime.now().isoformat(),
            "system_metrics": monitoring_service.get_system_metrics(),
            "performance_metrics": performance_optimizer.get_performance_stats(),
            "cache_metrics": cache_service.get_stats()
        }
        
    except Exception as e:
        logger.error(f"获取系统指标失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取系统指标失败: {str(e)}")

@router.get("/errors")
async def get_recent_errors(limit: int = 50):
    """获取最近的错误记录"""
    try:
        errors = monitoring_service.get_recent_errors(limit)
        return {
            "timestamp": datetime.now().isoformat(),
            "error_count": len(errors),
            "errors": errors
        }
        
    except Exception as e:
        logger.error(f"获取错误记录失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取错误记录失败: {str(e)}")

def _get_system_info() -> Dict[str, Any]:
    """获取系统信息"""
    try:
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')
        
        return {
            "cpu_count": psutil.cpu_count(),
            "cpu_percent": psutil.cpu_percent(interval=1),
            "memory": {
                "total": memory.total,
                "available": memory.available,
                "percent": memory.percent,
                "used": memory.used
            },
            "disk": {
                "total": disk.total,
                "used": disk.used,
                "free": disk.free,
                "percent": (disk.used / disk.total) * 100
            },
            "load_average": psutil.getloadavg() if hasattr(psutil, 'getloadavg') else None,
            "boot_time": psutil.boot_time()
        }
    except Exception as e:
        logger.error(f"获取系统信息失败: {str(e)}")
        return {"error": str(e)}

def _get_database_info(db: Session) -> Dict[str, Any]:
    """获取数据库信息"""
    try:
        # 获取数据库连接池信息
        pool = engine.pool
        
        return {
            "engine_url": str(engine.url).replace(engine.url.password or '', '***'),
            "pool_size": pool.size(),
            "checked_in": pool.checkedin(),
            "checked_out": pool.checkedout(),
            "overflow": pool.overflow(),
            "invalid": pool.invalid(),
            "database_type": settings.DATABASE_TYPE,
            "connection_successful": True
        }
    except Exception as e:
        logger.error(f"获取数据库信息失败: {str(e)}")
        return {
            "connection_successful": False,
            "error": str(e)
        }

def _get_configuration_info() -> Dict[str, Any]:
    """获取配置信息（敏感信息已脱敏）"""
    return {
        "database_type": settings.DATABASE_TYPE,
        "enable_metadata_enhancement": settings.ENABLE_METADATA_ENHANCEMENT,
        "enable_query_cache": settings.ENABLE_QUERY_CACHE,
        "cache_ttl": settings.CACHE_TTL,
        "db_pool_size": settings.DB_POOL_SIZE,
        "default_query_limit": settings.DEFAULT_QUERY_LIMIT,
        "enable_performance_monitoring": settings.ENABLE_PERFORMANCE_MONITORING,
        "slow_query_threshold": settings.SLOW_QUERY_THRESHOLD,
        "memory_warning_threshold": settings.MEMORY_WARNING_THRESHOLD
    }

@router.post("/optimize")
async def trigger_optimization():
    """触发系统优化"""
    try:
        # 执行内存优化
        performance_optimizer.optimize_memory_usage()
        
        # 清理过期缓存
        expired_count = cache_service.cleanup_expired()
        
        return {
            "status": "success",
            "timestamp": datetime.now().isoformat(),
            "message": "系统优化完成",
            "expired_cache_cleaned": expired_count
        }
        
    except Exception as e:
        logger.error(f"系统优化失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"系统优化失败: {str(e)}")

@router.post("/clear-cache")
async def clear_system_cache():
    """清空系统缓存"""
    try:
        cache_service.clear()
        
        return {
            "status": "success",
            "timestamp": datetime.now().isoformat(),
            "message": "系统缓存已清空"
        }
        
    except Exception as e:
        logger.error(f"清空缓存失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"清空缓存失败: {str(e)}")
