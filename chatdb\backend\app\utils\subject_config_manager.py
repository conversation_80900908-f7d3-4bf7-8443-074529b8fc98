"""
科目配置管理工具
提供科目关键词的动态管理功能
"""

import json
import os
from typing import Dict, List, Any
from app.config.subject_keywords import (
    SUBJECT_KEYWORDS, 
    SUBJECT_CATEGORIES,
    add_custom_subject,
    get_subject_by_keyword
)

class SubjectConfigManager:
    """科目配置管理器"""
    
    def __init__(self):
        self.config_file = os.path.join(
            os.path.dirname(__file__), 
            '..', 'config', 'custom_subjects.json'
        )
    
    def load_custom_subjects(self) -> Dict[str, Any]:
        """加载自定义科目配置"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            return {}
        except Exception as e:
            print(f"加载自定义科目配置失败: {e}")
            return {}
    
    def save_custom_subjects(self, config: Dict[str, Any]) -> bool:
        """保存自定义科目配置"""
        try:
            os.makedirs(os.path.dirname(self.config_file), exist_ok=True)
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            print(f"保存自定义科目配置失败: {e}")
            return False
    
    def add_subject_keyword(self, standard_name: str, keywords: List[str], category: str = None) -> bool:
        """
        添加科目关键词
        
        Args:
            standard_name: 标准科目名称
            keywords: 关键词列表
            category: 科目分类
            
        Returns:
            bool: 是否添加成功
        """
        try:
            # 添加到内存中的配置
            add_custom_subject(standard_name, keywords, category)
            
            # 保存到文件
            custom_config = self.load_custom_subjects()
            custom_config[standard_name] = {
                'keywords': keywords,
                'category': category
            }
            
            return self.save_custom_subjects(custom_config)
        except Exception as e:
            print(f"添加科目关键词失败: {e}")
            return False
    
    def remove_subject_keyword(self, standard_name: str) -> bool:
        """
        移除科目关键词
        
        Args:
            standard_name: 标准科目名称
            
        Returns:
            bool: 是否移除成功
        """
        try:
            # 从内存配置中移除
            if standard_name in SUBJECT_KEYWORDS:
                del SUBJECT_KEYWORDS[standard_name]
            
            # 从文件配置中移除
            custom_config = self.load_custom_subjects()
            if standard_name in custom_config:
                del custom_config[standard_name]
                return self.save_custom_subjects(custom_config)
            
            return True
        except Exception as e:
            print(f"移除科目关键词失败: {e}")
            return False
    
    def update_subject_keywords(self, standard_name: str, keywords: List[str]) -> bool:
        """
        更新科目关键词
        
        Args:
            standard_name: 标准科目名称
            keywords: 新的关键词列表
            
        Returns:
            bool: 是否更新成功
        """
        try:
            # 更新内存配置
            SUBJECT_KEYWORDS[standard_name] = keywords
            
            # 更新文件配置
            custom_config = self.load_custom_subjects()
            if standard_name in custom_config:
                custom_config[standard_name]['keywords'] = keywords
            else:
                custom_config[standard_name] = {'keywords': keywords}
            
            return self.save_custom_subjects(custom_config)
        except Exception as e:
            print(f"更新科目关键词失败: {e}")
            return False
    
    def get_all_subjects(self) -> Dict[str, List[str]]:
        """获取所有科目关键词配置"""
        return SUBJECT_KEYWORDS.copy()
    
    def search_subjects_by_keyword(self, keyword: str) -> List[str]:
        """
        根据关键词搜索相关科目
        
        Args:
            keyword: 搜索关键词
            
        Returns:
            List[str]: 匹配的标准科目名称列表
        """
        matches = []
        for standard_name, keywords in SUBJECT_KEYWORDS.items():
            if any(keyword.lower() in kw.lower() for kw in keywords):
                matches.append(standard_name)
        return matches
    
    def validate_subject_config(self) -> Dict[str, Any]:
        """
        验证科目配置的完整性
        
        Returns:
            Dict[str, Any]: 验证结果
        """
        result = {
            'valid': True,
            'errors': [],
            'warnings': [],
            'statistics': {}
        }
        
        try:
            # 统计信息
            total_subjects = len(SUBJECT_KEYWORDS)
            total_keywords = sum(len(keywords) for keywords in SUBJECT_KEYWORDS.values())
            
            result['statistics'] = {
                'total_subjects': total_subjects,
                'total_keywords': total_keywords,
                'avg_keywords_per_subject': round(total_keywords / total_subjects, 2) if total_subjects > 0 else 0
            }
            
            # 检查重复关键词
            all_keywords = []
            for standard_name, keywords in SUBJECT_KEYWORDS.items():
                for keyword in keywords:
                    if keyword in all_keywords:
                        result['warnings'].append(f"重复关键词: '{keyword}' 在多个科目中出现")
                    all_keywords.append(keyword)
            
            # 检查空关键词列表
            for standard_name, keywords in SUBJECT_KEYWORDS.items():
                if not keywords:
                    result['errors'].append(f"科目 '{standard_name}' 没有配置关键词")
                    result['valid'] = False
            
        except Exception as e:
            result['valid'] = False
            result['errors'].append(f"验证过程出错: {e}")
        
        return result
    
    def export_config(self, file_path: str) -> bool:
        """
        导出科目配置到指定文件
        
        Args:
            file_path: 导出文件路径
            
        Returns:
            bool: 是否导出成功
        """
        try:
            config = {
                'subject_keywords': SUBJECT_KEYWORDS,
                'subject_categories': SUBJECT_CATEGORIES,
                'export_time': str(datetime.now()),
                'version': '1.0'
            }
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
            
            return True
        except Exception as e:
            print(f"导出配置失败: {e}")
            return False
    
    def import_config(self, file_path: str) -> bool:
        """
        从指定文件导入科目配置
        
        Args:
            file_path: 导入文件路径
            
        Returns:
            bool: 是否导入成功
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            if 'subject_keywords' in config:
                SUBJECT_KEYWORDS.update(config['subject_keywords'])
            
            if 'subject_categories' in config:
                SUBJECT_CATEGORIES.update(config['subject_categories'])
            
            return True
        except Exception as e:
            print(f"导入配置失败: {e}")
            return False

# 全局配置管理器实例
subject_config_manager = SubjectConfigManager()
