{"ast": null, "code": "\"use client\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport React from 'react';\nimport CloseOutlined from \"@ant-design/icons/es/icons/CloseOutlined\";\nimport FileTextOutlined from \"@ant-design/icons/es/icons/FileTextOutlined\";\nimport classNames from 'classnames';\nimport CSSMotion from 'rc-motion';\nimport useEvent from \"rc-util/es/hooks/useEvent\";\nimport useMergedState from \"rc-util/es/hooks/useMergedState\";\nimport { useZIndex } from '../_util/hooks/useZIndex';\nimport { devUseWarning } from '../_util/warning';\nimport { useComponentConfig } from '../config-provider/context';\nimport useCSSVarCls from '../config-provider/hooks/useCSSVarCls';\nimport { FloatButtonGroupProvider } from './context';\nimport FloatButton, { floatButtonPrefixCls } from './FloatButton';\nimport useStyle from './style';\nconst FloatButtonGroup = props => {\n  var _a;\n  const {\n      prefixCls: customizePrefixCls,\n      className,\n      style,\n      shape = 'circle',\n      type = 'default',\n      placement = 'top',\n      icon = /*#__PURE__*/React.createElement(FileTextOutlined, null),\n      closeIcon,\n      description,\n      trigger,\n      children,\n      onOpenChange,\n      open: customOpen,\n      onClick: onTriggerButtonClick\n    } = props,\n    floatButtonProps = __rest(props, [\"prefixCls\", \"className\", \"style\", \"shape\", \"type\", \"placement\", \"icon\", \"closeIcon\", \"description\", \"trigger\", \"children\", \"onOpenChange\", \"open\", \"onClick\"]);\n  const {\n    direction,\n    getPrefixCls,\n    closeIcon: contextCloseIcon\n  } = useComponentConfig('floatButtonGroup');\n  const mergedCloseIcon = (_a = closeIcon !== null && closeIcon !== void 0 ? closeIcon : contextCloseIcon) !== null && _a !== void 0 ? _a : /*#__PURE__*/React.createElement(CloseOutlined, null);\n  const prefixCls = getPrefixCls(floatButtonPrefixCls, customizePrefixCls);\n  const rootCls = useCSSVarCls(prefixCls);\n  const [wrapCSSVar, hashId, cssVarCls] = useStyle(prefixCls, rootCls);\n  const groupPrefixCls = `${prefixCls}-group`;\n  const isMenuMode = trigger && ['click', 'hover'].includes(trigger);\n  const isValidPlacement = placement && ['top', 'left', 'right', 'bottom'].includes(placement);\n  const groupCls = classNames(groupPrefixCls, hashId, cssVarCls, rootCls, className, {\n    [`${groupPrefixCls}-rtl`]: direction === 'rtl',\n    [`${groupPrefixCls}-${shape}`]: shape,\n    [`${groupPrefixCls}-${shape}-shadow`]: !isMenuMode,\n    [`${groupPrefixCls}-${placement}`]: isMenuMode && isValidPlacement // 只有菜单模式才支持弹出方向\n  });\n  // ============================ zIndex ============================\n  const [zIndex] = useZIndex('FloatButton', style === null || style === void 0 ? void 0 : style.zIndex);\n  const mergedStyle = Object.assign(Object.assign({}, style), {\n    zIndex\n  });\n  const wrapperCls = classNames(hashId, `${groupPrefixCls}-wrap`);\n  const [open, setOpen] = useMergedState(false, {\n    value: customOpen\n  });\n  const floatButtonGroupRef = React.useRef(null);\n  // ========================== Open ==========================\n  const hoverTrigger = trigger === 'hover';\n  const clickTrigger = trigger === 'click';\n  const triggerOpen = useEvent(nextOpen => {\n    if (open !== nextOpen) {\n      setOpen(nextOpen);\n      onOpenChange === null || onOpenChange === void 0 ? void 0 : onOpenChange(nextOpen);\n    }\n  });\n  // ===================== Trigger: Hover =====================\n  const onMouseEnter = () => {\n    if (hoverTrigger) {\n      triggerOpen(true);\n    }\n  };\n  const onMouseLeave = () => {\n    if (hoverTrigger) {\n      triggerOpen(false);\n    }\n  };\n  // ===================== Trigger: Click =====================\n  const onInternalTriggerButtonClick = e => {\n    if (clickTrigger) {\n      triggerOpen(!open);\n    }\n    onTriggerButtonClick === null || onTriggerButtonClick === void 0 ? void 0 : onTriggerButtonClick(e);\n  };\n  React.useEffect(() => {\n    if (clickTrigger) {\n      const onDocClick = e => {\n        var _a;\n        // Skip if click on the group\n        if ((_a = floatButtonGroupRef.current) === null || _a === void 0 ? void 0 : _a.contains(e.target)) {\n          return;\n        }\n        triggerOpen(false);\n      };\n      document.addEventListener('click', onDocClick, {\n        capture: true\n      });\n      return () => document.removeEventListener('click', onDocClick, {\n        capture: true\n      });\n    }\n  }, [clickTrigger]);\n  // ======================== Warning =========================\n  if (process.env.NODE_ENV !== 'production') {\n    const warning = devUseWarning('FloatButton.Group');\n    process.env.NODE_ENV !== \"production\" ? warning(!('open' in props) || !!trigger, 'usage', '`open` need to be used together with `trigger`') : void 0;\n  }\n  // ========================= Render =========================\n  return wrapCSSVar(/*#__PURE__*/React.createElement(FloatButtonGroupProvider, {\n    value: shape\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    ref: floatButtonGroupRef,\n    className: groupCls,\n    style: mergedStyle,\n    // Hover trigger\n    onMouseEnter: onMouseEnter,\n    onMouseLeave: onMouseLeave\n  }, isMenuMode ? (/*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(CSSMotion, {\n    visible: open,\n    motionName: `${groupPrefixCls}-wrap`\n  }, _ref => {\n    let {\n      className: motionClassName\n    } = _ref;\n    return /*#__PURE__*/React.createElement(\"div\", {\n      className: classNames(motionClassName, wrapperCls)\n    }, children);\n  }), /*#__PURE__*/React.createElement(FloatButton, Object.assign({\n    type: type,\n    icon: open ? mergedCloseIcon : icon,\n    description: description,\n    \"aria-label\": props['aria-label'],\n    className: `${groupPrefixCls}-trigger`,\n    onClick: onInternalTriggerButtonClick\n  }, floatButtonProps)))) : children)));\n};\nexport default FloatButtonGroup;", "map": {"version": 3, "names": ["__rest", "s", "e", "t", "p", "Object", "prototype", "hasOwnProperty", "call", "indexOf", "getOwnPropertySymbols", "i", "length", "propertyIsEnumerable", "React", "CloseOutlined", "FileTextOutlined", "classNames", "CSSMotion", "useEvent", "useMergedState", "useZIndex", "devUseW<PERSON>ning", "useComponentConfig", "useCSSVarCls", "FloatButtonGroupProvider", "FloatButton", "floatButtonPrefixCls", "useStyle", "FloatButtonGroup", "props", "_a", "prefixCls", "customizePrefixCls", "className", "style", "shape", "type", "placement", "icon", "createElement", "closeIcon", "description", "trigger", "children", "onOpenChange", "open", "customOpen", "onClick", "onTriggerButtonClick", "floatButtonProps", "direction", "getPrefixCls", "contextCloseIcon", "mergedCloseIcon", "rootCls", "wrapCSSVar", "hashId", "cssVarCls", "groupPrefixCls", "isMenuMode", "includes", "isValidPlacement", "groupCls", "zIndex", "mergedStyle", "assign", "wrapperCls", "<PERSON><PERSON><PERSON>", "value", "floatButtonGroupRef", "useRef", "hoverTrigger", "clickTrigger", "triggerOpen", "nextOpen", "onMouseEnter", "onMouseLeave", "onInternalTriggerButtonClick", "useEffect", "onDocClick", "current", "contains", "target", "document", "addEventListener", "capture", "removeEventListener", "process", "env", "NODE_ENV", "warning", "ref", "Fragment", "visible", "motionName", "_ref", "motionClassName"], "sources": ["C:/Users/<USER>/PycharmProjects/智能数据分析系统/chatdb/frontend/node_modules/antd/es/float-button/FloatButtonGroup.js"], "sourcesContent": ["\"use client\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport React from 'react';\nimport CloseOutlined from \"@ant-design/icons/es/icons/CloseOutlined\";\nimport FileTextOutlined from \"@ant-design/icons/es/icons/FileTextOutlined\";\nimport classNames from 'classnames';\nimport CSSMotion from 'rc-motion';\nimport useEvent from \"rc-util/es/hooks/useEvent\";\nimport useMergedState from \"rc-util/es/hooks/useMergedState\";\nimport { useZIndex } from '../_util/hooks/useZIndex';\nimport { devUseWarning } from '../_util/warning';\nimport { useComponentConfig } from '../config-provider/context';\nimport useCSSVarCls from '../config-provider/hooks/useCSSVarCls';\nimport { FloatButtonGroupProvider } from './context';\nimport FloatButton, { floatButtonPrefixCls } from './FloatButton';\nimport useStyle from './style';\nconst FloatButtonGroup = props => {\n  var _a;\n  const {\n      prefixCls: customizePrefixCls,\n      className,\n      style,\n      shape = 'circle',\n      type = 'default',\n      placement = 'top',\n      icon = /*#__PURE__*/React.createElement(FileTextOutlined, null),\n      closeIcon,\n      description,\n      trigger,\n      children,\n      onOpenChange,\n      open: customOpen,\n      onClick: onTriggerButtonClick\n    } = props,\n    floatButtonProps = __rest(props, [\"prefixCls\", \"className\", \"style\", \"shape\", \"type\", \"placement\", \"icon\", \"closeIcon\", \"description\", \"trigger\", \"children\", \"onOpenChange\", \"open\", \"onClick\"]);\n  const {\n    direction,\n    getPrefixCls,\n    closeIcon: contextCloseIcon\n  } = useComponentConfig('floatButtonGroup');\n  const mergedCloseIcon = (_a = closeIcon !== null && closeIcon !== void 0 ? closeIcon : contextCloseIcon) !== null && _a !== void 0 ? _a : /*#__PURE__*/React.createElement(CloseOutlined, null);\n  const prefixCls = getPrefixCls(floatButtonPrefixCls, customizePrefixCls);\n  const rootCls = useCSSVarCls(prefixCls);\n  const [wrapCSSVar, hashId, cssVarCls] = useStyle(prefixCls, rootCls);\n  const groupPrefixCls = `${prefixCls}-group`;\n  const isMenuMode = trigger && ['click', 'hover'].includes(trigger);\n  const isValidPlacement = placement && ['top', 'left', 'right', 'bottom'].includes(placement);\n  const groupCls = classNames(groupPrefixCls, hashId, cssVarCls, rootCls, className, {\n    [`${groupPrefixCls}-rtl`]: direction === 'rtl',\n    [`${groupPrefixCls}-${shape}`]: shape,\n    [`${groupPrefixCls}-${shape}-shadow`]: !isMenuMode,\n    [`${groupPrefixCls}-${placement}`]: isMenuMode && isValidPlacement // 只有菜单模式才支持弹出方向\n  });\n  // ============================ zIndex ============================\n  const [zIndex] = useZIndex('FloatButton', style === null || style === void 0 ? void 0 : style.zIndex);\n  const mergedStyle = Object.assign(Object.assign({}, style), {\n    zIndex\n  });\n  const wrapperCls = classNames(hashId, `${groupPrefixCls}-wrap`);\n  const [open, setOpen] = useMergedState(false, {\n    value: customOpen\n  });\n  const floatButtonGroupRef = React.useRef(null);\n  // ========================== Open ==========================\n  const hoverTrigger = trigger === 'hover';\n  const clickTrigger = trigger === 'click';\n  const triggerOpen = useEvent(nextOpen => {\n    if (open !== nextOpen) {\n      setOpen(nextOpen);\n      onOpenChange === null || onOpenChange === void 0 ? void 0 : onOpenChange(nextOpen);\n    }\n  });\n  // ===================== Trigger: Hover =====================\n  const onMouseEnter = () => {\n    if (hoverTrigger) {\n      triggerOpen(true);\n    }\n  };\n  const onMouseLeave = () => {\n    if (hoverTrigger) {\n      triggerOpen(false);\n    }\n  };\n  // ===================== Trigger: Click =====================\n  const onInternalTriggerButtonClick = e => {\n    if (clickTrigger) {\n      triggerOpen(!open);\n    }\n    onTriggerButtonClick === null || onTriggerButtonClick === void 0 ? void 0 : onTriggerButtonClick(e);\n  };\n  React.useEffect(() => {\n    if (clickTrigger) {\n      const onDocClick = e => {\n        var _a;\n        // Skip if click on the group\n        if ((_a = floatButtonGroupRef.current) === null || _a === void 0 ? void 0 : _a.contains(e.target)) {\n          return;\n        }\n        triggerOpen(false);\n      };\n      document.addEventListener('click', onDocClick, {\n        capture: true\n      });\n      return () => document.removeEventListener('click', onDocClick, {\n        capture: true\n      });\n    }\n  }, [clickTrigger]);\n  // ======================== Warning =========================\n  if (process.env.NODE_ENV !== 'production') {\n    const warning = devUseWarning('FloatButton.Group');\n    process.env.NODE_ENV !== \"production\" ? warning(!('open' in props) || !!trigger, 'usage', '`open` need to be used together with `trigger`') : void 0;\n  }\n  // ========================= Render =========================\n  return wrapCSSVar(/*#__PURE__*/React.createElement(FloatButtonGroupProvider, {\n    value: shape\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    ref: floatButtonGroupRef,\n    className: groupCls,\n    style: mergedStyle,\n    // Hover trigger\n    onMouseEnter: onMouseEnter,\n    onMouseLeave: onMouseLeave\n  }, isMenuMode ? (/*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(CSSMotion, {\n    visible: open,\n    motionName: `${groupPrefixCls}-wrap`\n  }, _ref => {\n    let {\n      className: motionClassName\n    } = _ref;\n    return /*#__PURE__*/React.createElement(\"div\", {\n      className: classNames(motionClassName, wrapperCls)\n    }, children);\n  }), /*#__PURE__*/React.createElement(FloatButton, Object.assign({\n    type: type,\n    icon: open ? mergedCloseIcon : icon,\n    description: description,\n    \"aria-label\": props['aria-label'],\n    className: `${groupPrefixCls}-trigger`,\n    onClick: onInternalTriggerButtonClick\n  }, floatButtonProps)))) : children)));\n};\nexport default FloatButtonGroup;"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,MAAM,GAAG,IAAI,IAAI,IAAI,CAACA,MAAM,IAAI,UAAUC,CAAC,EAAEC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,CAAC,CAAC;EACV,KAAK,IAAIC,CAAC,IAAIH,CAAC,EAAE,IAAII,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACP,CAAC,EAAEG,CAAC,CAAC,IAAIF,CAAC,CAACO,OAAO,CAACL,CAAC,CAAC,GAAG,CAAC,EAAED,CAAC,CAACC,CAAC,CAAC,GAAGH,CAAC,CAACG,CAAC,CAAC;EAChG,IAAIH,CAAC,IAAI,IAAI,IAAI,OAAOI,MAAM,CAACK,qBAAqB,KAAK,UAAU,EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEP,CAAC,GAAGC,MAAM,CAACK,qBAAqB,CAACT,CAAC,CAAC,EAAEU,CAAC,GAAGP,CAAC,CAACQ,MAAM,EAAED,CAAC,EAAE,EAAE;IAC3I,IAAIT,CAAC,CAACO,OAAO,CAACL,CAAC,CAACO,CAAC,CAAC,CAAC,GAAG,CAAC,IAAIN,MAAM,CAACC,SAAS,CAACO,oBAAoB,CAACL,IAAI,CAACP,CAAC,EAAEG,CAAC,CAACO,CAAC,CAAC,CAAC,EAAER,CAAC,CAACC,CAAC,CAACO,CAAC,CAAC,CAAC,GAAGV,CAAC,CAACG,CAAC,CAACO,CAAC,CAAC,CAAC;EACnG;EACA,OAAOR,CAAC;AACV,CAAC;AACD,OAAOW,KAAK,MAAM,OAAO;AACzB,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAOC,gBAAgB,MAAM,6CAA6C;AAC1E,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,SAAS,MAAM,WAAW;AACjC,OAAOC,QAAQ,MAAM,2BAA2B;AAChD,OAAOC,cAAc,MAAM,iCAAiC;AAC5D,SAASC,SAAS,QAAQ,0BAA0B;AACpD,SAASC,aAAa,QAAQ,kBAAkB;AAChD,SAASC,kBAAkB,QAAQ,4BAA4B;AAC/D,OAAOC,YAAY,MAAM,uCAAuC;AAChE,SAASC,wBAAwB,QAAQ,WAAW;AACpD,OAAOC,WAAW,IAAIC,oBAAoB,QAAQ,eAAe;AACjE,OAAOC,QAAQ,MAAM,SAAS;AAC9B,MAAMC,gBAAgB,GAAGC,KAAK,IAAI;EAChC,IAAIC,EAAE;EACN,MAAM;MACFC,SAAS,EAAEC,kBAAkB;MAC7BC,SAAS;MACTC,KAAK;MACLC,KAAK,GAAG,QAAQ;MAChBC,IAAI,GAAG,SAAS;MAChBC,SAAS,GAAG,KAAK;MACjBC,IAAI,GAAG,aAAazB,KAAK,CAAC0B,aAAa,CAACxB,gBAAgB,EAAE,IAAI,CAAC;MAC/DyB,SAAS;MACTC,WAAW;MACXC,OAAO;MACPC,QAAQ;MACRC,YAAY;MACZC,IAAI,EAAEC,UAAU;MAChBC,OAAO,EAAEC;IACX,CAAC,GAAGnB,KAAK;IACToB,gBAAgB,GAAGlD,MAAM,CAAC8B,KAAK,EAAE,CAAC,WAAW,EAAE,WAAW,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,WAAW,EAAE,MAAM,EAAE,WAAW,EAAE,aAAa,EAAE,SAAS,EAAE,UAAU,EAAE,cAAc,EAAE,MAAM,EAAE,SAAS,CAAC,CAAC;EACnM,MAAM;IACJqB,SAAS;IACTC,YAAY;IACZX,SAAS,EAAEY;EACb,CAAC,GAAG9B,kBAAkB,CAAC,kBAAkB,CAAC;EAC1C,MAAM+B,eAAe,GAAG,CAACvB,EAAE,GAAGU,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAGA,SAAS,GAAGY,gBAAgB,MAAM,IAAI,IAAItB,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,aAAajB,KAAK,CAAC0B,aAAa,CAACzB,aAAa,EAAE,IAAI,CAAC;EAC/L,MAAMiB,SAAS,GAAGoB,YAAY,CAACzB,oBAAoB,EAAEM,kBAAkB,CAAC;EACxE,MAAMsB,OAAO,GAAG/B,YAAY,CAACQ,SAAS,CAAC;EACvC,MAAM,CAACwB,UAAU,EAAEC,MAAM,EAAEC,SAAS,CAAC,GAAG9B,QAAQ,CAACI,SAAS,EAAEuB,OAAO,CAAC;EACpE,MAAMI,cAAc,GAAG,GAAG3B,SAAS,QAAQ;EAC3C,MAAM4B,UAAU,GAAGjB,OAAO,IAAI,CAAC,OAAO,EAAE,OAAO,CAAC,CAACkB,QAAQ,CAAClB,OAAO,CAAC;EAClE,MAAMmB,gBAAgB,GAAGxB,SAAS,IAAI,CAAC,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,QAAQ,CAAC,CAACuB,QAAQ,CAACvB,SAAS,CAAC;EAC5F,MAAMyB,QAAQ,GAAG9C,UAAU,CAAC0C,cAAc,EAAEF,MAAM,EAAEC,SAAS,EAAEH,OAAO,EAAErB,SAAS,EAAE;IACjF,CAAC,GAAGyB,cAAc,MAAM,GAAGR,SAAS,KAAK,KAAK;IAC9C,CAAC,GAAGQ,cAAc,IAAIvB,KAAK,EAAE,GAAGA,KAAK;IACrC,CAAC,GAAGuB,cAAc,IAAIvB,KAAK,SAAS,GAAG,CAACwB,UAAU;IAClD,CAAC,GAAGD,cAAc,IAAIrB,SAAS,EAAE,GAAGsB,UAAU,IAAIE,gBAAgB,CAAC;EACrE,CAAC,CAAC;EACF;EACA,MAAM,CAACE,MAAM,CAAC,GAAG3C,SAAS,CAAC,aAAa,EAAEc,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,KAAK,CAAC6B,MAAM,CAAC;EACrG,MAAMC,WAAW,GAAG5D,MAAM,CAAC6D,MAAM,CAAC7D,MAAM,CAAC6D,MAAM,CAAC,CAAC,CAAC,EAAE/B,KAAK,CAAC,EAAE;IAC1D6B;EACF,CAAC,CAAC;EACF,MAAMG,UAAU,GAAGlD,UAAU,CAACwC,MAAM,EAAE,GAAGE,cAAc,OAAO,CAAC;EAC/D,MAAM,CAACb,IAAI,EAAEsB,OAAO,CAAC,GAAGhD,cAAc,CAAC,KAAK,EAAE;IAC5CiD,KAAK,EAAEtB;EACT,CAAC,CAAC;EACF,MAAMuB,mBAAmB,GAAGxD,KAAK,CAACyD,MAAM,CAAC,IAAI,CAAC;EAC9C;EACA,MAAMC,YAAY,GAAG7B,OAAO,KAAK,OAAO;EACxC,MAAM8B,YAAY,GAAG9B,OAAO,KAAK,OAAO;EACxC,MAAM+B,WAAW,GAAGvD,QAAQ,CAACwD,QAAQ,IAAI;IACvC,IAAI7B,IAAI,KAAK6B,QAAQ,EAAE;MACrBP,OAAO,CAACO,QAAQ,CAAC;MACjB9B,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,YAAY,CAAC8B,QAAQ,CAAC;IACpF;EACF,CAAC,CAAC;EACF;EACA,MAAMC,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAIJ,YAAY,EAAE;MAChBE,WAAW,CAAC,IAAI,CAAC;IACnB;EACF,CAAC;EACD,MAAMG,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAIL,YAAY,EAAE;MAChBE,WAAW,CAAC,KAAK,CAAC;IACpB;EACF,CAAC;EACD;EACA,MAAMI,4BAA4B,GAAG5E,CAAC,IAAI;IACxC,IAAIuE,YAAY,EAAE;MAChBC,WAAW,CAAC,CAAC5B,IAAI,CAAC;IACpB;IACAG,oBAAoB,KAAK,IAAI,IAAIA,oBAAoB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,oBAAoB,CAAC/C,CAAC,CAAC;EACrG,CAAC;EACDY,KAAK,CAACiE,SAAS,CAAC,MAAM;IACpB,IAAIN,YAAY,EAAE;MAChB,MAAMO,UAAU,GAAG9E,CAAC,IAAI;QACtB,IAAI6B,EAAE;QACN;QACA,IAAI,CAACA,EAAE,GAAGuC,mBAAmB,CAACW,OAAO,MAAM,IAAI,IAAIlD,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACmD,QAAQ,CAAChF,CAAC,CAACiF,MAAM,CAAC,EAAE;UACjG;QACF;QACAT,WAAW,CAAC,KAAK,CAAC;MACpB,CAAC;MACDU,QAAQ,CAACC,gBAAgB,CAAC,OAAO,EAAEL,UAAU,EAAE;QAC7CM,OAAO,EAAE;MACX,CAAC,CAAC;MACF,OAAO,MAAMF,QAAQ,CAACG,mBAAmB,CAAC,OAAO,EAAEP,UAAU,EAAE;QAC7DM,OAAO,EAAE;MACX,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAACb,YAAY,CAAC,CAAC;EAClB;EACA,IAAIe,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC,MAAMC,OAAO,GAAGrE,aAAa,CAAC,mBAAmB,CAAC;IAClDkE,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGC,OAAO,CAAC,EAAE,MAAM,IAAI7D,KAAK,CAAC,IAAI,CAAC,CAACa,OAAO,EAAE,OAAO,EAAE,gDAAgD,CAAC,GAAG,KAAK,CAAC;EACtJ;EACA;EACA,OAAOa,UAAU,CAAC,aAAa1C,KAAK,CAAC0B,aAAa,CAACf,wBAAwB,EAAE;IAC3E4C,KAAK,EAAEjC;EACT,CAAC,EAAE,aAAatB,KAAK,CAAC0B,aAAa,CAAC,KAAK,EAAE;IACzCoD,GAAG,EAAEtB,mBAAmB;IACxBpC,SAAS,EAAE6B,QAAQ;IACnB5B,KAAK,EAAE8B,WAAW;IAClB;IACAW,YAAY,EAAEA,YAAY;IAC1BC,YAAY,EAAEA;EAChB,CAAC,EAAEjB,UAAU,IAAI,aAAa9C,KAAK,CAAC0B,aAAa,CAAC1B,KAAK,CAAC+E,QAAQ,EAAE,IAAI,EAAE,aAAa/E,KAAK,CAAC0B,aAAa,CAACtB,SAAS,EAAE;IAClH4E,OAAO,EAAEhD,IAAI;IACbiD,UAAU,EAAE,GAAGpC,cAAc;EAC/B,CAAC,EAAEqC,IAAI,IAAI;IACT,IAAI;MACF9D,SAAS,EAAE+D;IACb,CAAC,GAAGD,IAAI;IACR,OAAO,aAAalF,KAAK,CAAC0B,aAAa,CAAC,KAAK,EAAE;MAC7CN,SAAS,EAAEjB,UAAU,CAACgF,eAAe,EAAE9B,UAAU;IACnD,CAAC,EAAEvB,QAAQ,CAAC;EACd,CAAC,CAAC,EAAE,aAAa9B,KAAK,CAAC0B,aAAa,CAACd,WAAW,EAAErB,MAAM,CAAC6D,MAAM,CAAC;IAC9D7B,IAAI,EAAEA,IAAI;IACVE,IAAI,EAAEO,IAAI,GAAGQ,eAAe,GAAGf,IAAI;IACnCG,WAAW,EAAEA,WAAW;IACxB,YAAY,EAAEZ,KAAK,CAAC,YAAY,CAAC;IACjCI,SAAS,EAAE,GAAGyB,cAAc,UAAU;IACtCX,OAAO,EAAE8B;EACX,CAAC,EAAE5B,gBAAgB,CAAC,CAAC,CAAC,IAAIN,QAAQ,CAAC,CAAC,CAAC;AACvC,CAAC;AACD,eAAef,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}