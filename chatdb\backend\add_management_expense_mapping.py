#!/usr/bin/env python3
"""
添加管理费用相关的字段映射
确保系统能正确识别管理费用查询应该使用account_full_name
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def add_management_expense_mappings():
    """添加管理费用相关映射"""
    print("🔧 添加管理费用相关映射")
    print("=" * 60)
    
    try:
        from app.db.session import SessionLocal
        from sqlalchemy import text
        
        db = SessionLocal()
        
        # 获取account_full_name的column_id
        result = db.execute(text("""
            SELECT sc.id
            FROM schemacolumn sc
            JOIN schematable st ON sc.table_id = st.id
            JOIN dbconnection dc ON st.connection_id = dc.id
            WHERE sc.column_name = 'account_full_name'
            AND st.table_name = 'financial_data'
            AND dc.name = 'fin_data'
        """))
        
        account_full_name_id = result.fetchone()
        if not account_full_name_id:
            print("❌ 未找到account_full_name字段")
            db.close()
            return False
        
        column_id = account_full_name_id[0]
        print(f"✅ 找到account_full_name字段，ID: {column_id}")
        
        # 要添加的管理费用相关映射
        management_mappings = [
            ('管理费用查询', 'account_full_name'),
            ('管理费用分析', 'account_full_name'),
            ('管理费用统计', 'account_full_name'),
            ('管理费用明细', 'account_full_name'),
            ('管理费用汇总', 'account_full_name')
        ]
        
        added_count = 0
        
        for nl_term, db_value in management_mappings:
            # 检查映射是否已存在
            result = db.execute(text("""
                SELECT id FROM valuemapping 
                WHERE column_id = :column_id AND nl_term = :nl_term
            """), {"column_id": column_id, "nl_term": nl_term})
            
            if result.fetchone():
                print(f"⚠️ 映射已存在: '{nl_term}' → {db_value}")
                continue
            
            # 添加新映射
            db.execute(text("""
                INSERT INTO valuemapping (column_id, nl_term, db_value, created_at)
                VALUES (:column_id, :nl_term, :db_value, datetime('now'))
            """), {"column_id": column_id, "nl_term": nl_term, "db_value": db_value})
            
            print(f"✅ 添加映射: '{nl_term}' → {db_value}")
            added_count += 1
        
        # 提交更改
        db.commit()
        print(f"\n💾 成功添加 {added_count} 个管理费用映射")
        
        db.close()
        return added_count > 0
        
    except Exception as e:
        print(f"❌ 添加管理费用映射失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def verify_mappings():
    """验证映射是否正确添加"""
    print(f"\n🔍 验证管理费用映射")
    print("=" * 60)
    
    try:
        from app.db.session import SessionLocal
        from sqlalchemy import text
        
        db = SessionLocal()
        
        # 查询所有管理费用相关映射
        result = db.execute(text("""
            SELECT vm.nl_term, vm.db_value, sc.column_name
            FROM valuemapping vm
            JOIN schemacolumn sc ON vm.column_id = sc.id
            WHERE vm.nl_term LIKE '%管理费用%' OR vm.db_value LIKE '%管理费用%'
            ORDER BY sc.column_name, vm.nl_term
        """))
        
        mappings = result.fetchall()
        
        print(f"📋 管理费用相关映射:")
        if mappings:
            for nl_term, db_value, col_name in mappings:
                print(f"    '{nl_term}' → '{db_value}' ({col_name})")
        else:
            print(f"    ❌ 无管理费用相关映射")
        
        db.close()
        return len(mappings) > 0
        
    except Exception as e:
        print(f"❌ 验证映射失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 添加管理费用字段映射")
    print("=" * 80)
    
    # 添加映射
    success = add_management_expense_mappings()
    
    if success:
        # 验证映射
        verify_mappings()
        
        print("\n" + "=" * 80)
        print("✅ 管理费用映射添加完成！")
        print("\n📝 下一步:")
        print("1. 重启后端服务")
        print("2. 测试管理费用查询")
        print("3. 验证是否使用account_full_name字段")
    else:
        print("\n❌ 管理费用映射添加失败")

if __name__ == "__main__":
    main()
