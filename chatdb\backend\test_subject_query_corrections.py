#!/usr/bin/env python3
"""
测试科目查询修正逻辑
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_subject_query_corrections():
    """测试科目查询修正功能"""
    print("🧪 测试科目查询修正逻辑")
    print("=" * 60)
    
    try:
        from app.services.text2sql_utils import apply_subject_query_corrections
        
        # 测试用例：模拟LLM生成的有问题的SQL
        test_cases = [
            {
                "name": "错误的account_code组合查询",
                "input": """
                SELECT accounting_unit_name AS 公司名称, SUM(debit_amount) AS 管理费用总额
                FROM financial_data
                WHERE year = 2024 AND month = 10 
                AND (account_code = 1000 OR account_full_name LIKE '%管理费用%')
                GROUP BY accounting_unit_name
                ORDER BY 管理费用总额 DESC
                """,
                "expected_fixes": [
                    "移除 account_code = 1000",
                    "保留 account_full_name LIKE '%管理费用%'"
                ]
            },
            {
                "name": "错误的account_name使用",
                "input": """
                SELECT accounting_unit_name AS 公司名称, SUM(debit_amount) AS 销售费用
                FROM financial_data  
                WHERE year = 2024 AND account_name LIKE '%销售费用%'
                GROUP BY accounting_unit_name
                """,
                "expected_fixes": [
                    "将 account_name 替换为 account_full_name"
                ]
            },
            {
                "name": "纯account_code错误查询",
                "input": """
                SELECT * FROM financial_data 
                WHERE account_code = 2000 AND year = 2024
                """,
                "expected_fixes": [
                    "移除 account_code = 2000"
                ]
            },
            {
                "name": "正确的查询（不应修改）",
                "input": """
                SELECT accounting_unit_name, SUM(debit_amount)
                FROM financial_data
                WHERE account_full_name LIKE '%管理费用%' AND year = 2024
                GROUP BY accounting_unit_name
                """,
                "expected_fixes": [
                    "不应修改"
                ]
            }
        ]
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n🔸 测试用例 {i}: {test_case['name']}")
            print(f"原始SQL:")
            print(test_case['input'].strip())
            
            # 应用修正
            corrected_sql = apply_subject_query_corrections(test_case['input'])
            
            print(f"\n修正后SQL:")
            print(corrected_sql.strip())
            
            print(f"\n期望修复:")
            for fix in test_case['expected_fixes']:
                print(f"  - {fix}")
            
            # 简单验证
            has_account_code_number = 'account_code = ' in corrected_sql and any(c.isdigit() for c in corrected_sql)
            has_account_name_like = 'account_name LIKE' in corrected_sql
            
            issues = []
            if has_account_code_number:
                issues.append("仍包含 account_code = 数值")
            if has_account_name_like:
                issues.append("仍使用 account_name LIKE")
            
            if issues:
                print(f"⚠️ 发现问题: {', '.join(issues)}")
            else:
                print(f"✅ 修正成功")
            
            print("-" * 60)
        
        return True
        
    except Exception as e:
        print(f"❌ 测试科目查询修正失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 测试科目查询修正逻辑")
    print("=" * 80)
    
    success = test_subject_query_corrections()
    
    print("\n" + "=" * 80)
    if success:
        print("✅ 科目查询修正逻辑测试完成")
        print("\n💡 如果修正逻辑工作正常，重启服务后应该能解决:")
        print("1. account_code = 1000 的幻觉问题")
        print("2. account_full_name 字段优先级问题")
    else:
        print("❌ 科目查询修正逻辑测试失败")

if __name__ == "__main__":
    main()
