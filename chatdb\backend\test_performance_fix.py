"""
Text2SQL性能修复验证测试
测试系统在查询后是否还会出现卡顿问题
"""
import asyncio
import time
import logging
import sys
import os
from typing import List, Dict, Any
import requests
import json

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.text2sql_performance_monitor import text2sql_monitor
from app.services.neo4j_connection_pool import neo4j_pool

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class PerformanceTestSuite:
    """性能测试套件"""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.test_results = []
        
    async def test_neo4j_connection_pool(self):
        """测试Neo4j连接池修复"""
        logger.info("🔧 测试Neo4j连接池修复...")
        
        try:
            # 测试连接池健康检查
            health = await neo4j_pool.health_check()
            assert health, "Neo4j连接池健康检查失败"
            
            # 测试连接池统计信息
            stats = await neo4j_pool.get_connection_stats()
            assert stats['status'] in ['healthy', 'not_initialized'], f"连接池状态异常: {stats['status']}"
            
            # 测试并发查询（模拟多个查询同时执行）
            start_time = time.time()
            tasks = []
            for i in range(5):
                task = asyncio.create_task(self._test_neo4j_query(f"test_query_{i}"))
                tasks.append(task)
            
            results = await asyncio.gather(*tasks, return_exceptions=True)
            duration = time.time() - start_time
            
            # 检查是否有异常
            exceptions = [r for r in results if isinstance(r, Exception)]
            if exceptions:
                logger.error(f"Neo4j并发查询出现异常: {exceptions}")
                return False
            
            logger.info(f"✅ Neo4j连接池测试通过，并发查询耗时: {duration:.2f}秒")
            return True
            
        except Exception as e:
            logger.error(f"❌ Neo4j连接池测试失败: {e}")
            return False
    
    async def _test_neo4j_query(self, query_id: str):
        """测试Neo4j查询"""
        try:
            # 模拟一个简单的Neo4j查询
            async with neo4j_pool.get_session() as session:
                result = await session.run("RETURN 1 as test")
                data = await result.data()
                return data
        except Exception as e:
            logger.error(f"Neo4j查询失败 {query_id}: {e}")
            raise
    
    def test_api_endpoints(self):
        """测试API端点响应"""
        logger.info("🌐 测试API端点响应...")
        
        endpoints = [
            "/api/performance/health",
            "/api/performance/metrics",
            "/api/performance/active-queries",
            "/api/performance/diagnostics"
        ]
        
        all_passed = True
        for endpoint in endpoints:
            try:
                url = f"{self.base_url}{endpoint}"
                response = requests.get(url, timeout=10)
                
                if response.status_code == 200:
                    logger.info(f"✅ {endpoint} 响应正常")
                else:
                    logger.error(f"❌ {endpoint} 响应异常: {response.status_code}")
                    all_passed = False
                    
            except requests.exceptions.RequestException as e:
                logger.error(f"❌ {endpoint} 请求失败: {e}")
                all_passed = False
        
        return all_passed
    
    def test_text2sql_query_performance(self):
        """测试Text2SQL查询性能"""
        logger.info("🔍 测试Text2SQL查询性能...")
        
        test_queries = [
            "查询所有资产科目的余额",
            "显示本月的收入情况",
            "统计各部门的费用支出",
            "查看现金流量表数据",
            "分析利润表的主要项目"
        ]
        
        all_passed = True
        for i, query in enumerate(test_queries):
            try:
                logger.info(f"测试查询 {i+1}: {query}")
                
                # 发送查询请求
                url = f"{self.base_url}/api/text2sql-sse/stream"
                params = {
                    "query": query,
                    "connection_id": 1,  # 假设连接ID为1
                    "user_feedback_enabled": False
                }
                
                start_time = time.time()
                response = requests.get(url, params=params, timeout=30)
                duration = time.time() - start_time
                
                if response.status_code == 200:
                    logger.info(f"✅ 查询 {i+1} 完成，耗时: {duration:.2f}秒")
                    
                    # 检查是否超过阈值
                    if duration > 30:
                        logger.warning(f"⚠️ 查询 {i+1} 耗时过长: {duration:.2f}秒")
                        all_passed = False
                else:
                    logger.error(f"❌ 查询 {i+1} 失败: {response.status_code}")
                    all_passed = False
                
                # 等待一段时间再执行下一个查询
                time.sleep(2)
                
            except requests.exceptions.RequestException as e:
                logger.error(f"❌ 查询 {i+1} 请求失败: {e}")
                all_passed = False
        
        return all_passed
    
    def test_memory_usage(self):
        """测试内存使用情况"""
        logger.info("💾 测试内存使用情况...")
        
        try:
            # 获取性能指标
            url = f"{self.base_url}/api/performance/metrics"
            response = requests.get(url, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                memory_usage = data.get('metrics', {}).get('system_metrics', {}).get('memory_usage', {}).get('current', 0)
                
                logger.info(f"当前内存使用率: {memory_usage:.1f}%")
                
                if memory_usage > 90:
                    logger.error(f"❌ 内存使用率过高: {memory_usage:.1f}%")
                    return False
                elif memory_usage > 80:
                    logger.warning(f"⚠️ 内存使用率较高: {memory_usage:.1f}%")
                else:
                    logger.info(f"✅ 内存使用率正常: {memory_usage:.1f}%")
                
                return True
            else:
                logger.error(f"❌ 获取内存使用情况失败: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"❌ 内存使用测试失败: {e}")
            return False
    
    def test_concurrent_queries(self):
        """测试并发查询处理"""
        logger.info("🚀 测试并发查询处理...")
        
        try:
            import threading
            import queue
            
            # 创建结果队列
            result_queue = queue.Queue()
            
            def send_query(query_id: int):
                try:
                    url = f"{self.base_url}/api/text2sql-sse/stream"
                    params = {
                        "query": f"测试并发查询 {query_id}",
                        "connection_id": 1,
                        "user_feedback_enabled": False
                    }
                    
                    start_time = time.time()
                    response = requests.get(url, params=params, timeout=30)
                    duration = time.time() - start_time
                    
                    result_queue.put({
                        'query_id': query_id,
                        'success': response.status_code == 200,
                        'duration': duration
                    })
                    
                except Exception as e:
                    result_queue.put({
                        'query_id': query_id,
                        'success': False,
                        'error': str(e)
                    })
            
            # 启动多个并发查询
            threads = []
            for i in range(3):  # 3个并发查询
                thread = threading.Thread(target=send_query, args=(i,))
                threads.append(thread)
                thread.start()
            
            # 等待所有线程完成
            for thread in threads:
                thread.join(timeout=60)
            
            # 收集结果
            results = []
            while not result_queue.empty():
                results.append(result_queue.get())
            
            # 分析结果
            success_count = sum(1 for r in results if r['success'])
            total_count = len(results)
            
            logger.info(f"并发查询结果: {success_count}/{total_count} 成功")
            
            if success_count == total_count:
                logger.info("✅ 并发查询测试通过")
                return True
            else:
                logger.error(f"❌ 并发查询测试失败: {success_count}/{total_count}")
                return False
                
        except Exception as e:
            logger.error(f"❌ 并发查询测试失败: {e}")
            return False
    
    async def run_all_tests(self):
        """运行所有测试"""
        logger.info("🧪 开始运行性能修复验证测试...")
        
        tests = [
            ("Neo4j连接池修复", self.test_neo4j_connection_pool()),
            ("API端点响应", self.test_api_endpoints()),
            ("Text2SQL查询性能", self.test_text2sql_query_performance()),
            ("内存使用情况", self.test_memory_usage()),
            ("并发查询处理", self.test_concurrent_queries())
        ]
        
        passed_tests = 0
        total_tests = len(tests)
        
        for test_name, test_func in tests:
            logger.info(f"\n{'='*50}")
            logger.info(f"运行测试: {test_name}")
            logger.info(f"{'='*50}")
            
            try:
                if asyncio.iscoroutine(test_func):
                    result = await test_func
                else:
                    result = test_func
                
                if result:
                    logger.info(f"✅ {test_name} - 通过")
                    passed_tests += 1
                else:
                    logger.error(f"❌ {test_name} - 失败")
                    
            except Exception as e:
                logger.error(f"❌ {test_name} - 异常: {e}")
        
        # 输出测试总结
        logger.info(f"\n{'='*50}")
        logger.info(f"测试总结")
        logger.info(f"{'='*50}")
        logger.info(f"通过测试: {passed_tests}/{total_tests}")
        logger.info(f"成功率: {passed_tests/total_tests*100:.1f}%")
        
        if passed_tests == total_tests:
            logger.info("🎉 所有测试通过！系统性能修复成功！")
            return True
        else:
            logger.error("⚠️ 部分测试失败，需要进一步调试")
            return False


async def main():
    """主函数"""
    test_suite = PerformanceTestSuite()
    success = await test_suite.run_all_tests()
    
    if success:
        print("\n🎉 性能修复验证成功！")
        sys.exit(0)
    else:
        print("\n❌ 性能修复验证失败！")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
