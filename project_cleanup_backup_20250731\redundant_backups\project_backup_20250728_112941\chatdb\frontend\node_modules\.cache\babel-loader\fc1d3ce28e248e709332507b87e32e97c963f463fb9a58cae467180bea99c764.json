{"ast": null, "code": "import { Keyframes } from '@ant-design/cssinjs';\nconst genNotificationPlacementStyle = token => {\n  const {\n    componentCls,\n    notificationMarginEdge,\n    animationMaxHeight\n  } = token;\n  const noticeCls = `${componentCls}-notice`;\n  const rightFadeIn = new Keyframes('antNotificationFadeIn', {\n    '0%': {\n      transform: `translate3d(100%, 0, 0)`,\n      opacity: 0\n    },\n    '100%': {\n      transform: `translate3d(0, 0, 0)`,\n      opacity: 1\n    }\n  });\n  const topFadeIn = new Keyframes('antNotificationTopFadeIn', {\n    '0%': {\n      top: -animationMaxHeight,\n      opacity: 0\n    },\n    '100%': {\n      top: 0,\n      opacity: 1\n    }\n  });\n  const bottomFadeIn = new Keyframes('antNotificationBottomFadeIn', {\n    '0%': {\n      bottom: token.calc(animationMaxHeight).mul(-1).equal(),\n      opacity: 0\n    },\n    '100%': {\n      bottom: 0,\n      opacity: 1\n    }\n  });\n  const leftFadeIn = new Keyframes('antNotificationLeftFadeIn', {\n    '0%': {\n      transform: `translate3d(-100%, 0, 0)`,\n      opacity: 0\n    },\n    '100%': {\n      transform: `translate3d(0, 0, 0)`,\n      opacity: 1\n    }\n  });\n  return {\n    [componentCls]: {\n      [`&${componentCls}-top, &${componentCls}-bottom`]: {\n        marginInline: 0,\n        [noticeCls]: {\n          marginInline: 'auto auto'\n        }\n      },\n      [`&${componentCls}-top`]: {\n        [`${componentCls}-fade-enter${componentCls}-fade-enter-active, ${componentCls}-fade-appear${componentCls}-fade-appear-active`]: {\n          animationName: topFadeIn\n        }\n      },\n      [`&${componentCls}-bottom`]: {\n        [`${componentCls}-fade-enter${componentCls}-fade-enter-active, ${componentCls}-fade-appear${componentCls}-fade-appear-active`]: {\n          animationName: bottomFadeIn\n        }\n      },\n      [`&${componentCls}-topRight, &${componentCls}-bottomRight`]: {\n        [`${componentCls}-fade-enter${componentCls}-fade-enter-active, ${componentCls}-fade-appear${componentCls}-fade-appear-active`]: {\n          animationName: rightFadeIn\n        }\n      },\n      [`&${componentCls}-topLeft, &${componentCls}-bottomLeft`]: {\n        marginRight: {\n          value: 0,\n          _skip_check_: true\n        },\n        marginLeft: {\n          value: notificationMarginEdge,\n          _skip_check_: true\n        },\n        [noticeCls]: {\n          marginInlineEnd: 'auto',\n          marginInlineStart: 0\n        },\n        [`${componentCls}-fade-enter${componentCls}-fade-enter-active, ${componentCls}-fade-appear${componentCls}-fade-appear-active`]: {\n          animationName: leftFadeIn\n        }\n      }\n    }\n  };\n};\nexport default genNotificationPlacementStyle;", "map": {"version": 3, "names": ["Keyframes", "genNotificationPlacementStyle", "token", "componentCls", "notificationMarginEdge", "animationMaxHeight", "noticeCls", "rightFadeIn", "transform", "opacity", "topFadeIn", "top", "bottomFadeIn", "bottom", "calc", "mul", "equal", "leftFadeIn", "marginInline", "animationName", "marginRight", "value", "_skip_check_", "marginLeft", "marginInlineEnd", "marginInlineStart"], "sources": ["C:/Users/<USER>/PycharmProjects/智能数据分析系统/chatdb/frontend/node_modules/antd/es/notification/style/placement.js"], "sourcesContent": ["import { Keyframes } from '@ant-design/cssinjs';\nconst genNotificationPlacementStyle = token => {\n  const {\n    componentCls,\n    notificationMarginEdge,\n    animationMaxHeight\n  } = token;\n  const noticeCls = `${componentCls}-notice`;\n  const rightFadeIn = new Keyframes('antNotificationFadeIn', {\n    '0%': {\n      transform: `translate3d(100%, 0, 0)`,\n      opacity: 0\n    },\n    '100%': {\n      transform: `translate3d(0, 0, 0)`,\n      opacity: 1\n    }\n  });\n  const topFadeIn = new Keyframes('antNotificationTopFadeIn', {\n    '0%': {\n      top: -animationMaxHeight,\n      opacity: 0\n    },\n    '100%': {\n      top: 0,\n      opacity: 1\n    }\n  });\n  const bottomFadeIn = new Keyframes('antNotificationBottomFadeIn', {\n    '0%': {\n      bottom: token.calc(animationMaxHeight).mul(-1).equal(),\n      opacity: 0\n    },\n    '100%': {\n      bottom: 0,\n      opacity: 1\n    }\n  });\n  const leftFadeIn = new Keyframes('antNotificationLeftFadeIn', {\n    '0%': {\n      transform: `translate3d(-100%, 0, 0)`,\n      opacity: 0\n    },\n    '100%': {\n      transform: `translate3d(0, 0, 0)`,\n      opacity: 1\n    }\n  });\n  return {\n    [componentCls]: {\n      [`&${componentCls}-top, &${componentCls}-bottom`]: {\n        marginInline: 0,\n        [noticeCls]: {\n          marginInline: 'auto auto'\n        }\n      },\n      [`&${componentCls}-top`]: {\n        [`${componentCls}-fade-enter${componentCls}-fade-enter-active, ${componentCls}-fade-appear${componentCls}-fade-appear-active`]: {\n          animationName: topFadeIn\n        }\n      },\n      [`&${componentCls}-bottom`]: {\n        [`${componentCls}-fade-enter${componentCls}-fade-enter-active, ${componentCls}-fade-appear${componentCls}-fade-appear-active`]: {\n          animationName: bottomFadeIn\n        }\n      },\n      [`&${componentCls}-topRight, &${componentCls}-bottomRight`]: {\n        [`${componentCls}-fade-enter${componentCls}-fade-enter-active, ${componentCls}-fade-appear${componentCls}-fade-appear-active`]: {\n          animationName: rightFadeIn\n        }\n      },\n      [`&${componentCls}-topLeft, &${componentCls}-bottomLeft`]: {\n        marginRight: {\n          value: 0,\n          _skip_check_: true\n        },\n        marginLeft: {\n          value: notificationMarginEdge,\n          _skip_check_: true\n        },\n        [noticeCls]: {\n          marginInlineEnd: 'auto',\n          marginInlineStart: 0\n        },\n        [`${componentCls}-fade-enter${componentCls}-fade-enter-active, ${componentCls}-fade-appear${componentCls}-fade-appear-active`]: {\n          animationName: leftFadeIn\n        }\n      }\n    }\n  };\n};\nexport default genNotificationPlacementStyle;"], "mappings": "AAAA,SAASA,SAAS,QAAQ,qBAAqB;AAC/C,MAAMC,6BAA6B,GAAGC,KAAK,IAAI;EAC7C,MAAM;IACJC,YAAY;IACZC,sBAAsB;IACtBC;EACF,CAAC,GAAGH,KAAK;EACT,MAAMI,SAAS,GAAG,GAAGH,YAAY,SAAS;EAC1C,MAAMI,WAAW,GAAG,IAAIP,SAAS,CAAC,uBAAuB,EAAE;IACzD,IAAI,EAAE;MACJQ,SAAS,EAAE,yBAAyB;MACpCC,OAAO,EAAE;IACX,CAAC;IACD,MAAM,EAAE;MACND,SAAS,EAAE,sBAAsB;MACjCC,OAAO,EAAE;IACX;EACF,CAAC,CAAC;EACF,MAAMC,SAAS,GAAG,IAAIV,SAAS,CAAC,0BAA0B,EAAE;IAC1D,IAAI,EAAE;MACJW,GAAG,EAAE,CAACN,kBAAkB;MACxBI,OAAO,EAAE;IACX,CAAC;IACD,MAAM,EAAE;MACNE,GAAG,EAAE,CAAC;MACNF,OAAO,EAAE;IACX;EACF,CAAC,CAAC;EACF,MAAMG,YAAY,GAAG,IAAIZ,SAAS,CAAC,6BAA6B,EAAE;IAChE,IAAI,EAAE;MACJa,MAAM,EAAEX,KAAK,CAACY,IAAI,CAACT,kBAAkB,CAAC,CAACU,GAAG,CAAC,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC;MACtDP,OAAO,EAAE;IACX,CAAC;IACD,MAAM,EAAE;MACNI,MAAM,EAAE,CAAC;MACTJ,OAAO,EAAE;IACX;EACF,CAAC,CAAC;EACF,MAAMQ,UAAU,GAAG,IAAIjB,SAAS,CAAC,2BAA2B,EAAE;IAC5D,IAAI,EAAE;MACJQ,SAAS,EAAE,0BAA0B;MACrCC,OAAO,EAAE;IACX,CAAC;IACD,MAAM,EAAE;MACND,SAAS,EAAE,sBAAsB;MACjCC,OAAO,EAAE;IACX;EACF,CAAC,CAAC;EACF,OAAO;IACL,CAACN,YAAY,GAAG;MACd,CAAC,IAAIA,YAAY,UAAUA,YAAY,SAAS,GAAG;QACjDe,YAAY,EAAE,CAAC;QACf,CAACZ,SAAS,GAAG;UACXY,YAAY,EAAE;QAChB;MACF,CAAC;MACD,CAAC,IAAIf,YAAY,MAAM,GAAG;QACxB,CAAC,GAAGA,YAAY,cAAcA,YAAY,uBAAuBA,YAAY,eAAeA,YAAY,qBAAqB,GAAG;UAC9HgB,aAAa,EAAET;QACjB;MACF,CAAC;MACD,CAAC,IAAIP,YAAY,SAAS,GAAG;QAC3B,CAAC,GAAGA,YAAY,cAAcA,YAAY,uBAAuBA,YAAY,eAAeA,YAAY,qBAAqB,GAAG;UAC9HgB,aAAa,EAAEP;QACjB;MACF,CAAC;MACD,CAAC,IAAIT,YAAY,eAAeA,YAAY,cAAc,GAAG;QAC3D,CAAC,GAAGA,YAAY,cAAcA,YAAY,uBAAuBA,YAAY,eAAeA,YAAY,qBAAqB,GAAG;UAC9HgB,aAAa,EAAEZ;QACjB;MACF,CAAC;MACD,CAAC,IAAIJ,YAAY,cAAcA,YAAY,aAAa,GAAG;QACzDiB,WAAW,EAAE;UACXC,KAAK,EAAE,CAAC;UACRC,YAAY,EAAE;QAChB,CAAC;QACDC,UAAU,EAAE;UACVF,KAAK,EAAEjB,sBAAsB;UAC7BkB,YAAY,EAAE;QAChB,CAAC;QACD,CAAChB,SAAS,GAAG;UACXkB,eAAe,EAAE,MAAM;UACvBC,iBAAiB,EAAE;QACrB,CAAC;QACD,CAAC,GAAGtB,YAAY,cAAcA,YAAY,uBAAuBA,YAAY,eAAeA,YAAY,qBAAqB,GAAG;UAC9HgB,aAAa,EAAEF;QACjB;MACF;IACF;EACF,CAAC;AACH,CAAC;AACD,eAAehB,6BAA6B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}