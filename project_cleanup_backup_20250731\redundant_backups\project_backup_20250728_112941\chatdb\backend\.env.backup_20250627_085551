# ===== LLM配置 =====
# 使用阿里云DashScope的Qwen模型
OPENAI_API_KEY=sk-ea96a14cb80c419d9393b91f0d42fa7f
OPENAI_API_BASE=https://dashscope.aliyuncs.com/compatible-mode/v1
LLM_MODEL=qwen-max

# 模型参数配置
LLM_TEMPERATURE=0.1
LLM_MAX_TOKENS=4000

# Neo4j配置
NEO4J_URI=neo4j://localhost:7687
NEO4J_USER=neo4j
NEO4J_PASSWORD=Di@nhua11

# ===== 数据库配置 =====
# 数据库类型配置
DATABASE_TYPE=sqlite
# SQLite配置 - 指向包含financial_data和元数据的数据库文件（完整路径）
SQLITE_DB_PATH=C:\Users\<USER>\PycharmProjects\智能数据分析系统\fin_data.db

# MySQL配置（已禁用，切换到SQLite）
# MYSQL_SERVER=localhost
# MYSQL_PORT=3306
# MYSQL_USER=root
# MYSQL_DB=chatdb
# MYSQL_PASSWORD=mysql

# ===== Milvus配置 =====
MILVUS_HOST=localhost
MILVUS_PORT=19530

# ===== 向量模型配置 =====
# 使用BGE中文向量模型
EMBEDDING_MODEL=BAAI/bge-base-zh-v1.5
VECTOR_DIMENSION=768

# ===== 混合检索配置 =====
HYBRID_RETRIEVAL_ENABLED=true
SEMANTIC_WEIGHT=0.35
STRUCTURAL_WEIGHT=0.35
PATTERN_WEIGHT=0.20
QUALITY_WEIGHT=0.10

# ===== 学习配置 =====
AUTO_LEARNING_ENABLED=true
FEEDBACK_LEARNING_ENABLED=true
PATTERN_DISCOVERY_ENABLED=true

# ===== 性能配置 =====
RETRIEVAL_CACHE_TTL=3600
MAX_EXAMPLES_PER_QUERY=5
PARALLEL_RETRIEVAL=true