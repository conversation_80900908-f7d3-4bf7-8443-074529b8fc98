#!/usr/bin/env python3
"""
调试映射连接问题
检查schema_context中的column_id与resource.db中的映射是否匹配
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import sqlite3
from sqlalchemy.orm import Session
from sqlalchemy import create_engine
from app.core.config import settings

def check_schema_context_columns():
    """检查schema_context中的列信息"""
    print("🔍 检查schema_context中的列信息")
    print("=" * 60)
    
    try:
        # 连接到主数据库（通常是resource.db）
        engine = create_engine(f"sqlite:///{settings.SQLITE_DB_PATH}")
        db = Session(engine)
        
        # 查询fin_data连接的financial_data表的列信息
        from sqlalchemy import text
        query = text("""
        SELECT sc.id, sc.column_name, st.table_name, dc.name as connection_name
        FROM schemacolumn sc
        JOIN schematable st ON sc.table_id = st.id
        JOIN dbconnection dc ON st.connection_id = dc.id
        WHERE dc.name = 'fin_data' AND st.table_name = 'financial_data'
        ORDER BY sc.column_name
        """)

        result = db.execute(query)
        columns = result.fetchall()
        
        print(f"📋 找到 {len(columns)} 个列:")
        for col_id, col_name, table_name, conn_name in columns:
            print(f"  ID:{col_id} - {col_name} ({table_name}.{conn_name})")
        
        db.close()
        return columns
        
    except Exception as e:
        print(f"❌ 查询schema列信息失败: {e}")
        return []

def check_value_mappings_for_columns(columns):
    """检查这些列的值映射"""
    print("\n🔗 检查列的值映射")
    print("=" * 60)
    
    try:
        # 连接到resource.db
        project_root = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
        db_path = os.path.join(project_root, 'resource.db')
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查关键列的映射
        key_columns = ['accounting_unit_name', 'accounting_organization', 'account_full_name', 'account_name']
        
        for col_id, col_name, table_name, conn_name in columns:
            if col_name in key_columns:
                print(f"\n🔸 检查列: {col_name} (ID: {col_id})")
                
                # 查询该列的映射
                cursor.execute('''
                    SELECT id, nl_term, db_value
                    FROM valuemapping
                    WHERE column_id = ?
                    ORDER BY nl_term
                ''', (col_id,))
                
                mappings = cursor.fetchall()
                if mappings:
                    print(f"  找到 {len(mappings)} 个映射:")
                    for mapping_id, nl_term, db_value in mappings:
                        print(f"    '{nl_term}' → '{db_value}'")
                else:
                    print(f"  ❌ 未找到映射")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 检查值映射失败: {e}")

def simulate_get_value_mappings():
    """模拟get_value_mappings函数的执行"""
    print("\n🧪 模拟get_value_mappings函数执行")
    print("=" * 60)
    
    try:
        from app.services.text2sql_utils import get_value_mappings
        from app import crud
        from sqlalchemy import create_engine
        
        # 创建数据库会话
        engine = create_engine(f"sqlite:///{settings.SQLITE_DB_PATH}")
        db = Session(engine)
        
        # 模拟schema_context
        schema_context = {
            "columns": []
        }
        
        # 获取实际的列信息
        from sqlalchemy import text
        query = text("""
        SELECT sc.id, sc.column_name, st.table_name
        FROM schemacolumn sc
        JOIN schematable st ON sc.table_id = st.id
        JOIN dbconnection dc ON st.connection_id = dc.id
        WHERE dc.name = 'fin_data' AND st.table_name = 'financial_data'
        """)

        result = db.execute(query)
        columns = result.fetchall()
        
        for col_id, col_name, table_name in columns:
            schema_context["columns"].append({
                "id": col_id,
                "name": col_name,
                "table_name": table_name
            })
        
        print(f"📋 模拟schema_context包含 {len(schema_context['columns'])} 个列")
        
        # 调用get_value_mappings
        mappings = get_value_mappings(db, schema_context)
        
        print(f"📊 获取到 {len(mappings)} 个表.列的映射:")
        for table_col, mapping_dict in mappings.items():
            print(f"\n🔸 {table_col}:")
            for nl_term, db_value in mapping_dict.items():
                print(f"    '{nl_term}' → '{db_value}'")
        
        # 检查关键映射是否存在
        key_mappings = [
            ('financial_data.accounting_unit_name', '公司'),
            ('financial_data.account_full_name', '科目名称'),
            ('financial_data.account_full_name', 'account_name')
        ]
        
        print(f"\n📋 关键映射检查:")
        for table_col, nl_term in key_mappings:
            if table_col in mappings and nl_term in mappings[table_col]:
                db_value = mappings[table_col][nl_term]
                print(f"  ✅ {table_col}['{nl_term}'] → '{db_value}'")
            else:
                print(f"  ❌ {table_col}['{nl_term}'] → 未找到")
        
        db.close()
        return len(mappings) > 0
        
    except Exception as e:
        print(f"❌ 模拟get_value_mappings失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_database_connections():
    """检查数据库连接配置"""
    print("\n🔧 检查数据库连接配置")
    print("=" * 60)
    
    print(f"SQLITE_DB_PATH: {settings.SQLITE_DB_PATH}")
    print(f"METADATA_DB_PATH: {settings.METADATA_DB_PATH}")
    print(f"BUSINESS_DB_PATH: {settings.BUSINESS_DB_PATH}")
    
    # 检查文件是否存在
    paths_to_check = [
        ("resource.db", settings.SQLITE_DB_PATH),
        ("fin_data.db", settings.BUSINESS_DB_PATH)
    ]
    
    for name, path in paths_to_check:
        if not os.path.isabs(path):
            project_root = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
            full_path = os.path.join(project_root, path)
        else:
            full_path = path
            
        exists = os.path.exists(full_path)
        status = "✅" if exists else "❌"
        print(f"{status} {name}: {full_path}")

def provide_solution():
    """提供解决方案"""
    print("\n🔧 问题解决方案")
    print("=" * 60)
    
    print("基于调试结果，可能的问题和解决方案:")
    print()
    print("1. **数据库连接问题**:")
    print("   - 检查系统是否连接到正确的resource.db")
    print("   - 确认schema_context中的column_id与resource.db中的ID匹配")
    print()
    print("2. **映射数据问题**:")
    print("   - 确认valuemapping表中确实存在相关映射")
    print("   - 检查column_id的关联是否正确")
    print()
    print("3. **代码逻辑问题**:")
    print("   - 确认get_value_mappings函数被正确调用")
    print("   - 检查是否有异常被忽略")
    print()
    print("4. **缓存问题**:")
    print("   - 重启服务清除所有缓存")
    print("   - 检查是否有Redis或其他缓存机制")

def main():
    """主函数"""
    print("🚀 调试映射连接问题")
    print("=" * 80)
    
    try:
        # 执行调试步骤
        check_database_connections()
        columns = check_schema_context_columns()
        
        if columns:
            check_value_mappings_for_columns(columns)
            mapping_success = simulate_get_value_mappings()
            
            if mapping_success:
                print("\n✅ 映射获取成功！问题可能在其他地方。")
            else:
                print("\n❌ 映射获取失败！这是问题的根源。")
        else:
            print("\n❌ 无法获取列信息，请检查数据库连接。")
        
        provide_solution()
        
    except Exception as e:
        print(f"\n❌ 调试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
