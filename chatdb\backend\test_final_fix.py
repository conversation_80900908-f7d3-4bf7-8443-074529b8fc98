#!/usr/bin/env python3
"""
测试最终修复效果
验证参数顺序修复后的完整流程
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_construct_prompt_parameters():
    """测试construct_prompt参数顺序"""
    print("🧪 测试construct_prompt参数顺序")
    print("=" * 60)
    
    try:
        from app.services.text2sql_service import construct_prompt
        
        # 模拟参数
        query = "分析2024年1月各公司管理费用"
        schema_context = {
            "tables": [{"name": "financial_data", "columns": []}],
            "columns": [
                {"id": 226, "name": "accounting_unit_name", "table_name": "financial_data"},
                {"id": 228, "name": "account_full_name", "table_name": "financial_data"}
            ]
        }
        value_mappings = {
            "financial_data.accounting_unit_name": {
                "公司": "accounting_unit_name",
                "企业": "accounting_unit_name"
            },
            "financial_data.account_full_name": {
                "科目名称": "account_full_name",
                "account_name": "account_full_name"
            }
        }
        metadata = {}
        
        # 测试正确的参数顺序
        print("📝 调用construct_prompt...")
        prompt = construct_prompt(query, schema_context, value_mappings, metadata)
        
        print(f"✅ 提示生成成功，长度: {len(prompt)} 字符")
        
        # 检查关键内容
        key_checks = [
            ("包含查询内容", query in prompt),
            ("包含值映射", "公司" in prompt or "accounting_unit_name" in prompt),
            ("包含字段优先级", "优先级" in prompt or "首选" in prompt),
            ("包含表结构", "financial_data" in prompt)
        ]
        
        print("\n📋 关键内容检查:")
        for check_name, result in key_checks:
            status = "✅" if result else "❌"
            print(f"  {check_name}: {status}")
        
        # 显示提示片段
        if len(prompt) > 500:
            print(f"\n📄 提示内容预览 (前500字符):")
            print(prompt[:500] + "...")
        else:
            print(f"\n📄 完整提示内容:")
            print(prompt)
        
        return len(value_mappings) > 0 and len(prompt) > 100
        
    except Exception as e:
        print(f"❌ 测试construct_prompt失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_value_mappings_in_prompt():
    """测试值映射是否正确传递到提示中"""
    print("\n🔗 测试值映射传递")
    print("=" * 60)
    
    try:
        from app.services.text2sql_service import construct_prompt
        from app.services.text2sql_utils import process_sql_with_value_mappings
        
        # 模拟完整的值映射
        value_mappings = {
            "financial_data.accounting_unit_name": {
                "公司": "accounting_unit_name",
                "企业": "accounting_unit_name",
                "company": "accounting_unit_name",
                "company_name": "accounting_unit_name"
            },
            "financial_data.account_full_name": {
                "科目名称": "account_full_name",
                "会计科目名称": "account_full_name",
                "account_name": "account_full_name",
                "科目": "account_full_name"
            }
        }
        
        schema_context = {
            "tables": [{"name": "financial_data"}],
            "columns": [
                {"id": 226, "name": "accounting_unit_name", "table_name": "financial_data"},
                {"id": 228, "name": "account_full_name", "table_name": "financial_data"}
            ]
        }
        
        query = "分析2024年1月各公司管理费用"
        
        # 生成提示
        prompt = construct_prompt(query, schema_context, value_mappings, {})
        
        print(f"📊 值映射数量: {sum(len(mappings) for mappings in value_mappings.values())}")
        print(f"📝 提示长度: {len(prompt)} 字符")
        
        # 检查值映射是否在提示中
        mapping_terms = []
        for table_col, mappings in value_mappings.items():
            for nl_term, db_value in mappings.items():
                mapping_terms.append(nl_term)
        
        found_terms = [term for term in mapping_terms if term in prompt]
        print(f"📋 提示中找到的映射术语: {len(found_terms)}/{len(mapping_terms)}")
        
        if found_terms:
            print(f"  找到的术语: {found_terms[:5]}{'...' if len(found_terms) > 5 else ''}")
        
        # 测试SQL后处理
        test_sql = "SELECT accounting_organization, account_name FROM financial_data WHERE account_name LIKE '%管理费用%'"
        processed_sql = process_sql_with_value_mappings(test_sql, value_mappings)
        
        print(f"\n🔧 SQL后处理测试:")
        print(f"  原始SQL: {test_sql}")
        print(f"  处理后SQL: {processed_sql}")
        
        uses_correct_fields = "accounting_unit_name" in processed_sql and "account_full_name" in processed_sql
        print(f"  使用正确字段: {'✅' if uses_correct_fields else '❌'}")
        
        return len(found_terms) > 0 and uses_correct_fields
        
    except Exception as e:
        print(f"❌ 测试值映射传递失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_enhanced_prompt_service():
    """测试增强提示服务"""
    print("\n🎨 测试增强提示服务")
    print("=" * 60)
    
    try:
        from app.services.enhanced_prompt_service import EnhancedPromptService
        
        prompt_service = EnhancedPromptService()
        
        # 测试字段映射指导
        guidance = prompt_service.get_field_mapping_guidance()
        
        print(f"📝 字段映射指导长度: {len(guidance)} 字符")
        
        # 检查关键优先级指导
        priority_checks = [
            ("accounting_unit_name优先级", "accounting_unit_name" in guidance),
            ("account_full_name优先级", "account_full_name" in guidance),
            ("优先级规则", "优先级" in guidance or "首选" in guidance),
            ("禁用字段警告", "禁止" in guidance or "禁用" in guidance)
        ]
        
        print("📋 优先级指导检查:")
        for check_name, result in priority_checks:
            status = "✅" if result else "❌"
            print(f"  {check_name}: {status}")
        
        return all(result for _, result in priority_checks)
        
    except Exception as e:
        print(f"❌ 测试增强提示服务失败: {e}")
        return False

def simulate_complete_pipeline():
    """模拟完整的处理流程"""
    print("\n🔄 模拟完整处理流程")
    print("=" * 60)
    
    try:
        from app.services.text2sql_service import construct_prompt
        from app.services.text2sql_utils import process_sql_with_value_mappings
        
        # 模拟完整流程的数据
        query = "分析2024年1月各公司管理费用"
        
        schema_context = {
            "tables": [{"name": "financial_data"}],
            "columns": [
                {"id": 223, "name": "year", "table_name": "financial_data"},
                {"id": 224, "name": "month", "table_name": "financial_data"},
                {"id": 226, "name": "accounting_unit_name", "table_name": "financial_data"},
                {"id": 228, "name": "account_full_name", "table_name": "financial_data"},
                {"id": 249, "name": "debit_amount", "table_name": "financial_data"}
            ]
        }
        
        value_mappings = {
            "financial_data.accounting_unit_name": {
                "公司": "accounting_unit_name",
                "企业": "accounting_unit_name"
            },
            "financial_data.account_full_name": {
                "科目名称": "account_full_name",
                "管理费用": "account_full_name"
            },
            "financial_data.debit_amount": {
                "费用": "debit_amount",
                "管理费用": "debit_amount"
            }
        }
        
        print("🔸 步骤1: 构建提示")
        prompt = construct_prompt(query, schema_context, value_mappings, {})
        print(f"  提示长度: {len(prompt)} 字符")
        
        print("🔸 步骤2: 模拟LLM生成SQL（可能有错误字段）")
        # 模拟LLM可能生成的错误SQL
        llm_sql = """SELECT 
    accounting_organization, 
    SUM(debit_amount) AS total_management_expenses
FROM financial_data 
WHERE year = 2024 AND month = 1 AND account_name LIKE '%管理费用%'
GROUP BY accounting_organization
ORDER BY total_management_expenses DESC"""
        
        print(f"  LLM生成的SQL: {llm_sql[:100]}...")
        
        print("🔸 步骤3: SQL后处理修正")
        corrected_sql = process_sql_with_value_mappings(llm_sql, value_mappings)
        print(f"  修正后的SQL: {corrected_sql[:100]}...")
        
        # 检查修正效果
        corrections = [
            ("使用accounting_unit_name", "accounting_unit_name" in corrected_sql),
            ("使用account_full_name", "account_full_name" in corrected_sql),
            ("不使用accounting_organization", "accounting_organization" not in corrected_sql),
            ("不使用account_name", "account_name" not in corrected_sql or "account_full_name" in corrected_sql)
        ]
        
        print("📋 修正效果检查:")
        all_correct = True
        for check_name, result in corrections:
            status = "✅" if result else "❌"
            print(f"  {check_name}: {status}")
            if not result:
                all_correct = False
        
        return all_correct
        
    except Exception as e:
        print(f"❌ 模拟完整流程失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 测试最终修复效果")
    print("=" * 80)
    
    # 执行所有测试
    tests = [
        ("construct_prompt参数顺序", test_construct_prompt_parameters),
        ("值映射传递", test_value_mappings_in_prompt),
        ("增强提示服务", test_enhanced_prompt_service),
        ("完整流程模拟", simulate_complete_pipeline)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 执行失败: {e}")
            results.append((test_name, False))
    
    # 总结结果
    print("\n" + "=" * 80)
    print("📊 最终测试结果")
    print("=" * 80)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
    
    all_passed = all(result for _, result in results)
    
    if all_passed:
        print("\n🎉 所有测试通过！字段映射优先级修复已完成。")
        print("\n📝 下一步:")
        print("1. 重启后端服务")
        print("2. 清除所有缓存")
        print("3. 在前端测试智能查询功能")
        print("4. 验证生成的SQL使用正确的优先字段")
    else:
        print("\n⚠️ 部分测试失败，需要进一步调试。")

if __name__ == "__main__":
    main()
