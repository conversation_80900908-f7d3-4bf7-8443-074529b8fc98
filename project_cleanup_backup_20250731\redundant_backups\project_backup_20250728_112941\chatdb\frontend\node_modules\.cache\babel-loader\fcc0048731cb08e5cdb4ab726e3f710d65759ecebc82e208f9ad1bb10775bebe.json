{"ast": null, "code": "import warning from \"rc-util/es/warning\";\nimport * as React from 'react';\nexport var HOOK_MARK = 'RC_FORM_INTERNAL_HOOKS';\n\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nvar warningFunc = function warningFunc() {\n  warning(false, 'Can not find FormContext. Please make sure you wrap Field under Form.');\n};\nvar Context = /*#__PURE__*/React.createContext({\n  getFieldValue: warningFunc,\n  getFieldsValue: warningFunc,\n  getFieldError: warningFunc,\n  getFieldWarning: warningFunc,\n  getFieldsError: warningFunc,\n  isFieldsTouched: warningFunc,\n  isFieldTouched: warningFunc,\n  isFieldValidating: warningFunc,\n  isFieldsValidating: warningFunc,\n  resetFields: warningFunc,\n  setFields: warningFunc,\n  setFieldValue: warningFunc,\n  setFieldsValue: warningFunc,\n  validateFields: warningFunc,\n  submit: warningFunc,\n  getInternalHooks: function getInternalHooks() {\n    warningFunc();\n    return {\n      dispatch: warningFunc,\n      initEntityValue: warningFunc,\n      registerField: warningFunc,\n      useSubscribe: warningFunc,\n      setInitialValues: warningFunc,\n      destroyForm: warningFunc,\n      setCallbacks: warningFunc,\n      registerWatch: warningFunc,\n      getFields: warningFunc,\n      setValidateMessages: warningFunc,\n      setPreserve: warningFunc,\n      getInitialValue: warningFunc\n    };\n  }\n});\nexport default Context;", "map": {"version": 3, "names": ["warning", "React", "HOOK_MARK", "warningFunc", "Context", "createContext", "getFieldValue", "getFieldsValue", "getFieldError", "getFieldWarning", "getFieldsError", "isFieldsTouched", "isFieldTouched", "isFieldValidating", "isFieldsValidating", "resetFields", "setFields", "setFieldValue", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "validateFields", "submit", "getInternalHooks", "dispatch", "initEntityValue", "registerField", "useSubscribe", "setInitialValues", "destroyForm", "setCallbacks", "registerWatch", "getFields", "setValidateMessages", "setPreserve", "getInitialValue"], "sources": ["C:/Users/<USER>/PycharmProjects/智能数据分析系统/chatdb/frontend/node_modules/rc-field-form/es/FieldContext.js"], "sourcesContent": ["import warning from \"rc-util/es/warning\";\nimport * as React from 'react';\nexport var HOOK_MARK = 'RC_FORM_INTERNAL_HOOKS';\n\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nvar warningFunc = function warningFunc() {\n  warning(false, 'Can not find FormContext. Please make sure you wrap Field under Form.');\n};\nvar Context = /*#__PURE__*/React.createContext({\n  getFieldValue: warningFunc,\n  getFieldsValue: warningFunc,\n  getFieldError: warningFunc,\n  getFieldWarning: warningFunc,\n  getFieldsError: warningFunc,\n  isFieldsTouched: warningFunc,\n  isFieldTouched: warningFunc,\n  isFieldValidating: warningFunc,\n  isFieldsValidating: warningFunc,\n  resetFields: warningFunc,\n  setFields: warningFunc,\n  setFieldValue: warningFunc,\n  setFieldsValue: warningFunc,\n  validateFields: warningFunc,\n  submit: warningFunc,\n  getInternalHooks: function getInternalHooks() {\n    warningFunc();\n    return {\n      dispatch: warningFunc,\n      initEntityValue: warningFunc,\n      registerField: warningFunc,\n      useSubscribe: warningFunc,\n      setInitialValues: warningFunc,\n      destroyForm: warningFunc,\n      setCallbacks: warningFunc,\n      registerWatch: warningFunc,\n      getFields: warningFunc,\n      setValidateMessages: warningFunc,\n      setPreserve: warningFunc,\n      getInitialValue: warningFunc\n    };\n  }\n});\nexport default Context;"], "mappings": "AAAA,OAAOA,OAAO,MAAM,oBAAoB;AACxC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAO,IAAIC,SAAS,GAAG,wBAAwB;;AAE/C;AACA,IAAIC,WAAW,GAAG,SAASA,WAAWA,CAAA,EAAG;EACvCH,OAAO,CAAC,KAAK,EAAE,uEAAuE,CAAC;AACzF,CAAC;AACD,IAAII,OAAO,GAAG,aAAaH,KAAK,CAACI,aAAa,CAAC;EAC7CC,aAAa,EAAEH,WAAW;EAC1BI,cAAc,EAAEJ,WAAW;EAC3BK,aAAa,EAAEL,WAAW;EAC1BM,eAAe,EAAEN,WAAW;EAC5BO,cAAc,EAAEP,WAAW;EAC3BQ,eAAe,EAAER,WAAW;EAC5BS,cAAc,EAAET,WAAW;EAC3BU,iBAAiB,EAAEV,WAAW;EAC9BW,kBAAkB,EAAEX,WAAW;EAC/BY,WAAW,EAAEZ,WAAW;EACxBa,SAAS,EAAEb,WAAW;EACtBc,aAAa,EAAEd,WAAW;EAC1Be,cAAc,EAAEf,WAAW;EAC3BgB,cAAc,EAAEhB,WAAW;EAC3BiB,MAAM,EAAEjB,WAAW;EACnBkB,gBAAgB,EAAE,SAASA,gBAAgBA,CAAA,EAAG;IAC5ClB,WAAW,CAAC,CAAC;IACb,OAAO;MACLmB,QAAQ,EAAEnB,WAAW;MACrBoB,eAAe,EAAEpB,WAAW;MAC5BqB,aAAa,EAAErB,WAAW;MAC1BsB,YAAY,EAAEtB,WAAW;MACzBuB,gBAAgB,EAAEvB,WAAW;MAC7BwB,WAAW,EAAExB,WAAW;MACxByB,YAAY,EAAEzB,WAAW;MACzB0B,aAAa,EAAE1B,WAAW;MAC1B2B,SAAS,EAAE3B,WAAW;MACtB4B,mBAAmB,EAAE5B,WAAW;MAChC6B,WAAW,EAAE7B,WAAW;MACxB8B,eAAe,EAAE9B;IACnB,CAAC;EACH;AACF,CAAC,CAAC;AACF,eAAeC,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}