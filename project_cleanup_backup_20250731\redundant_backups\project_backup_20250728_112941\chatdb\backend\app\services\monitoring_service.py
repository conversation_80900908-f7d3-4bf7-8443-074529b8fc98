"""
监控和错误处理服务
提供系统性能监控、错误跟踪和健康检查功能
"""
import time
import logging
import traceback
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
from functools import wraps
from collections import defaultdict, deque
import threading
import psutil
import os

logger = logging.getLogger(__name__)

class MonitoringService:
    """系统监控服务"""
    
    def __init__(self):
        # 性能指标存储
        self.metrics = {
            'api_calls': defaultdict(int),
            'response_times': defaultdict(list),
            'error_counts': defaultdict(int),
            'active_connections': 0,
            'memory_usage': deque(maxlen=100),
            'cpu_usage': deque(maxlen=100),
        }
        
        # 错误日志存储
        self.error_log = deque(maxlen=1000)
        
        # 健康检查状态
        self.health_status = {
            'database': True,
            'llm_service': True,
            'cache_service': True,
            'last_check': datetime.now()
        }
        
        # 启动监控线程
        self._start_monitoring_thread()
    
    def record_api_call(self, endpoint: str, response_time: float, status_code: int):
        """记录API调用指标"""
        self.metrics['api_calls'][endpoint] += 1
        self.metrics['response_times'][endpoint].append(response_time)
        
        # 只保留最近100次调用的响应时间
        if len(self.metrics['response_times'][endpoint]) > 100:
            self.metrics['response_times'][endpoint] = self.metrics['response_times'][endpoint][-100:]
        
        # 记录错误
        if status_code >= 400:
            self.metrics['error_counts'][endpoint] += 1
    
    def record_error(self, error: Exception, context: Dict[str, Any] = None):
        """记录错误信息"""
        error_info = {
            'timestamp': datetime.now(),
            'error_type': type(error).__name__,
            'error_message': str(error),
            'traceback': traceback.format_exc(),
            'context': context or {}
        }
        self.error_log.append(error_info)
        logger.error(f"错误记录: {error_info}")
    
    def get_system_metrics(self) -> Dict[str, Any]:
        """获取系统指标"""
        # 计算平均响应时间
        avg_response_times = {}
        for endpoint, times in self.metrics['response_times'].items():
            if times:
                avg_response_times[endpoint] = sum(times) / len(times)
        
        # 获取当前系统资源使用情况
        current_memory = psutil.virtual_memory().percent
        current_cpu = psutil.cpu_percent()
        
        self.metrics['memory_usage'].append(current_memory)
        self.metrics['cpu_usage'].append(current_cpu)
        
        return {
            'api_calls': dict(self.metrics['api_calls']),
            'avg_response_times': avg_response_times,
            'error_counts': dict(self.metrics['error_counts']),
            'active_connections': self.metrics['active_connections'],
            'memory_usage': {
                'current': current_memory,
                'average': sum(self.metrics['memory_usage']) / len(self.metrics['memory_usage']) if self.metrics['memory_usage'] else 0
            },
            'cpu_usage': {
                'current': current_cpu,
                'average': sum(self.metrics['cpu_usage']) / len(self.metrics['cpu_usage']) if self.metrics['cpu_usage'] else 0
            },
            'uptime': time.time() - self.start_time if hasattr(self, 'start_time') else 0
        }
    
    def get_health_status(self) -> Dict[str, Any]:
        """获取健康检查状态"""
        return {
            **self.health_status,
            'overall_healthy': all(self.health_status[key] for key in ['database', 'llm_service', 'cache_service'])
        }
    
    def update_health_status(self, service: str, status: bool):
        """更新服务健康状态"""
        self.health_status[service] = status
        self.health_status['last_check'] = datetime.now()
    
    def get_recent_errors(self, limit: int = 50) -> List[Dict[str, Any]]:
        """获取最近的错误记录"""
        return list(self.error_log)[-limit:]
    
    def _start_monitoring_thread(self):
        """启动监控线程"""
        self.start_time = time.time()
        
        def monitor_loop():
            while True:
                try:
                    # 每分钟收集一次系统指标
                    time.sleep(60)
                    self._collect_system_metrics()
                except Exception as e:
                    logger.error(f"监控线程错误: {e}")
        
        monitor_thread = threading.Thread(target=monitor_loop, daemon=True)
        monitor_thread.start()
    
    def _collect_system_metrics(self):
        """收集系统指标"""
        try:
            # 这里可以添加更多的系统指标收集逻辑
            pass
        except Exception as e:
            logger.error(f"收集系统指标失败: {e}")

# 全局监控实例
monitoring_service = MonitoringService()

def monitor_api_call(endpoint_name: str = None):
    """API调用监控装饰器"""
    def decorator(func):
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            start_time = time.time()
            endpoint = endpoint_name or func.__name__
            status_code = 200
            
            try:
                result = await func(*args, **kwargs)
                return result
            except Exception as e:
                status_code = 500
                monitoring_service.record_error(e, {'endpoint': endpoint, 'args': str(args)[:200]})
                raise
            finally:
                response_time = time.time() - start_time
                monitoring_service.record_api_call(endpoint, response_time, status_code)
        
        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            start_time = time.time()
            endpoint = endpoint_name or func.__name__
            status_code = 200
            
            try:
                result = func(*args, **kwargs)
                return result
            except Exception as e:
                status_code = 500
                monitoring_service.record_error(e, {'endpoint': endpoint, 'args': str(args)[:200]})
                raise
            finally:
                response_time = time.time() - start_time
                monitoring_service.record_api_call(endpoint, response_time, status_code)
        
        # 检查函数是否是异步的
        import asyncio
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
    
    return decorator

def log_performance(operation_name: str):
    """性能日志装饰器"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            start_time = time.time()
            try:
                result = func(*args, **kwargs)
                execution_time = time.time() - start_time
                logger.info(f"{operation_name} 执行完成，耗时: {execution_time:.3f}秒")
                return result
            except Exception as e:
                execution_time = time.time() - start_time
                logger.error(f"{operation_name} 执行失败，耗时: {execution_time:.3f}秒，错误: {e}")
                raise
        return wrapper
    return decorator

# 健康检查函数
async def check_database_health() -> bool:
    """检查数据库健康状态"""
    try:
        from app.db.session import SessionLocal
        db = SessionLocal()
        db.execute("SELECT 1")
        db.close()
        return True
    except Exception as e:
        logger.error(f"数据库健康检查失败: {e}")
        return False

async def check_llm_service_health() -> bool:
    """检查LLM服务健康状态"""
    try:
        from app.core.llms import model_client
        # 简单的测试调用
        response = model_client.complete("测试", max_tokens=10)
        return bool(response)
    except Exception as e:
        logger.error(f"LLM服务健康检查失败: {e}")
        return False

async def check_cache_service_health() -> bool:
    """检查缓存服务健康状态"""
    try:
        from app.services.cache_service import cache_service
        # 测试缓存读写
        test_key = "health_check_test"
        cache_service.set(test_key, "test_value", ttl=10)
        result = cache_service.get(test_key)
        cache_service.delete(test_key)
        return result == "test_value"
    except Exception as e:
        logger.error(f"缓存服务健康检查失败: {e}")
        return False

async def perform_health_checks():
    """执行所有健康检查"""
    checks = {
        'database': check_database_health(),
        'llm_service': check_llm_service_health(),
        'cache_service': check_cache_service_health()
    }
    
    for service, check_coro in checks.items():
        try:
            status = await check_coro
            monitoring_service.update_health_status(service, status)
        except Exception as e:
            logger.error(f"健康检查失败 {service}: {e}")
            monitoring_service.update_health_status(service, False)
