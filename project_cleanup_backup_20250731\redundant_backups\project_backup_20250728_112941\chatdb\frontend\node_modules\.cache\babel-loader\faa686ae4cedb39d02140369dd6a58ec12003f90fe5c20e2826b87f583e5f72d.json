{"ast": null, "code": "import { createContext } from 'react';\nconst RowContext = /*#__PURE__*/createContext({});\nexport default RowContext;", "map": {"version": 3, "names": ["createContext", "RowContext"], "sources": ["C:/Users/<USER>/PycharmProjects/智能数据分析系统/chatdb/frontend/node_modules/antd/es/grid/RowContext.js"], "sourcesContent": ["import { createContext } from 'react';\nconst RowContext = /*#__PURE__*/createContext({});\nexport default RowContext;"], "mappings": "AAAA,SAASA,aAAa,QAAQ,OAAO;AACrC,MAAMC,UAAU,GAAG,aAAaD,aAAa,CAAC,CAAC,CAAC,CAAC;AACjD,eAAeC,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}