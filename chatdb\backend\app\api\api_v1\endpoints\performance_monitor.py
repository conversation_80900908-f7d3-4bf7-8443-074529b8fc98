"""
性能监控API端点
提供Text2SQL系统性能监控和诊断功能
"""
from fastapi import APIRouter, HTTPException
from typing import Dict, Any, List
import logging

from app.services.text2sql_performance_monitor import text2sql_monitor
from app.services.neo4j_connection_pool import neo4j_pool

router = APIRouter()
logger = logging.getLogger(__name__)


@router.get("/health")
async def performance_health_check():
    """性能健康检查"""
    try:
        summary = text2sql_monitor.get_performance_summary()
        
        # 检查Neo4j连接池健康状态
        neo4j_health = await neo4j_pool.health_check()
        neo4j_stats = await neo4j_pool.get_connection_stats()
        
        return {
            "status": "ok",
            "timestamp": "2025-01-31T12:00:00Z",
            "performance_summary": summary,
            "neo4j_health": {
                "healthy": neo4j_health,
                "stats": neo4j_stats
            },
            "recommendations": _generate_recommendations(summary)
        }
    except Exception as e:
        logger.error(f"性能健康检查失败: {e}")
        raise HTTPException(status_code=500, detail=f"健康检查失败: {str(e)}")


@router.get("/metrics")
async def get_performance_metrics():
    """获取详细性能指标"""
    try:
        summary = text2sql_monitor.get_performance_summary()
        return {
            "metrics": summary,
            "timestamp": "2025-01-31T12:00:00Z"
        }
    except Exception as e:
        logger.error(f"获取性能指标失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取指标失败: {str(e)}")


@router.get("/active-queries")
async def get_active_queries():
    """获取当前活动查询"""
    try:
        active_queries = text2sql_monitor.active_queries
        return {
            "active_queries": active_queries,
            "count": len(active_queries),
            "timestamp": "2025-01-31T12:00:00Z"
        }
    except Exception as e:
        logger.error(f"获取活动查询失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取活动查询失败: {str(e)}")


@router.get("/node-performance/{node_name}")
async def get_node_performance(node_name: str):
    """获取特定节点的性能数据"""
    try:
        if node_name not in text2sql_monitor.pipeline_metrics:
            raise HTTPException(status_code=404, detail=f"节点 {node_name} 不存在")
        
        metrics = text2sql_monitor.pipeline_metrics[node_name]
        
        # 计算统计信息
        if metrics:
            durations = [m['duration'] for m in metrics if m['success']]
            success_count = len(durations)
            total_count = len(metrics)
            
            stats = {
                "node_name": node_name,
                "total_calls": total_count,
                "success_calls": success_count,
                "success_rate": (success_count / total_count * 100) if total_count > 0 else 0,
                "avg_duration": sum(durations) / len(durations) if durations else 0,
                "max_duration": max(durations) if durations else 0,
                "min_duration": min(durations) if durations else 0,
                "recent_calls": list(metrics)[-10:]  # 最近10次调用
            }
        else:
            stats = {
                "node_name": node_name,
                "total_calls": 0,
                "success_calls": 0,
                "success_rate": 0,
                "avg_duration": 0,
                "max_duration": 0,
                "min_duration": 0,
                "recent_calls": []
            }
        
        return stats
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取节点性能失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取节点性能失败: {str(e)}")


@router.post("/reset-metrics")
async def reset_performance_metrics():
    """重置性能指标"""
    try:
        # 清空所有指标
        for metrics in text2sql_monitor.pipeline_metrics.values():
            metrics.clear()
        
        for metrics in text2sql_monitor.system_metrics.values():
            metrics.clear()
        
        text2sql_monitor.error_stats.clear()
        
        return {
            "status": "success",
            "message": "性能指标已重置",
            "timestamp": "2025-01-31T12:00:00Z"
        }
    except Exception as e:
        logger.error(f"重置性能指标失败: {e}")
        raise HTTPException(status_code=500, detail=f"重置指标失败: {str(e)}")


@router.get("/diagnostics")
async def run_diagnostics():
    """运行系统诊断"""
    try:
        diagnostics = {
            "timestamp": "2025-01-31T12:00:00Z",
            "checks": {}
        }
        
        # 检查1: 内存使用
        summary = text2sql_monitor.get_performance_summary()
        memory_usage = summary.get('system_metrics', {}).get('memory_usage', {}).get('current', 0)
        
        diagnostics["checks"]["memory_usage"] = {
            "status": "critical" if memory_usage > 90 else "warning" if memory_usage > 80 else "ok",
            "value": memory_usage,
            "message": f"当前内存使用率: {memory_usage:.1f}%"
        }
        
        # 检查2: 活动查询数量
        active_count = len(text2sql_monitor.active_queries)
        diagnostics["checks"]["active_queries"] = {
            "status": "warning" if active_count > 10 else "ok",
            "value": active_count,
            "message": f"当前活动查询数量: {active_count}"
        }
        
        # 检查3: Neo4j连接健康
        neo4j_health = await neo4j_pool.health_check()
        diagnostics["checks"]["neo4j_connection"] = {
            "status": "ok" if neo4j_health else "critical",
            "value": neo4j_health,
            "message": "Neo4j连接正常" if neo4j_health else "Neo4j连接异常"
        }
        
        # 检查4: 节点性能
        slow_nodes = []
        for node_name, metrics in text2sql_monitor.pipeline_metrics.items():
            if metrics:
                recent_durations = [m['duration'] for m in list(metrics)[-5:] if m['success']]
                if recent_durations:
                    avg_duration = sum(recent_durations) / len(recent_durations)
                    if avg_duration > text2sql_monitor.thresholds['node_warning_time']:
                        slow_nodes.append({
                            "node": node_name,
                            "avg_duration": avg_duration
                        })
        
        diagnostics["checks"]["node_performance"] = {
            "status": "warning" if slow_nodes else "ok",
            "value": slow_nodes,
            "message": f"发现{len(slow_nodes)}个慢节点" if slow_nodes else "所有节点性能正常"
        }
        
        # 总体健康状态
        all_statuses = [check["status"] for check in diagnostics["checks"].values()]
        if "critical" in all_statuses:
            diagnostics["overall_status"] = "critical"
        elif "warning" in all_statuses:
            diagnostics["overall_status"] = "warning"
        else:
            diagnostics["overall_status"] = "healthy"
        
        return diagnostics
        
    except Exception as e:
        logger.error(f"运行诊断失败: {e}")
        raise HTTPException(status_code=500, detail=f"诊断失败: {str(e)}")


def _generate_recommendations(summary: Dict[str, Any]) -> List[str]:
    """基于性能摘要生成优化建议"""
    recommendations = []
    
    # 内存使用建议
    memory_usage = summary.get('system_metrics', {}).get('memory_usage', {}).get('current', 0)
    if memory_usage > 90:
        recommendations.append("内存使用率过高，建议重启服务或增加内存")
    elif memory_usage > 80:
        recommendations.append("内存使用率较高，建议清理缓存或优化查询")
    
    # 活动查询建议
    active_queries = summary.get('active_queries', 0)
    if active_queries > 10:
        recommendations.append("活动查询数量过多，可能存在查询阻塞问题")
    
    # 节点性能建议
    node_performance = summary.get('node_performance', {})
    for node_name, stats in node_performance.items():
        if stats.get('avg_duration', 0) > 10:
            recommendations.append(f"节点{node_name}平均响应时间过长，建议优化")
        if stats.get('success_rate', 100) < 95:
            recommendations.append(f"节点{node_name}成功率较低，建议检查错误日志")
    
    # 错误统计建议
    error_stats = summary.get('error_stats', {})
    if error_stats:
        total_errors = sum(error_stats.values())
        if total_errors > 10:
            recommendations.append("系统错误较多，建议检查日志并修复问题")
    
    if not recommendations:
        recommendations.append("系统运行正常，无需特别优化")
    
    return recommendations
