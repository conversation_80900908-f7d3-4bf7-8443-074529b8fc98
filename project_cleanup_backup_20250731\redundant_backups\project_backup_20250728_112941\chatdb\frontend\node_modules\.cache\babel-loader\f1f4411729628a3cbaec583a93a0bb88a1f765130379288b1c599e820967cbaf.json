{"ast": null, "code": "\"use client\";\n\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport React, { useEffect } from 'react';\nimport { useMutateObserver } from '@rc-component/mutate-observer';\nimport classNames from 'classnames';\nimport useEvent from \"rc-util/es/hooks/useEvent\";\nimport toList from '../_util/toList';\nimport { useToken } from '../theme/internal';\nimport WatermarkContext from './context';\nimport useClips, { FontGap } from './useClips';\nimport useRafDebounce from './useRafDebounce';\nimport useSingletonCache from './useSingletonCache';\nimport useWatermark from './useWatermark';\nimport { getPixelRatio, reRendering } from './utils';\n/**\n * Only return `next` when size changed.\n * This is only used for elements compare, not a shallow equal!\n */\nfunction getSizeDiff(prev, next) {\n  return prev.size === next.size ? prev : next;\n}\nconst DEFAULT_GAP_X = 100;\nconst DEFAULT_GAP_Y = 100;\nconst fixedStyle = {\n  position: 'relative',\n  overflow: 'hidden'\n};\nconst Watermark = props => {\n  var _a, _b;\n  const {\n    /**\n     * The antd content layer zIndex is basically below 10\n     * https://github.com/ant-design/ant-design/blob/6192403b2ce517c017f9e58a32d58774921c10cd/components/style/themes/default.less#L335\n     */\n    zIndex = 9,\n    rotate = -22,\n    width,\n    height,\n    image,\n    content,\n    font = {},\n    style,\n    className,\n    rootClassName,\n    gap = [DEFAULT_GAP_X, DEFAULT_GAP_Y],\n    offset,\n    children,\n    inherit = true\n  } = props;\n  const mergedStyle = Object.assign(Object.assign({}, fixedStyle), style);\n  const [, token] = useToken();\n  const {\n    color = token.colorFill,\n    fontSize = token.fontSizeLG,\n    fontWeight = 'normal',\n    fontStyle = 'normal',\n    fontFamily = 'sans-serif',\n    textAlign = 'center'\n  } = font;\n  const [gapX = DEFAULT_GAP_X, gapY = DEFAULT_GAP_Y] = gap;\n  const gapXCenter = gapX / 2;\n  const gapYCenter = gapY / 2;\n  const offsetLeft = (_a = offset === null || offset === void 0 ? void 0 : offset[0]) !== null && _a !== void 0 ? _a : gapXCenter;\n  const offsetTop = (_b = offset === null || offset === void 0 ? void 0 : offset[1]) !== null && _b !== void 0 ? _b : gapYCenter;\n  const markStyle = React.useMemo(() => {\n    const mergedMarkStyle = {\n      zIndex,\n      position: 'absolute',\n      left: 0,\n      top: 0,\n      width: '100%',\n      height: '100%',\n      pointerEvents: 'none',\n      backgroundRepeat: 'repeat'\n    };\n    /** Calculate the style of the offset */\n    let positionLeft = offsetLeft - gapXCenter;\n    let positionTop = offsetTop - gapYCenter;\n    if (positionLeft > 0) {\n      mergedMarkStyle.left = `${positionLeft}px`;\n      mergedMarkStyle.width = `calc(100% - ${positionLeft}px)`;\n      positionLeft = 0;\n    }\n    if (positionTop > 0) {\n      mergedMarkStyle.top = `${positionTop}px`;\n      mergedMarkStyle.height = `calc(100% - ${positionTop}px)`;\n      positionTop = 0;\n    }\n    mergedMarkStyle.backgroundPosition = `${positionLeft}px ${positionTop}px`;\n    return mergedMarkStyle;\n  }, [zIndex, offsetLeft, gapXCenter, offsetTop, gapYCenter]);\n  const [container, setContainer] = React.useState();\n  // Used for nest case like Modal, Drawer\n  const [subElements, setSubElements] = React.useState(() => new Set());\n  // Nest elements should also support watermark\n  const targetElements = React.useMemo(() => {\n    const list = container ? [container] : [];\n    return [].concat(list, _toConsumableArray(Array.from(subElements)));\n  }, [container, subElements]);\n  // ============================ Content =============================\n  /**\n   * Get the width and height of the watermark. The default values are as follows\n   * Image: [120, 64]; Content: It's calculated by content;\n   */\n  const getMarkSize = ctx => {\n    let defaultWidth = 120;\n    let defaultHeight = 64;\n    if (!image && ctx.measureText) {\n      ctx.font = `${Number(fontSize)}px ${fontFamily}`;\n      const contents = toList(content);\n      const sizes = contents.map(item => {\n        const metrics = ctx.measureText(item);\n        return [metrics.width, metrics.fontBoundingBoxAscent + metrics.fontBoundingBoxDescent];\n      });\n      defaultWidth = Math.ceil(Math.max.apply(Math, _toConsumableArray(sizes.map(size => size[0]))));\n      defaultHeight = Math.ceil(Math.max.apply(Math, _toConsumableArray(sizes.map(size => size[1])))) * contents.length + (contents.length - 1) * FontGap;\n    }\n    return [width !== null && width !== void 0 ? width : defaultWidth, height !== null && height !== void 0 ? height : defaultHeight];\n  };\n  const getClips = useClips();\n  const getClipsCache = useSingletonCache();\n  const [watermarkInfo, setWatermarkInfo] = React.useState(null);\n  // Generate new Watermark content\n  const renderWatermark = () => {\n    const canvas = document.createElement('canvas');\n    const ctx = canvas.getContext('2d');\n    if (ctx) {\n      const ratio = getPixelRatio();\n      const [markWidth, markHeight] = getMarkSize(ctx);\n      const drawCanvas = drawContent => {\n        const params = [drawContent || '', rotate, ratio, markWidth, markHeight, {\n          color,\n          fontSize,\n          fontStyle,\n          fontWeight,\n          fontFamily,\n          textAlign\n        }, gapX, gapY];\n        const [nextClips, clipWidth] = getClipsCache(params, () => getClips.apply(void 0, params));\n        setWatermarkInfo([nextClips, clipWidth]);\n      };\n      if (image) {\n        const img = new Image();\n        img.onload = () => {\n          drawCanvas(img);\n        };\n        img.onerror = () => {\n          drawCanvas(content);\n        };\n        img.crossOrigin = 'anonymous';\n        img.referrerPolicy = 'no-referrer';\n        img.src = image;\n      } else {\n        drawCanvas(content);\n      }\n    }\n  };\n  const syncWatermark = useRafDebounce(renderWatermark);\n  // ============================= Effect =============================\n  // Append watermark to the container\n  const [appendWatermark, removeWatermark, isWatermarkEle] = useWatermark(markStyle);\n  useEffect(() => {\n    if (watermarkInfo) {\n      targetElements.forEach(holder => {\n        appendWatermark(watermarkInfo[0], watermarkInfo[1], holder);\n      });\n    }\n  }, [watermarkInfo, targetElements]);\n  // ============================ Observe =============================\n  const onMutate = useEvent(mutations => {\n    mutations.forEach(mutation => {\n      if (reRendering(mutation, isWatermarkEle)) {\n        syncWatermark();\n      } else if (mutation.target === container && mutation.attributeName === 'style') {\n        // We've only force container not modify.\n        // Not consider nest case.\n        const keyStyles = Object.keys(fixedStyle);\n        for (let i = 0; i < keyStyles.length; i += 1) {\n          const key = keyStyles[i];\n          const oriValue = mergedStyle[key];\n          const currentValue = container.style[key];\n          if (oriValue && oriValue !== currentValue) {\n            container.style[key] = oriValue;\n          }\n        }\n      }\n    });\n  });\n  useMutateObserver(targetElements, onMutate);\n  useEffect(syncWatermark, [rotate, zIndex, width, height, image, content, color, fontSize, fontWeight, fontStyle, fontFamily, textAlign, gapX, gapY, offsetLeft, offsetTop]);\n  // ============================ Context =============================\n  const watermarkContext = React.useMemo(() => ({\n    add: ele => {\n      setSubElements(prev => {\n        const clone = new Set(prev);\n        clone.add(ele);\n        return getSizeDiff(prev, clone);\n      });\n    },\n    remove: ele => {\n      removeWatermark(ele);\n      setSubElements(prev => {\n        const clone = new Set(prev);\n        clone.delete(ele);\n        return getSizeDiff(prev, clone);\n      });\n    }\n  }), []);\n  // ============================= Render =============================\n  const childNode = inherit ? (/*#__PURE__*/React.createElement(WatermarkContext.Provider, {\n    value: watermarkContext\n  }, children)) : children;\n  return /*#__PURE__*/React.createElement(\"div\", {\n    ref: setContainer,\n    className: classNames(className, rootClassName),\n    style: mergedStyle\n  }, childNode);\n};\nif (process.env.NODE_ENV !== 'production') {\n  Watermark.displayName = 'Watermark';\n}\nexport default Watermark;", "map": {"version": 3, "names": ["_toConsumableArray", "React", "useEffect", "useMutateObserver", "classNames", "useEvent", "toList", "useToken", "WatermarkContext", "useClips", "FontGap", "useRafDebounce", "useSingletonCache", "useWatermark", "getPixelRatio", "reRendering", "getSizeDiff", "prev", "next", "size", "DEFAULT_GAP_X", "DEFAULT_GAP_Y", "fixedStyle", "position", "overflow", "Watermark", "props", "_a", "_b", "zIndex", "rotate", "width", "height", "image", "content", "font", "style", "className", "rootClassName", "gap", "offset", "children", "inherit", "mergedStyle", "Object", "assign", "token", "color", "colorFill", "fontSize", "fontSizeLG", "fontWeight", "fontStyle", "fontFamily", "textAlign", "gapX", "gapY", "gapXCenter", "gapYCenter", "offsetLeft", "offsetTop", "mark<PERSON><PERSON><PERSON>", "useMemo", "mergedMarkStyle", "left", "top", "pointerEvents", "backgroundRepeat", "positionLeft", "positionTop", "backgroundPosition", "container", "<PERSON><PERSON><PERSON><PERSON>", "useState", "subElements", "setSubElements", "Set", "targetElements", "list", "concat", "Array", "from", "getMarkSize", "ctx", "defaultWidth", "defaultHeight", "measureText", "Number", "contents", "sizes", "map", "item", "metrics", "fontBoundingBoxAscent", "fontBoundingBoxDescent", "Math", "ceil", "max", "apply", "length", "getClips", "getClipsCache", "watermarkInfo", "setWatermarkInfo", "renderWatermark", "canvas", "document", "createElement", "getContext", "ratio", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "draw<PERSON><PERSON>vas", "drawContent", "params", "nextClips", "clipWidth", "img", "Image", "onload", "onerror", "crossOrigin", "referrerPolicy", "src", "syncWatermark", "appendWatermark", "removeWatermark", "isWatermarkEle", "for<PERSON>ach", "holder", "onMutate", "mutations", "mutation", "target", "attributeName", "keyStyles", "keys", "i", "key", "oriValue", "currentValue", "watermarkContext", "add", "ele", "clone", "remove", "delete", "childNode", "Provider", "value", "ref", "process", "env", "NODE_ENV", "displayName"], "sources": ["C:/Users/<USER>/PycharmProjects/智能数据分析系统/chatdb/frontend/node_modules/antd/es/watermark/index.js"], "sourcesContent": ["\"use client\";\n\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport React, { useEffect } from 'react';\nimport { useMutateObserver } from '@rc-component/mutate-observer';\nimport classNames from 'classnames';\nimport useEvent from \"rc-util/es/hooks/useEvent\";\nimport toList from '../_util/toList';\nimport { useToken } from '../theme/internal';\nimport WatermarkContext from './context';\nimport useClips, { FontGap } from './useClips';\nimport useRafDebounce from './useRafDebounce';\nimport useSingletonCache from './useSingletonCache';\nimport useWatermark from './useWatermark';\nimport { getPixelRatio, reRendering } from './utils';\n/**\n * Only return `next` when size changed.\n * This is only used for elements compare, not a shallow equal!\n */\nfunction getSizeDiff(prev, next) {\n  return prev.size === next.size ? prev : next;\n}\nconst DEFAULT_GAP_X = 100;\nconst DEFAULT_GAP_Y = 100;\nconst fixedStyle = {\n  position: 'relative',\n  overflow: 'hidden'\n};\nconst Watermark = props => {\n  var _a, _b;\n  const {\n    /**\n     * The antd content layer zIndex is basically below 10\n     * https://github.com/ant-design/ant-design/blob/6192403b2ce517c017f9e58a32d58774921c10cd/components/style/themes/default.less#L335\n     */\n    zIndex = 9,\n    rotate = -22,\n    width,\n    height,\n    image,\n    content,\n    font = {},\n    style,\n    className,\n    rootClassName,\n    gap = [DEFAULT_GAP_X, DEFAULT_GAP_Y],\n    offset,\n    children,\n    inherit = true\n  } = props;\n  const mergedStyle = Object.assign(Object.assign({}, fixedStyle), style);\n  const [, token] = useToken();\n  const {\n    color = token.colorFill,\n    fontSize = token.fontSizeLG,\n    fontWeight = 'normal',\n    fontStyle = 'normal',\n    fontFamily = 'sans-serif',\n    textAlign = 'center'\n  } = font;\n  const [gapX = DEFAULT_GAP_X, gapY = DEFAULT_GAP_Y] = gap;\n  const gapXCenter = gapX / 2;\n  const gapYCenter = gapY / 2;\n  const offsetLeft = (_a = offset === null || offset === void 0 ? void 0 : offset[0]) !== null && _a !== void 0 ? _a : gapXCenter;\n  const offsetTop = (_b = offset === null || offset === void 0 ? void 0 : offset[1]) !== null && _b !== void 0 ? _b : gapYCenter;\n  const markStyle = React.useMemo(() => {\n    const mergedMarkStyle = {\n      zIndex,\n      position: 'absolute',\n      left: 0,\n      top: 0,\n      width: '100%',\n      height: '100%',\n      pointerEvents: 'none',\n      backgroundRepeat: 'repeat'\n    };\n    /** Calculate the style of the offset */\n    let positionLeft = offsetLeft - gapXCenter;\n    let positionTop = offsetTop - gapYCenter;\n    if (positionLeft > 0) {\n      mergedMarkStyle.left = `${positionLeft}px`;\n      mergedMarkStyle.width = `calc(100% - ${positionLeft}px)`;\n      positionLeft = 0;\n    }\n    if (positionTop > 0) {\n      mergedMarkStyle.top = `${positionTop}px`;\n      mergedMarkStyle.height = `calc(100% - ${positionTop}px)`;\n      positionTop = 0;\n    }\n    mergedMarkStyle.backgroundPosition = `${positionLeft}px ${positionTop}px`;\n    return mergedMarkStyle;\n  }, [zIndex, offsetLeft, gapXCenter, offsetTop, gapYCenter]);\n  const [container, setContainer] = React.useState();\n  // Used for nest case like Modal, Drawer\n  const [subElements, setSubElements] = React.useState(() => new Set());\n  // Nest elements should also support watermark\n  const targetElements = React.useMemo(() => {\n    const list = container ? [container] : [];\n    return [].concat(list, _toConsumableArray(Array.from(subElements)));\n  }, [container, subElements]);\n  // ============================ Content =============================\n  /**\n   * Get the width and height of the watermark. The default values are as follows\n   * Image: [120, 64]; Content: It's calculated by content;\n   */\n  const getMarkSize = ctx => {\n    let defaultWidth = 120;\n    let defaultHeight = 64;\n    if (!image && ctx.measureText) {\n      ctx.font = `${Number(fontSize)}px ${fontFamily}`;\n      const contents = toList(content);\n      const sizes = contents.map(item => {\n        const metrics = ctx.measureText(item);\n        return [metrics.width, metrics.fontBoundingBoxAscent + metrics.fontBoundingBoxDescent];\n      });\n      defaultWidth = Math.ceil(Math.max.apply(Math, _toConsumableArray(sizes.map(size => size[0]))));\n      defaultHeight = Math.ceil(Math.max.apply(Math, _toConsumableArray(sizes.map(size => size[1])))) * contents.length + (contents.length - 1) * FontGap;\n    }\n    return [width !== null && width !== void 0 ? width : defaultWidth, height !== null && height !== void 0 ? height : defaultHeight];\n  };\n  const getClips = useClips();\n  const getClipsCache = useSingletonCache();\n  const [watermarkInfo, setWatermarkInfo] = React.useState(null);\n  // Generate new Watermark content\n  const renderWatermark = () => {\n    const canvas = document.createElement('canvas');\n    const ctx = canvas.getContext('2d');\n    if (ctx) {\n      const ratio = getPixelRatio();\n      const [markWidth, markHeight] = getMarkSize(ctx);\n      const drawCanvas = drawContent => {\n        const params = [drawContent || '', rotate, ratio, markWidth, markHeight, {\n          color,\n          fontSize,\n          fontStyle,\n          fontWeight,\n          fontFamily,\n          textAlign\n        }, gapX, gapY];\n        const [nextClips, clipWidth] = getClipsCache(params, () => getClips.apply(void 0, params));\n        setWatermarkInfo([nextClips, clipWidth]);\n      };\n      if (image) {\n        const img = new Image();\n        img.onload = () => {\n          drawCanvas(img);\n        };\n        img.onerror = () => {\n          drawCanvas(content);\n        };\n        img.crossOrigin = 'anonymous';\n        img.referrerPolicy = 'no-referrer';\n        img.src = image;\n      } else {\n        drawCanvas(content);\n      }\n    }\n  };\n  const syncWatermark = useRafDebounce(renderWatermark);\n  // ============================= Effect =============================\n  // Append watermark to the container\n  const [appendWatermark, removeWatermark, isWatermarkEle] = useWatermark(markStyle);\n  useEffect(() => {\n    if (watermarkInfo) {\n      targetElements.forEach(holder => {\n        appendWatermark(watermarkInfo[0], watermarkInfo[1], holder);\n      });\n    }\n  }, [watermarkInfo, targetElements]);\n  // ============================ Observe =============================\n  const onMutate = useEvent(mutations => {\n    mutations.forEach(mutation => {\n      if (reRendering(mutation, isWatermarkEle)) {\n        syncWatermark();\n      } else if (mutation.target === container && mutation.attributeName === 'style') {\n        // We've only force container not modify.\n        // Not consider nest case.\n        const keyStyles = Object.keys(fixedStyle);\n        for (let i = 0; i < keyStyles.length; i += 1) {\n          const key = keyStyles[i];\n          const oriValue = mergedStyle[key];\n          const currentValue = container.style[key];\n          if (oriValue && oriValue !== currentValue) {\n            container.style[key] = oriValue;\n          }\n        }\n      }\n    });\n  });\n  useMutateObserver(targetElements, onMutate);\n  useEffect(syncWatermark, [rotate, zIndex, width, height, image, content, color, fontSize, fontWeight, fontStyle, fontFamily, textAlign, gapX, gapY, offsetLeft, offsetTop]);\n  // ============================ Context =============================\n  const watermarkContext = React.useMemo(() => ({\n    add: ele => {\n      setSubElements(prev => {\n        const clone = new Set(prev);\n        clone.add(ele);\n        return getSizeDiff(prev, clone);\n      });\n    },\n    remove: ele => {\n      removeWatermark(ele);\n      setSubElements(prev => {\n        const clone = new Set(prev);\n        clone.delete(ele);\n        return getSizeDiff(prev, clone);\n      });\n    }\n  }), []);\n  // ============================= Render =============================\n  const childNode = inherit ? (/*#__PURE__*/React.createElement(WatermarkContext.Provider, {\n    value: watermarkContext\n  }, children)) : children;\n  return /*#__PURE__*/React.createElement(\"div\", {\n    ref: setContainer,\n    className: classNames(className, rootClassName),\n    style: mergedStyle\n  }, childNode);\n};\nif (process.env.NODE_ENV !== 'production') {\n  Watermark.displayName = 'Watermark';\n}\nexport default Watermark;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,kBAAkB,MAAM,8CAA8C;AAC7E,OAAOC,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,SAASC,iBAAiB,QAAQ,+BAA+B;AACjE,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,QAAQ,MAAM,2BAA2B;AAChD,OAAOC,MAAM,MAAM,iBAAiB;AACpC,SAASC,QAAQ,QAAQ,mBAAmB;AAC5C,OAAOC,gBAAgB,MAAM,WAAW;AACxC,OAAOC,QAAQ,IAAIC,OAAO,QAAQ,YAAY;AAC9C,OAAOC,cAAc,MAAM,kBAAkB;AAC7C,OAAOC,iBAAiB,MAAM,qBAAqB;AACnD,OAAOC,YAAY,MAAM,gBAAgB;AACzC,SAASC,aAAa,EAAEC,WAAW,QAAQ,SAAS;AACpD;AACA;AACA;AACA;AACA,SAASC,WAAWA,CAACC,IAAI,EAAEC,IAAI,EAAE;EAC/B,OAAOD,IAAI,CAACE,IAAI,KAAKD,IAAI,CAACC,IAAI,GAAGF,IAAI,GAAGC,IAAI;AAC9C;AACA,MAAME,aAAa,GAAG,GAAG;AACzB,MAAMC,aAAa,GAAG,GAAG;AACzB,MAAMC,UAAU,GAAG;EACjBC,QAAQ,EAAE,UAAU;EACpBC,QAAQ,EAAE;AACZ,CAAC;AACD,MAAMC,SAAS,GAAGC,KAAK,IAAI;EACzB,IAAIC,EAAE,EAAEC,EAAE;EACV,MAAM;IACJ;AACJ;AACA;AACA;IACIC,MAAM,GAAG,CAAC;IACVC,MAAM,GAAG,CAAC,EAAE;IACZC,KAAK;IACLC,MAAM;IACNC,KAAK;IACLC,OAAO;IACPC,IAAI,GAAG,CAAC,CAAC;IACTC,KAAK;IACLC,SAAS;IACTC,aAAa;IACbC,GAAG,GAAG,CAACnB,aAAa,EAAEC,aAAa,CAAC;IACpCmB,MAAM;IACNC,QAAQ;IACRC,OAAO,GAAG;EACZ,CAAC,GAAGhB,KAAK;EACT,MAAMiB,WAAW,GAAGC,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEvB,UAAU,CAAC,EAAEc,KAAK,CAAC;EACvE,MAAM,GAAGU,KAAK,CAAC,GAAGvC,QAAQ,CAAC,CAAC;EAC5B,MAAM;IACJwC,KAAK,GAAGD,KAAK,CAACE,SAAS;IACvBC,QAAQ,GAAGH,KAAK,CAACI,UAAU;IAC3BC,UAAU,GAAG,QAAQ;IACrBC,SAAS,GAAG,QAAQ;IACpBC,UAAU,GAAG,YAAY;IACzBC,SAAS,GAAG;EACd,CAAC,GAAGnB,IAAI;EACR,MAAM,CAACoB,IAAI,GAAGnC,aAAa,EAAEoC,IAAI,GAAGnC,aAAa,CAAC,GAAGkB,GAAG;EACxD,MAAMkB,UAAU,GAAGF,IAAI,GAAG,CAAC;EAC3B,MAAMG,UAAU,GAAGF,IAAI,GAAG,CAAC;EAC3B,MAAMG,UAAU,GAAG,CAAChC,EAAE,GAAGa,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAAC,CAAC,CAAC,MAAM,IAAI,IAAIb,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG8B,UAAU;EAC/H,MAAMG,SAAS,GAAG,CAAChC,EAAE,GAAGY,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAAC,CAAC,CAAC,MAAM,IAAI,IAAIZ,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG8B,UAAU;EAC9H,MAAMG,SAAS,GAAG5D,KAAK,CAAC6D,OAAO,CAAC,MAAM;IACpC,MAAMC,eAAe,GAAG;MACtBlC,MAAM;MACNN,QAAQ,EAAE,UAAU;MACpByC,IAAI,EAAE,CAAC;MACPC,GAAG,EAAE,CAAC;MACNlC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,MAAM;MACdkC,aAAa,EAAE,MAAM;MACrBC,gBAAgB,EAAE;IACpB,CAAC;IACD;IACA,IAAIC,YAAY,GAAGT,UAAU,GAAGF,UAAU;IAC1C,IAAIY,WAAW,GAAGT,SAAS,GAAGF,UAAU;IACxC,IAAIU,YAAY,GAAG,CAAC,EAAE;MACpBL,eAAe,CAACC,IAAI,GAAG,GAAGI,YAAY,IAAI;MAC1CL,eAAe,CAAChC,KAAK,GAAG,eAAeqC,YAAY,KAAK;MACxDA,YAAY,GAAG,CAAC;IAClB;IACA,IAAIC,WAAW,GAAG,CAAC,EAAE;MACnBN,eAAe,CAACE,GAAG,GAAG,GAAGI,WAAW,IAAI;MACxCN,eAAe,CAAC/B,MAAM,GAAG,eAAeqC,WAAW,KAAK;MACxDA,WAAW,GAAG,CAAC;IACjB;IACAN,eAAe,CAACO,kBAAkB,GAAG,GAAGF,YAAY,MAAMC,WAAW,IAAI;IACzE,OAAON,eAAe;EACxB,CAAC,EAAE,CAAClC,MAAM,EAAE8B,UAAU,EAAEF,UAAU,EAAEG,SAAS,EAAEF,UAAU,CAAC,CAAC;EAC3D,MAAM,CAACa,SAAS,EAAEC,YAAY,CAAC,GAAGvE,KAAK,CAACwE,QAAQ,CAAC,CAAC;EAClD;EACA,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAG1E,KAAK,CAACwE,QAAQ,CAAC,MAAM,IAAIG,GAAG,CAAC,CAAC,CAAC;EACrE;EACA,MAAMC,cAAc,GAAG5E,KAAK,CAAC6D,OAAO,CAAC,MAAM;IACzC,MAAMgB,IAAI,GAAGP,SAAS,GAAG,CAACA,SAAS,CAAC,GAAG,EAAE;IACzC,OAAO,EAAE,CAACQ,MAAM,CAACD,IAAI,EAAE9E,kBAAkB,CAACgF,KAAK,CAACC,IAAI,CAACP,WAAW,CAAC,CAAC,CAAC;EACrE,CAAC,EAAE,CAACH,SAAS,EAAEG,WAAW,CAAC,CAAC;EAC5B;EACA;AACF;AACA;AACA;EACE,MAAMQ,WAAW,GAAGC,GAAG,IAAI;IACzB,IAAIC,YAAY,GAAG,GAAG;IACtB,IAAIC,aAAa,GAAG,EAAE;IACtB,IAAI,CAACpD,KAAK,IAAIkD,GAAG,CAACG,WAAW,EAAE;MAC7BH,GAAG,CAAChD,IAAI,GAAG,GAAGoD,MAAM,CAACtC,QAAQ,CAAC,MAAMI,UAAU,EAAE;MAChD,MAAMmC,QAAQ,GAAGlF,MAAM,CAAC4B,OAAO,CAAC;MAChC,MAAMuD,KAAK,GAAGD,QAAQ,CAACE,GAAG,CAACC,IAAI,IAAI;QACjC,MAAMC,OAAO,GAAGT,GAAG,CAACG,WAAW,CAACK,IAAI,CAAC;QACrC,OAAO,CAACC,OAAO,CAAC7D,KAAK,EAAE6D,OAAO,CAACC,qBAAqB,GAAGD,OAAO,CAACE,sBAAsB,CAAC;MACxF,CAAC,CAAC;MACFV,YAAY,GAAGW,IAAI,CAACC,IAAI,CAACD,IAAI,CAACE,GAAG,CAACC,KAAK,CAACH,IAAI,EAAE/F,kBAAkB,CAACyF,KAAK,CAACC,GAAG,CAACvE,IAAI,IAAIA,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9FkE,aAAa,GAAGU,IAAI,CAACC,IAAI,CAACD,IAAI,CAACE,GAAG,CAACC,KAAK,CAACH,IAAI,EAAE/F,kBAAkB,CAACyF,KAAK,CAACC,GAAG,CAACvE,IAAI,IAAIA,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGqE,QAAQ,CAACW,MAAM,GAAG,CAACX,QAAQ,CAACW,MAAM,GAAG,CAAC,IAAIzF,OAAO;IACrJ;IACA,OAAO,CAACqB,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAGA,KAAK,GAAGqD,YAAY,EAAEpD,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAGA,MAAM,GAAGqD,aAAa,CAAC;EACnI,CAAC;EACD,MAAMe,QAAQ,GAAG3F,QAAQ,CAAC,CAAC;EAC3B,MAAM4F,aAAa,GAAGzF,iBAAiB,CAAC,CAAC;EACzC,MAAM,CAAC0F,aAAa,EAAEC,gBAAgB,CAAC,GAAGtG,KAAK,CAACwE,QAAQ,CAAC,IAAI,CAAC;EAC9D;EACA,MAAM+B,eAAe,GAAGA,CAAA,KAAM;IAC5B,MAAMC,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;IAC/C,MAAMxB,GAAG,GAAGsB,MAAM,CAACG,UAAU,CAAC,IAAI,CAAC;IACnC,IAAIzB,GAAG,EAAE;MACP,MAAM0B,KAAK,GAAG/F,aAAa,CAAC,CAAC;MAC7B,MAAM,CAACgG,SAAS,EAAEC,UAAU,CAAC,GAAG7B,WAAW,CAACC,GAAG,CAAC;MAChD,MAAM6B,UAAU,GAAGC,WAAW,IAAI;QAChC,MAAMC,MAAM,GAAG,CAACD,WAAW,IAAI,EAAE,EAAEnF,MAAM,EAAE+E,KAAK,EAAEC,SAAS,EAAEC,UAAU,EAAE;UACvEhE,KAAK;UACLE,QAAQ;UACRG,SAAS;UACTD,UAAU;UACVE,UAAU;UACVC;QACF,CAAC,EAAEC,IAAI,EAAEC,IAAI,CAAC;QACd,MAAM,CAAC2D,SAAS,EAAEC,SAAS,CAAC,GAAGf,aAAa,CAACa,MAAM,EAAE,MAAMd,QAAQ,CAACF,KAAK,CAAC,KAAK,CAAC,EAAEgB,MAAM,CAAC,CAAC;QAC1FX,gBAAgB,CAAC,CAACY,SAAS,EAAEC,SAAS,CAAC,CAAC;MAC1C,CAAC;MACD,IAAInF,KAAK,EAAE;QACT,MAAMoF,GAAG,GAAG,IAAIC,KAAK,CAAC,CAAC;QACvBD,GAAG,CAACE,MAAM,GAAG,MAAM;UACjBP,UAAU,CAACK,GAAG,CAAC;QACjB,CAAC;QACDA,GAAG,CAACG,OAAO,GAAG,MAAM;UAClBR,UAAU,CAAC9E,OAAO,CAAC;QACrB,CAAC;QACDmF,GAAG,CAACI,WAAW,GAAG,WAAW;QAC7BJ,GAAG,CAACK,cAAc,GAAG,aAAa;QAClCL,GAAG,CAACM,GAAG,GAAG1F,KAAK;MACjB,CAAC,MAAM;QACL+E,UAAU,CAAC9E,OAAO,CAAC;MACrB;IACF;EACF,CAAC;EACD,MAAM0F,aAAa,GAAGjH,cAAc,CAAC6F,eAAe,CAAC;EACrD;EACA;EACA,MAAM,CAACqB,eAAe,EAAEC,eAAe,EAAEC,cAAc,CAAC,GAAGlH,YAAY,CAACgD,SAAS,CAAC;EAClF3D,SAAS,CAAC,MAAM;IACd,IAAIoG,aAAa,EAAE;MACjBzB,cAAc,CAACmD,OAAO,CAACC,MAAM,IAAI;QAC/BJ,eAAe,CAACvB,aAAa,CAAC,CAAC,CAAC,EAAEA,aAAa,CAAC,CAAC,CAAC,EAAE2B,MAAM,CAAC;MAC7D,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAAC3B,aAAa,EAAEzB,cAAc,CAAC,CAAC;EACnC;EACA,MAAMqD,QAAQ,GAAG7H,QAAQ,CAAC8H,SAAS,IAAI;IACrCA,SAAS,CAACH,OAAO,CAACI,QAAQ,IAAI;MAC5B,IAAIrH,WAAW,CAACqH,QAAQ,EAAEL,cAAc,CAAC,EAAE;QACzCH,aAAa,CAAC,CAAC;MACjB,CAAC,MAAM,IAAIQ,QAAQ,CAACC,MAAM,KAAK9D,SAAS,IAAI6D,QAAQ,CAACE,aAAa,KAAK,OAAO,EAAE;QAC9E;QACA;QACA,MAAMC,SAAS,GAAG3F,MAAM,CAAC4F,IAAI,CAAClH,UAAU,CAAC;QACzC,KAAK,IAAImH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,SAAS,CAACpC,MAAM,EAAEsC,CAAC,IAAI,CAAC,EAAE;UAC5C,MAAMC,GAAG,GAAGH,SAAS,CAACE,CAAC,CAAC;UACxB,MAAME,QAAQ,GAAGhG,WAAW,CAAC+F,GAAG,CAAC;UACjC,MAAME,YAAY,GAAGrE,SAAS,CAACnC,KAAK,CAACsG,GAAG,CAAC;UACzC,IAAIC,QAAQ,IAAIA,QAAQ,KAAKC,YAAY,EAAE;YACzCrE,SAAS,CAACnC,KAAK,CAACsG,GAAG,CAAC,GAAGC,QAAQ;UACjC;QACF;MACF;IACF,CAAC,CAAC;EACJ,CAAC,CAAC;EACFxI,iBAAiB,CAAC0E,cAAc,EAAEqD,QAAQ,CAAC;EAC3ChI,SAAS,CAAC0H,aAAa,EAAE,CAAC9F,MAAM,EAAED,MAAM,EAAEE,KAAK,EAAEC,MAAM,EAAEC,KAAK,EAAEC,OAAO,EAAEa,KAAK,EAAEE,QAAQ,EAAEE,UAAU,EAAEC,SAAS,EAAEC,UAAU,EAAEC,SAAS,EAAEC,IAAI,EAAEC,IAAI,EAAEG,UAAU,EAAEC,SAAS,CAAC,CAAC;EAC3K;EACA,MAAMiF,gBAAgB,GAAG5I,KAAK,CAAC6D,OAAO,CAAC,OAAO;IAC5CgF,GAAG,EAAEC,GAAG,IAAI;MACVpE,cAAc,CAAC1D,IAAI,IAAI;QACrB,MAAM+H,KAAK,GAAG,IAAIpE,GAAG,CAAC3D,IAAI,CAAC;QAC3B+H,KAAK,CAACF,GAAG,CAACC,GAAG,CAAC;QACd,OAAO/H,WAAW,CAACC,IAAI,EAAE+H,KAAK,CAAC;MACjC,CAAC,CAAC;IACJ,CAAC;IACDC,MAAM,EAAEF,GAAG,IAAI;MACbjB,eAAe,CAACiB,GAAG,CAAC;MACpBpE,cAAc,CAAC1D,IAAI,IAAI;QACrB,MAAM+H,KAAK,GAAG,IAAIpE,GAAG,CAAC3D,IAAI,CAAC;QAC3B+H,KAAK,CAACE,MAAM,CAACH,GAAG,CAAC;QACjB,OAAO/H,WAAW,CAACC,IAAI,EAAE+H,KAAK,CAAC;MACjC,CAAC,CAAC;IACJ;EACF,CAAC,CAAC,EAAE,EAAE,CAAC;EACP;EACA,MAAMG,SAAS,GAAGzG,OAAO,IAAI,aAAazC,KAAK,CAAC0G,aAAa,CAACnG,gBAAgB,CAAC4I,QAAQ,EAAE;IACvFC,KAAK,EAAER;EACT,CAAC,EAAEpG,QAAQ,CAAC,IAAIA,QAAQ;EACxB,OAAO,aAAaxC,KAAK,CAAC0G,aAAa,CAAC,KAAK,EAAE;IAC7C2C,GAAG,EAAE9E,YAAY;IACjBnC,SAAS,EAAEjC,UAAU,CAACiC,SAAS,EAAEC,aAAa,CAAC;IAC/CF,KAAK,EAAEO;EACT,CAAC,EAAEwG,SAAS,CAAC;AACf,CAAC;AACD,IAAII,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzChI,SAAS,CAACiI,WAAW,GAAG,WAAW;AACrC;AACA,eAAejI,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}