"""
测试性能监控API的简单脚本
"""
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.text2sql_performance_monitor import text2sql_monitor

def test_performance_monitor():
    """测试性能监控器"""
    print("🧪 测试性能监控器...")
    
    try:
        # 测试获取性能摘要
        summary = text2sql_monitor.get_performance_summary()
        print("✅ 性能摘要获取成功")
        print(f"活动查询数: {summary.get('active_queries', 0)}")
        print(f"健康状态: {summary.get('health_status', 'unknown')}")
        
        # 测试开始查询监控
        query_id = text2sql_monitor.start_query_monitoring("test_query_123", "测试查询")
        print(f"✅ 查询监控启动成功: {query_id}")
        
        # 测试记录节点性能
        text2sql_monitor.record_node_performance(query_id, 'node1_schema_retrieval', 2.5, True)
        print("✅ 节点性能记录成功")
        
        # 测试完成查询监控
        text2sql_monitor.finish_query_monitoring(query_id, 10.0, True)
        print("✅ 查询监控完成成功")
        
        # 再次获取性能摘要
        summary2 = text2sql_monitor.get_performance_summary()
        print("✅ 第二次性能摘要获取成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 性能监控器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_performance_monitor()
    if success:
        print("\n🎉 性能监控器测试通过！")
    else:
        print("\n❌ 性能监控器测试失败！")
        sys.exit(1)
