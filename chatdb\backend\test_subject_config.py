#!/usr/bin/env python3
"""
测试科目配置文件功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_subject_config_loading():
    """测试科目配置加载"""
    print("🧪 测试科目配置加载")
    print("=" * 60)
    
    try:
        from app.config.subject_keywords import (
            get_subject_keywords,
            get_subject_categories,
            get_subject_by_keyword
        )
        
        # 测试获取科目关键词
        keywords = get_subject_keywords()
        print(f"✅ 成功加载科目关键词配置")
        print(f"📊 总计 {len(keywords)} 个科目")
        
        # 显示部分配置
        print(f"\n📋 科目配置示例:")
        count = 0
        for standard_name, keyword_list in keywords.items():
            if count < 5:  # 只显示前5个
                print(f"  {standard_name}: {keyword_list}")
                count += 1
            else:
                break
        
        if len(keywords) > 5:
            print(f"  ... 还有 {len(keywords) - 5} 个科目")
        
        # 测试获取科目分类
        categories = get_subject_categories()
        print(f"\n✅ 成功加载科目分类配置")
        print(f"📊 总计 {len(categories)} 个分类")
        
        for category, subjects in categories.items():
            print(f"  {category}: {subjects}")
        
        # 测试关键词查找
        test_keywords = ['管理费用', '销售费', '收入', '不存在的关键词']
        print(f"\n🔍 测试关键词查找:")
        
        for keyword in test_keywords:
            subject = get_subject_by_keyword(keyword)
            if subject:
                print(f"  '{keyword}' → {subject} ✅")
            else:
                print(f"  '{keyword}' → 未找到 ❌")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试科目配置加载失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_subject_intent_detection_with_config():
    """测试使用配置文件的科目意图检测"""
    print(f"\n🧪 测试科目意图检测（使用配置文件）")
    print("=" * 60)
    
    try:
        from app.services.text2sql_service import detect_subject_query_intent
        
        # 测试查询
        test_queries = [
            "分析2024年11月各公司的管理费用",
            "查询销售费用最高的部门",
            "统计营业收入情况",
            "各公司的研发费用明细",
            "财务费用分析报告",
            "主营业务成本统计",
            "查询固定资产情况",
            "应付账款明细",
            "这是一个不包含科目的查询"
        ]
        
        success_count = 0
        
        for i, query in enumerate(test_queries, 1):
            print(f"\n🔸 测试 {i}: {query}")
            
            try:
                intent = detect_subject_query_intent(query)
                
                print(f"  检测结果:")
                print(f"    有科目查询: {'✅' if intent['has_subject_query'] else '❌'}")
                
                if intent['subjects']:
                    print(f"    检测到的科目:")
                    for subject in intent['subjects']:
                        print(f"      - 关键词: '{subject['keyword']}'")
                        print(f"        标准名称: {subject['standard_name']}")
                        print(f"        筛选条件: {subject['condition']}")
                    success_count += 1
                else:
                    print(f"    检测到的科目: 无")
                    if "不包含科目" not in query:
                        print(f"    ⚠️ 可能需要添加相关关键词到配置文件")
                    else:
                        success_count += 1  # 正确识别为非科目查询
                        
            except Exception as e:
                print(f"    ❌ 检测失败: {e}")
        
        print(f"\n📊 测试结果: {success_count}/{len(test_queries)} 个查询处理成功")
        return success_count == len(test_queries)
        
    except Exception as e:
        print(f"❌ 测试科目意图检测失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_config_manager():
    """测试配置管理器"""
    print(f"\n🧪 测试配置管理器")
    print("=" * 60)
    
    try:
        from app.utils.subject_config_manager import subject_config_manager
        
        # 测试验证配置
        print("🔍 验证当前配置:")
        validation = subject_config_manager.validate_subject_config()
        
        print(f"  配置有效: {'✅' if validation['valid'] else '❌'}")
        print(f"  统计信息:")
        stats = validation['statistics']
        for key, value in stats.items():
            print(f"    {key}: {value}")
        
        if validation['warnings']:
            print(f"  警告:")
            for warning in validation['warnings']:
                print(f"    ⚠️ {warning}")
        
        if validation['errors']:
            print(f"  错误:")
            for error in validation['errors']:
                print(f"    ❌ {error}")
        
        # 测试搜索功能
        print(f"\n🔍 测试搜索功能:")
        search_terms = ['费用', '收入', '成本']
        
        for term in search_terms:
            matches = subject_config_manager.search_subjects_by_keyword(term)
            print(f"  搜索 '{term}': 找到 {len(matches)} 个匹配科目")
            if matches:
                print(f"    {matches[:3]}{'...' if len(matches) > 3 else ''}")
        
        return validation['valid']
        
    except Exception as e:
        print(f"❌ 测试配置管理器失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 测试科目配置文件系统")
    print("=" * 80)
    
    # 执行测试
    tests = [
        ("科目配置加载", test_subject_config_loading),
        ("科目意图检测", test_subject_intent_detection_with_config),
        ("配置管理器", test_config_manager)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 总结结果
    print("\n" + "=" * 80)
    print("📊 测试结果总结:")
    print("=" * 80)
    
    for test_name, result in results:
        status = "✅ 成功" if result else "❌ 失败"
        print(f"{test_name}: {status}")
    
    all_passed = all(result for _, result in results)
    
    if all_passed:
        print("\n🎉 所有测试通过！")
        print("💡 科目配置文件系统工作正常")
        print("\n📁 配置文件位置:")
        print("  - 主配置: app/config/subject_keywords.py")
        print("  - 管理工具: app/utils/subject_config_manager.py")
        print("\n🔧 维护建议:")
        print("  1. 在 subject_keywords.py 中添加新的科目关键词")
        print("  2. 使用配置管理器进行动态管理")
        print("  3. 定期验证配置完整性")
    else:
        print("\n⚠️ 部分测试失败，请检查配置文件")

if __name__ == "__main__":
    main()
