{"ast": null, "code": "import raf from \"rc-util/es/raf\";\nimport { easeInOutCubic } from './easings';\nimport getScroll, { isWindow } from './getScroll';\nexport default function scrollTo(y) {\n  let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  const {\n    getContainer = () => window,\n    callback,\n    duration = 450\n  } = options;\n  const container = getContainer();\n  const scrollTop = getScroll(container);\n  const startTime = Date.now();\n  const frameFunc = () => {\n    const timestamp = Date.now();\n    const time = timestamp - startTime;\n    const nextScrollTop = easeInOutCubic(time > duration ? duration : time, scrollTop, y, duration);\n    if (isWindow(container)) {\n      container.scrollTo(window.pageXOffset, nextScrollTop);\n    } else if (container instanceof Document || container.constructor.name === 'HTMLDocument') {\n      container.documentElement.scrollTop = nextScrollTop;\n    } else {\n      container.scrollTop = nextScrollTop;\n    }\n    if (time < duration) {\n      raf(frameFunc);\n    } else if (typeof callback === 'function') {\n      callback();\n    }\n  };\n  raf(frameFunc);\n}", "map": {"version": 3, "names": ["raf", "easeInOutCubic", "getScroll", "isWindow", "scrollTo", "y", "options", "arguments", "length", "undefined", "getContainer", "window", "callback", "duration", "container", "scrollTop", "startTime", "Date", "now", "frameFunc", "timestamp", "time", "nextScrollTop", "pageXOffset", "Document", "constructor", "name", "documentElement"], "sources": ["C:/Users/<USER>/PycharmProjects/智能数据分析系统/chatdb/frontend/node_modules/antd/es/_util/scrollTo.js"], "sourcesContent": ["import raf from \"rc-util/es/raf\";\nimport { easeInOutCubic } from './easings';\nimport getScroll, { isWindow } from './getScroll';\nexport default function scrollTo(y) {\n  let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  const {\n    getContainer = () => window,\n    callback,\n    duration = 450\n  } = options;\n  const container = getContainer();\n  const scrollTop = getScroll(container);\n  const startTime = Date.now();\n  const frameFunc = () => {\n    const timestamp = Date.now();\n    const time = timestamp - startTime;\n    const nextScrollTop = easeInOutCubic(time > duration ? duration : time, scrollTop, y, duration);\n    if (isWindow(container)) {\n      container.scrollTo(window.pageXOffset, nextScrollTop);\n    } else if (container instanceof Document || container.constructor.name === 'HTMLDocument') {\n      container.documentElement.scrollTop = nextScrollTop;\n    } else {\n      container.scrollTop = nextScrollTop;\n    }\n    if (time < duration) {\n      raf(frameFunc);\n    } else if (typeof callback === 'function') {\n      callback();\n    }\n  };\n  raf(frameFunc);\n}"], "mappings": "AAAA,OAAOA,GAAG,MAAM,gBAAgB;AAChC,SAASC,cAAc,QAAQ,WAAW;AAC1C,OAAOC,SAAS,IAAIC,QAAQ,QAAQ,aAAa;AACjD,eAAe,SAASC,QAAQA,CAACC,CAAC,EAAE;EAClC,IAAIC,OAAO,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;EACpF,MAAM;IACJG,YAAY,GAAGA,CAAA,KAAMC,MAAM;IAC3BC,QAAQ;IACRC,QAAQ,GAAG;EACb,CAAC,GAAGP,OAAO;EACX,MAAMQ,SAAS,GAAGJ,YAAY,CAAC,CAAC;EAChC,MAAMK,SAAS,GAAGb,SAAS,CAACY,SAAS,CAAC;EACtC,MAAME,SAAS,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC;EAC5B,MAAMC,SAAS,GAAGA,CAAA,KAAM;IACtB,MAAMC,SAAS,GAAGH,IAAI,CAACC,GAAG,CAAC,CAAC;IAC5B,MAAMG,IAAI,GAAGD,SAAS,GAAGJ,SAAS;IAClC,MAAMM,aAAa,GAAGrB,cAAc,CAACoB,IAAI,GAAGR,QAAQ,GAAGA,QAAQ,GAAGQ,IAAI,EAAEN,SAAS,EAAEV,CAAC,EAAEQ,QAAQ,CAAC;IAC/F,IAAIV,QAAQ,CAACW,SAAS,CAAC,EAAE;MACvBA,SAAS,CAACV,QAAQ,CAACO,MAAM,CAACY,WAAW,EAAED,aAAa,CAAC;IACvD,CAAC,MAAM,IAAIR,SAAS,YAAYU,QAAQ,IAAIV,SAAS,CAACW,WAAW,CAACC,IAAI,KAAK,cAAc,EAAE;MACzFZ,SAAS,CAACa,eAAe,CAACZ,SAAS,GAAGO,aAAa;IACrD,CAAC,MAAM;MACLR,SAAS,CAACC,SAAS,GAAGO,aAAa;IACrC;IACA,IAAID,IAAI,GAAGR,QAAQ,EAAE;MACnBb,GAAG,CAACmB,SAAS,CAAC;IAChB,CAAC,MAAM,IAAI,OAAOP,QAAQ,KAAK,UAAU,EAAE;MACzCA,QAAQ,CAAC,CAAC;IACZ;EACF,CAAC;EACDZ,GAAG,CAACmB,SAAS,CAAC;AAChB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}