{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport AuditOutlinedSvg from \"@ant-design/icons-svg/es/asn/AuditOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar AuditOutlined = function AuditOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: AuditOutlinedSvg\n  }));\n};\n\n/**![audit](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTI5NiAyNTBjLTQuNCAwLTggMy42LTggOHY0OGMwIDQuNCAzLjYgOCA4IDhoMzg0YzQuNCAwIDgtMy42IDgtOHYtNDhjMC00LjQtMy42LTgtOC04SDI5NnptMTg0IDE0NEgyOTZjLTQuNCAwLTggMy42LTggOHY0OGMwIDQuNCAzLjYgOCA4IDhoMTg0YzQuNCAwIDgtMy42IDgtOHYtNDhjMC00LjQtMy42LTgtOC04em0tNDggNDU4SDIwOFYxNDhoNTYwdjMyMGMwIDQuNCAzLjYgOCA4IDhoNTZjNC40IDAgOC0zLjYgOC04VjEwOGMwLTE3LjctMTQuMy0zMi0zMi0zMkgxNjhjLTE3LjcgMC0zMiAxNC4zLTMyIDMydjc4NGMwIDE3LjcgMTQuMyAzMiAzMiAzMmgyNjRjNC40IDAgOC0zLjYgOC04di01NmMwLTQuNC0zLjYtOC04LTh6bTQ0MC04OEg3Mjh2LTM2LjZjNDYuMy0xMy44IDgwLTU2LjYgODAtMTA3LjQgMC02MS45LTUwLjEtMTEyLTExMi0xMTJzLTExMiA1MC4xLTExMiAxMTJjMCA1MC43IDMzLjcgOTMuNiA4MCAxMDcuNFY3NjRINTIwYy04LjggMC0xNiA3LjItMTYgMTZ2MTUyYzAgOC44IDcuMiAxNiAxNiAxNmgzNTJjOC44IDAgMTYtNy4yIDE2LTE2Vjc4MGMwLTguOC03LjItMTYtMTYtMTZ6TTY0NiA2MjBjMC0yNy42IDIyLjQtNTAgNTAtNTBzNTAgMjIuNCA1MCA1MC0yMi40IDUwLTUwIDUwLTUwLTIyLjQtNTAtNTB6bTE4MCAyNjZINTY2di02MGgyNjB2NjB6IiAvPjwvc3ZnPg==) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(AuditOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'AuditOutlined';\n}\nexport default RefIcon;", "map": {"version": 3, "names": ["_extends", "React", "AuditOutlinedSvg", "AntdIcon", "AuditOutlined", "props", "ref", "createElement", "icon", "RefIcon", "forwardRef", "process", "env", "NODE_ENV", "displayName"], "sources": ["C:/Users/<USER>/PycharmProjects/智能数据分析系统/chatdb/frontend/node_modules/@ant-design/icons/es/icons/AuditOutlined.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport AuditOutlinedSvg from \"@ant-design/icons-svg/es/asn/AuditOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar AuditOutlined = function AuditOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: AuditOutlinedSvg\n  }));\n};\n\n/**![audit](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTI5NiAyNTBjLTQuNCAwLTggMy42LTggOHY0OGMwIDQuNCAzLjYgOCA4IDhoMzg0YzQuNCAwIDgtMy42IDgtOHYtNDhjMC00LjQtMy42LTgtOC04SDI5NnptMTg0IDE0NEgyOTZjLTQuNCAwLTggMy42LTggOHY0OGMwIDQuNCAzLjYgOCA4IDhoMTg0YzQuNCAwIDgtMy42IDgtOHYtNDhjMC00LjQtMy42LTgtOC04em0tNDggNDU4SDIwOFYxNDhoNTYwdjMyMGMwIDQuNCAzLjYgOCA4IDhoNTZjNC40IDAgOC0zLjYgOC04VjEwOGMwLTE3LjctMTQuMy0zMi0zMi0zMkgxNjhjLTE3LjcgMC0zMiAxNC4zLTMyIDMydjc4NGMwIDE3LjcgMTQuMyAzMiAzMiAzMmgyNjRjNC40IDAgOC0zLjYgOC04di01NmMwLTQuNC0zLjYtOC04LTh6bTQ0MC04OEg3Mjh2LTM2LjZjNDYuMy0xMy44IDgwLTU2LjYgODAtMTA3LjQgMC02MS45LTUwLjEtMTEyLTExMi0xMTJzLTExMiA1MC4xLTExMiAxMTJjMCA1MC43IDMzLjcgOTMuNiA4MCAxMDcuNFY3NjRINTIwYy04LjggMC0xNiA3LjItMTYgMTZ2MTUyYzAgOC44IDcuMiAxNiAxNiAxNmgzNTJjOC44IDAgMTYtNy4yIDE2LTE2Vjc4MGMwLTguOC03LjItMTYtMTYtMTZ6TTY0NiA2MjBjMC0yNy42IDIyLjQtNTAgNTAtNTBzNTAgMjIuNCA1MCA1MC0yMi40IDUwLTUwIDUwLTUwLTIyLjQtNTAtNTB6bTE4MCAyNjZINTY2di02MGgyNjB2NjB6IiAvPjwvc3ZnPg==) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(AuditOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'AuditOutlined';\n}\nexport default RefIcon;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD;AACA;;AAEA,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,gBAAgB,MAAM,4CAA4C;AACzE,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,IAAIC,aAAa,GAAG,SAASA,aAAaA,CAACC,KAAK,EAAEC,GAAG,EAAE;EACrD,OAAO,aAAaL,KAAK,CAACM,aAAa,CAACJ,QAAQ,EAAEH,QAAQ,CAAC,CAAC,CAAC,EAAEK,KAAK,EAAE;IACpEC,GAAG,EAAEA,GAAG;IACRE,IAAI,EAAEN;EACR,CAAC,CAAC,CAAC;AACL,CAAC;;AAED;AACA,IAAIO,OAAO,GAAG,aAAaR,KAAK,CAACS,UAAU,CAACN,aAAa,CAAC;AAC1D,IAAIO,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCJ,OAAO,CAACK,WAAW,GAAG,eAAe;AACvC;AACA,eAAeL,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}