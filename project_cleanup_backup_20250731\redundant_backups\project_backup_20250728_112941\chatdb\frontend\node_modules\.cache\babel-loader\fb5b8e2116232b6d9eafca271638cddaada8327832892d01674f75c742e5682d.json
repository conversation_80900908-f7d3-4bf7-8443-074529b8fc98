{"ast": null, "code": "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"children\"];\nimport useMemo from \"rc-util/es/hooks/useMemo\";\nimport isEqual from \"rc-util/es/isEqual\";\nimport * as React from 'react';\nimport CacheEntity from \"./Cache\";\nexport var ATTR_TOKEN = 'data-token-hash';\nexport var ATTR_MARK = 'data-css-hash';\nexport var ATTR_CACHE_PATH = 'data-cache-path';\n\n// Mark css-in-js instance in style element\nexport var CSS_IN_JS_INSTANCE = '__cssinjs_instance__';\nexport function createCache() {\n  var cssinjsInstanceId = Math.random().toString(12).slice(2);\n\n  // Tricky SSR: Move all inline style to the head.\n  // PS: We do not recommend tricky mode.\n  if (typeof document !== 'undefined' && document.head && document.body) {\n    var styles = document.body.querySelectorAll(\"style[\".concat(ATTR_MARK, \"]\")) || [];\n    var firstChild = document.head.firstChild;\n    Array.from(styles).forEach(function (style) {\n      style[CSS_IN_JS_INSTANCE] = style[CSS_IN_JS_INSTANCE] || cssinjsInstanceId;\n\n      // Not force move if no head\n      if (style[CSS_IN_JS_INSTANCE] === cssinjsInstanceId) {\n        document.head.insertBefore(style, firstChild);\n      }\n    });\n\n    // Deduplicate of moved styles\n    var styleHash = {};\n    Array.from(document.querySelectorAll(\"style[\".concat(ATTR_MARK, \"]\"))).forEach(function (style) {\n      var hash = style.getAttribute(ATTR_MARK);\n      if (styleHash[hash]) {\n        if (style[CSS_IN_JS_INSTANCE] === cssinjsInstanceId) {\n          var _style$parentNode;\n          (_style$parentNode = style.parentNode) === null || _style$parentNode === void 0 || _style$parentNode.removeChild(style);\n        }\n      } else {\n        styleHash[hash] = true;\n      }\n    });\n  }\n  return new CacheEntity(cssinjsInstanceId);\n}\nvar StyleContext = /*#__PURE__*/React.createContext({\n  hashPriority: 'low',\n  cache: createCache(),\n  defaultCache: true\n});\nexport var StyleProvider = function StyleProvider(props) {\n  var children = props.children,\n    restProps = _objectWithoutProperties(props, _excluded);\n  var parentContext = React.useContext(StyleContext);\n  var context = useMemo(function () {\n    var mergedContext = _objectSpread({}, parentContext);\n    Object.keys(restProps).forEach(function (key) {\n      var value = restProps[key];\n      if (restProps[key] !== undefined) {\n        mergedContext[key] = value;\n      }\n    });\n    var cache = restProps.cache;\n    mergedContext.cache = mergedContext.cache || createCache();\n    mergedContext.defaultCache = !cache && parentContext.defaultCache;\n    return mergedContext;\n  }, [parentContext, restProps], function (prev, next) {\n    return !isEqual(prev[0], next[0], true) || !isEqual(prev[1], next[1], true);\n  });\n  return /*#__PURE__*/React.createElement(StyleContext.Provider, {\n    value: context\n  }, children);\n};\nexport default StyleContext;", "map": {"version": 3, "names": ["_objectSpread", "_objectWithoutProperties", "_excluded", "useMemo", "isEqual", "React", "Cache<PERSON>ntity", "ATTR_TOKEN", "ATTR_MARK", "ATTR_CACHE_PATH", "CSS_IN_JS_INSTANCE", "createCache", "cssinjsInstanceId", "Math", "random", "toString", "slice", "document", "head", "body", "styles", "querySelectorAll", "concat", "<PERSON><PERSON><PERSON><PERSON>", "Array", "from", "for<PERSON>ach", "style", "insertBefore", "styleHash", "hash", "getAttribute", "_style$parentNode", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "StyleContext", "createContext", "hashPriority", "cache", "defaultCache", "StyleProvider", "props", "children", "restProps", "parentContext", "useContext", "context", "mergedContext", "Object", "keys", "key", "value", "undefined", "prev", "next", "createElement", "Provider"], "sources": ["C:/Users/<USER>/PycharmProjects/智能数据分析系统/chatdb/frontend/node_modules/@ant-design/cssinjs/es/StyleContext.js"], "sourcesContent": ["import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"children\"];\nimport useMemo from \"rc-util/es/hooks/useMemo\";\nimport isEqual from \"rc-util/es/isEqual\";\nimport * as React from 'react';\nimport CacheEntity from \"./Cache\";\nexport var ATTR_TOKEN = 'data-token-hash';\nexport var ATTR_MARK = 'data-css-hash';\nexport var ATTR_CACHE_PATH = 'data-cache-path';\n\n// Mark css-in-js instance in style element\nexport var CSS_IN_JS_INSTANCE = '__cssinjs_instance__';\nexport function createCache() {\n  var cssinjsInstanceId = Math.random().toString(12).slice(2);\n\n  // Tricky SSR: Move all inline style to the head.\n  // PS: We do not recommend tricky mode.\n  if (typeof document !== 'undefined' && document.head && document.body) {\n    var styles = document.body.querySelectorAll(\"style[\".concat(ATTR_MARK, \"]\")) || [];\n    var firstChild = document.head.firstChild;\n    Array.from(styles).forEach(function (style) {\n      style[CSS_IN_JS_INSTANCE] = style[CSS_IN_JS_INSTANCE] || cssinjsInstanceId;\n\n      // Not force move if no head\n      if (style[CSS_IN_JS_INSTANCE] === cssinjsInstanceId) {\n        document.head.insertBefore(style, firstChild);\n      }\n    });\n\n    // Deduplicate of moved styles\n    var styleHash = {};\n    Array.from(document.querySelectorAll(\"style[\".concat(ATTR_MARK, \"]\"))).forEach(function (style) {\n      var hash = style.getAttribute(ATTR_MARK);\n      if (styleHash[hash]) {\n        if (style[CSS_IN_JS_INSTANCE] === cssinjsInstanceId) {\n          var _style$parentNode;\n          (_style$parentNode = style.parentNode) === null || _style$parentNode === void 0 || _style$parentNode.removeChild(style);\n        }\n      } else {\n        styleHash[hash] = true;\n      }\n    });\n  }\n  return new CacheEntity(cssinjsInstanceId);\n}\nvar StyleContext = /*#__PURE__*/React.createContext({\n  hashPriority: 'low',\n  cache: createCache(),\n  defaultCache: true\n});\nexport var StyleProvider = function StyleProvider(props) {\n  var children = props.children,\n    restProps = _objectWithoutProperties(props, _excluded);\n  var parentContext = React.useContext(StyleContext);\n  var context = useMemo(function () {\n    var mergedContext = _objectSpread({}, parentContext);\n    Object.keys(restProps).forEach(function (key) {\n      var value = restProps[key];\n      if (restProps[key] !== undefined) {\n        mergedContext[key] = value;\n      }\n    });\n    var cache = restProps.cache;\n    mergedContext.cache = mergedContext.cache || createCache();\n    mergedContext.defaultCache = !cache && parentContext.defaultCache;\n    return mergedContext;\n  }, [parentContext, restProps], function (prev, next) {\n    return !isEqual(prev[0], next[0], true) || !isEqual(prev[1], next[1], true);\n  });\n  return /*#__PURE__*/React.createElement(StyleContext.Provider, {\n    value: context\n  }, children);\n};\nexport default StyleContext;"], "mappings": "AAAA,OAAOA,aAAa,MAAM,0CAA0C;AACpE,OAAOC,wBAAwB,MAAM,oDAAoD;AACzF,IAAIC,SAAS,GAAG,CAAC,UAAU,CAAC;AAC5B,OAAOC,OAAO,MAAM,0BAA0B;AAC9C,OAAOC,OAAO,MAAM,oBAAoB;AACxC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,WAAW,MAAM,SAAS;AACjC,OAAO,IAAIC,UAAU,GAAG,iBAAiB;AACzC,OAAO,IAAIC,SAAS,GAAG,eAAe;AACtC,OAAO,IAAIC,eAAe,GAAG,iBAAiB;;AAE9C;AACA,OAAO,IAAIC,kBAAkB,GAAG,sBAAsB;AACtD,OAAO,SAASC,WAAWA,CAAA,EAAG;EAC5B,IAAIC,iBAAiB,GAAGC,IAAI,CAACC,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,KAAK,CAAC,CAAC,CAAC;;EAE3D;EACA;EACA,IAAI,OAAOC,QAAQ,KAAK,WAAW,IAAIA,QAAQ,CAACC,IAAI,IAAID,QAAQ,CAACE,IAAI,EAAE;IACrE,IAAIC,MAAM,GAAGH,QAAQ,CAACE,IAAI,CAACE,gBAAgB,CAAC,QAAQ,CAACC,MAAM,CAACd,SAAS,EAAE,GAAG,CAAC,CAAC,IAAI,EAAE;IAClF,IAAIe,UAAU,GAAGN,QAAQ,CAACC,IAAI,CAACK,UAAU;IACzCC,KAAK,CAACC,IAAI,CAACL,MAAM,CAAC,CAACM,OAAO,CAAC,UAAUC,KAAK,EAAE;MAC1CA,KAAK,CAACjB,kBAAkB,CAAC,GAAGiB,KAAK,CAACjB,kBAAkB,CAAC,IAAIE,iBAAiB;;MAE1E;MACA,IAAIe,KAAK,CAACjB,kBAAkB,CAAC,KAAKE,iBAAiB,EAAE;QACnDK,QAAQ,CAACC,IAAI,CAACU,YAAY,CAACD,KAAK,EAAEJ,UAAU,CAAC;MAC/C;IACF,CAAC,CAAC;;IAEF;IACA,IAAIM,SAAS,GAAG,CAAC,CAAC;IAClBL,KAAK,CAACC,IAAI,CAACR,QAAQ,CAACI,gBAAgB,CAAC,QAAQ,CAACC,MAAM,CAACd,SAAS,EAAE,GAAG,CAAC,CAAC,CAAC,CAACkB,OAAO,CAAC,UAAUC,KAAK,EAAE;MAC9F,IAAIG,IAAI,GAAGH,KAAK,CAACI,YAAY,CAACvB,SAAS,CAAC;MACxC,IAAIqB,SAAS,CAACC,IAAI,CAAC,EAAE;QACnB,IAAIH,KAAK,CAACjB,kBAAkB,CAAC,KAAKE,iBAAiB,EAAE;UACnD,IAAIoB,iBAAiB;UACrB,CAACA,iBAAiB,GAAGL,KAAK,CAACM,UAAU,MAAM,IAAI,IAAID,iBAAiB,KAAK,KAAK,CAAC,IAAIA,iBAAiB,CAACE,WAAW,CAACP,KAAK,CAAC;QACzH;MACF,CAAC,MAAM;QACLE,SAAS,CAACC,IAAI,CAAC,GAAG,IAAI;MACxB;IACF,CAAC,CAAC;EACJ;EACA,OAAO,IAAIxB,WAAW,CAACM,iBAAiB,CAAC;AAC3C;AACA,IAAIuB,YAAY,GAAG,aAAa9B,KAAK,CAAC+B,aAAa,CAAC;EAClDC,YAAY,EAAE,KAAK;EACnBC,KAAK,EAAE3B,WAAW,CAAC,CAAC;EACpB4B,YAAY,EAAE;AAChB,CAAC,CAAC;AACF,OAAO,IAAIC,aAAa,GAAG,SAASA,aAAaA,CAACC,KAAK,EAAE;EACvD,IAAIC,QAAQ,GAAGD,KAAK,CAACC,QAAQ;IAC3BC,SAAS,GAAG1C,wBAAwB,CAACwC,KAAK,EAAEvC,SAAS,CAAC;EACxD,IAAI0C,aAAa,GAAGvC,KAAK,CAACwC,UAAU,CAACV,YAAY,CAAC;EAClD,IAAIW,OAAO,GAAG3C,OAAO,CAAC,YAAY;IAChC,IAAI4C,aAAa,GAAG/C,aAAa,CAAC,CAAC,CAAC,EAAE4C,aAAa,CAAC;IACpDI,MAAM,CAACC,IAAI,CAACN,SAAS,CAAC,CAACjB,OAAO,CAAC,UAAUwB,GAAG,EAAE;MAC5C,IAAIC,KAAK,GAAGR,SAAS,CAACO,GAAG,CAAC;MAC1B,IAAIP,SAAS,CAACO,GAAG,CAAC,KAAKE,SAAS,EAAE;QAChCL,aAAa,CAACG,GAAG,CAAC,GAAGC,KAAK;MAC5B;IACF,CAAC,CAAC;IACF,IAAIb,KAAK,GAAGK,SAAS,CAACL,KAAK;IAC3BS,aAAa,CAACT,KAAK,GAAGS,aAAa,CAACT,KAAK,IAAI3B,WAAW,CAAC,CAAC;IAC1DoC,aAAa,CAACR,YAAY,GAAG,CAACD,KAAK,IAAIM,aAAa,CAACL,YAAY;IACjE,OAAOQ,aAAa;EACtB,CAAC,EAAE,CAACH,aAAa,EAAED,SAAS,CAAC,EAAE,UAAUU,IAAI,EAAEC,IAAI,EAAE;IACnD,OAAO,CAAClD,OAAO,CAACiD,IAAI,CAAC,CAAC,CAAC,EAAEC,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,CAAClD,OAAO,CAACiD,IAAI,CAAC,CAAC,CAAC,EAAEC,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC;EAC7E,CAAC,CAAC;EACF,OAAO,aAAajD,KAAK,CAACkD,aAAa,CAACpB,YAAY,CAACqB,QAAQ,EAAE;IAC7DL,KAAK,EAAEL;EACT,CAAC,EAAEJ,QAAQ,CAAC;AACd,CAAC;AACD,eAAeP,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}