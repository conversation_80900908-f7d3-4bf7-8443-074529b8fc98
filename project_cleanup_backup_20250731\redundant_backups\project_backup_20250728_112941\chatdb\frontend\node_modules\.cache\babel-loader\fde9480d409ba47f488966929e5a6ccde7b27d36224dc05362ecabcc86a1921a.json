{"ast": null, "code": "import React, { memo, useRef } from 'react';\nimport cc from 'classcat';\nimport { useStore } from '@reactflow/core';\nimport { shallow } from 'zustand/shallow';\nvar BackgroundVariant;\n(function (BackgroundVariant) {\n  BackgroundVariant[\"Lines\"] = \"lines\";\n  BackgroundVariant[\"Dots\"] = \"dots\";\n  BackgroundVariant[\"Cross\"] = \"cross\";\n})(BackgroundVariant || (BackgroundVariant = {}));\nfunction LinePattern({\n  color,\n  dimensions,\n  lineWidth\n}) {\n  return React.createElement(\"path\", {\n    stroke: color,\n    strokeWidth: lineWidth,\n    d: `M${dimensions[0] / 2} 0 V${dimensions[1]} M0 ${dimensions[1] / 2} H${dimensions[0]}`\n  });\n}\nfunction DotPattern({\n  color,\n  radius\n}) {\n  return React.createElement(\"circle\", {\n    cx: radius,\n    cy: radius,\n    r: radius,\n    fill: color\n  });\n}\nconst defaultColor = {\n  [BackgroundVariant.Dots]: '#91919a',\n  [BackgroundVariant.Lines]: '#eee',\n  [BackgroundVariant.Cross]: '#e2e2e2'\n};\nconst defaultSize = {\n  [BackgroundVariant.Dots]: 1,\n  [BackgroundVariant.Lines]: 1,\n  [BackgroundVariant.Cross]: 6\n};\nconst selector = s => ({\n  transform: s.transform,\n  patternId: `pattern-${s.rfId}`\n});\nfunction Background({\n  id,\n  variant = BackgroundVariant.Dots,\n  // only used for dots and cross\n  gap = 20,\n  // only used for lines and cross\n  size,\n  lineWidth = 1,\n  offset = 2,\n  color,\n  style,\n  className\n}) {\n  const ref = useRef(null);\n  const {\n    transform,\n    patternId\n  } = useStore(selector, shallow);\n  const patternColor = color || defaultColor[variant];\n  const patternSize = size || defaultSize[variant];\n  const isDots = variant === BackgroundVariant.Dots;\n  const isCross = variant === BackgroundVariant.Cross;\n  const gapXY = Array.isArray(gap) ? gap : [gap, gap];\n  const scaledGap = [gapXY[0] * transform[2] || 1, gapXY[1] * transform[2] || 1];\n  const scaledSize = patternSize * transform[2];\n  const patternDimensions = isCross ? [scaledSize, scaledSize] : scaledGap;\n  const patternOffset = isDots ? [scaledSize / offset, scaledSize / offset] : [patternDimensions[0] / offset, patternDimensions[1] / offset];\n  return React.createElement(\"svg\", {\n    className: cc(['react-flow__background', className]),\n    style: {\n      ...style,\n      position: 'absolute',\n      width: '100%',\n      height: '100%',\n      top: 0,\n      left: 0\n    },\n    ref: ref,\n    \"data-testid\": \"rf__background\"\n  }, React.createElement(\"pattern\", {\n    id: patternId + id,\n    x: transform[0] % scaledGap[0],\n    y: transform[1] % scaledGap[1],\n    width: scaledGap[0],\n    height: scaledGap[1],\n    patternUnits: \"userSpaceOnUse\",\n    patternTransform: `translate(-${patternOffset[0]},-${patternOffset[1]})`\n  }, isDots ? React.createElement(DotPattern, {\n    color: patternColor,\n    radius: scaledSize / offset\n  }) : React.createElement(LinePattern, {\n    dimensions: patternDimensions,\n    color: patternColor,\n    lineWidth: lineWidth\n  })), React.createElement(\"rect\", {\n    x: \"0\",\n    y: \"0\",\n    width: \"100%\",\n    height: \"100%\",\n    fill: `url(#${patternId + id})`\n  }));\n}\nBackground.displayName = 'Background';\nvar Background$1 = memo(Background);\nexport { Background$1 as Background, BackgroundVariant };", "map": {"version": 3, "names": ["React", "memo", "useRef", "cc", "useStore", "shallow", "<PERSON><PERSON><PERSON><PERSON>", "LinePattern", "color", "dimensions", "lineWidth", "createElement", "stroke", "strokeWidth", "d", "DotPattern", "radius", "cx", "cy", "r", "fill", "defaultColor", "Dots", "Lines", "Cross", "defaultSize", "selector", "s", "transform", "patternId", "rfId", "Background", "id", "variant", "gap", "size", "offset", "style", "className", "ref", "patternColor", "patternSize", "isDots", "isCross", "gapXY", "Array", "isArray", "scaledGap", "scaledSize", "patternDimensions", "patternOffset", "position", "width", "height", "top", "left", "x", "y", "patternUnits", "patternTransform", "displayName", "Background$1"], "sources": ["C:/Users/<USER>/PycharmProjects/智能数据分析系统/chatdb/frontend/node_modules/@reactflow/background/dist/esm/index.mjs"], "sourcesContent": ["import React, { memo, useRef } from 'react';\nimport cc from 'classcat';\nimport { useStore } from '@reactflow/core';\nimport { shallow } from 'zustand/shallow';\n\nvar BackgroundVariant;\n(function (BackgroundVariant) {\n    BackgroundVariant[\"Lines\"] = \"lines\";\n    BackgroundVariant[\"Dots\"] = \"dots\";\n    BackgroundVariant[\"Cross\"] = \"cross\";\n})(BackgroundVariant || (BackgroundVariant = {}));\n\nfunction LinePattern({ color, dimensions, lineWidth }) {\n    return (React.createElement(\"path\", { stroke: color, strokeWidth: lineWidth, d: `M${dimensions[0] / 2} 0 V${dimensions[1]} M0 ${dimensions[1] / 2} H${dimensions[0]}` }));\n}\nfunction DotPattern({ color, radius }) {\n    return React.createElement(\"circle\", { cx: radius, cy: radius, r: radius, fill: color });\n}\n\nconst defaultColor = {\n    [BackgroundVariant.Dots]: '#91919a',\n    [BackgroundVariant.Lines]: '#eee',\n    [BackgroundVariant.Cross]: '#e2e2e2',\n};\nconst defaultSize = {\n    [BackgroundVariant.Dots]: 1,\n    [BackgroundVariant.Lines]: 1,\n    [BackgroundVariant.Cross]: 6,\n};\nconst selector = (s) => ({ transform: s.transform, patternId: `pattern-${s.rfId}` });\nfunction Background({ id, variant = BackgroundVariant.Dots, \n// only used for dots and cross\ngap = 20, \n// only used for lines and cross\nsize, lineWidth = 1, offset = 2, color, style, className, }) {\n    const ref = useRef(null);\n    const { transform, patternId } = useStore(selector, shallow);\n    const patternColor = color || defaultColor[variant];\n    const patternSize = size || defaultSize[variant];\n    const isDots = variant === BackgroundVariant.Dots;\n    const isCross = variant === BackgroundVariant.Cross;\n    const gapXY = Array.isArray(gap) ? gap : [gap, gap];\n    const scaledGap = [gapXY[0] * transform[2] || 1, gapXY[1] * transform[2] || 1];\n    const scaledSize = patternSize * transform[2];\n    const patternDimensions = isCross ? [scaledSize, scaledSize] : scaledGap;\n    const patternOffset = isDots\n        ? [scaledSize / offset, scaledSize / offset]\n        : [patternDimensions[0] / offset, patternDimensions[1] / offset];\n    return (React.createElement(\"svg\", { className: cc(['react-flow__background', className]), style: {\n            ...style,\n            position: 'absolute',\n            width: '100%',\n            height: '100%',\n            top: 0,\n            left: 0,\n        }, ref: ref, \"data-testid\": \"rf__background\" },\n        React.createElement(\"pattern\", { id: patternId + id, x: transform[0] % scaledGap[0], y: transform[1] % scaledGap[1], width: scaledGap[0], height: scaledGap[1], patternUnits: \"userSpaceOnUse\", patternTransform: `translate(-${patternOffset[0]},-${patternOffset[1]})` }, isDots ? (React.createElement(DotPattern, { color: patternColor, radius: scaledSize / offset })) : (React.createElement(LinePattern, { dimensions: patternDimensions, color: patternColor, lineWidth: lineWidth }))),\n        React.createElement(\"rect\", { x: \"0\", y: \"0\", width: \"100%\", height: \"100%\", fill: `url(#${patternId + id})` })));\n}\nBackground.displayName = 'Background';\nvar Background$1 = memo(Background);\n\nexport { Background$1 as Background, BackgroundVariant };\n"], "mappings": "AAAA,OAAOA,KAAK,IAAIC,IAAI,EAAEC,MAAM,QAAQ,OAAO;AAC3C,OAAOC,EAAE,MAAM,UAAU;AACzB,SAASC,QAAQ,QAAQ,iBAAiB;AAC1C,SAASC,OAAO,QAAQ,iBAAiB;AAEzC,IAAIC,iBAAiB;AACrB,CAAC,UAAUA,iBAAiB,EAAE;EAC1BA,iBAAiB,CAAC,OAAO,CAAC,GAAG,OAAO;EACpCA,iBAAiB,CAAC,MAAM,CAAC,GAAG,MAAM;EAClCA,iBAAiB,CAAC,OAAO,CAAC,GAAG,OAAO;AACxC,CAAC,EAAEA,iBAAiB,KAAKA,iBAAiB,GAAG,CAAC,CAAC,CAAC,CAAC;AAEjD,SAASC,WAAWA,CAAC;EAAEC,KAAK;EAAEC,UAAU;EAAEC;AAAU,CAAC,EAAE;EACnD,OAAQV,KAAK,CAACW,aAAa,CAAC,MAAM,EAAE;IAAEC,MAAM,EAAEJ,KAAK;IAAEK,WAAW,EAAEH,SAAS;IAAEI,CAAC,EAAE,IAAIL,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC,OAAOA,UAAU,CAAC,CAAC,CAAC,OAAOA,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC,KAAKA,UAAU,CAAC,CAAC,CAAC;EAAG,CAAC,CAAC;AAC5K;AACA,SAASM,UAAUA,CAAC;EAAEP,KAAK;EAAEQ;AAAO,CAAC,EAAE;EACnC,OAAOhB,KAAK,CAACW,aAAa,CAAC,QAAQ,EAAE;IAAEM,EAAE,EAAED,MAAM;IAAEE,EAAE,EAAEF,MAAM;IAAEG,CAAC,EAAEH,MAAM;IAAEI,IAAI,EAAEZ;EAAM,CAAC,CAAC;AAC5F;AAEA,MAAMa,YAAY,GAAG;EACjB,CAACf,iBAAiB,CAACgB,IAAI,GAAG,SAAS;EACnC,CAAChB,iBAAiB,CAACiB,KAAK,GAAG,MAAM;EACjC,CAACjB,iBAAiB,CAACkB,KAAK,GAAG;AAC/B,CAAC;AACD,MAAMC,WAAW,GAAG;EAChB,CAACnB,iBAAiB,CAACgB,IAAI,GAAG,CAAC;EAC3B,CAAChB,iBAAiB,CAACiB,KAAK,GAAG,CAAC;EAC5B,CAACjB,iBAAiB,CAACkB,KAAK,GAAG;AAC/B,CAAC;AACD,MAAME,QAAQ,GAAIC,CAAC,KAAM;EAAEC,SAAS,EAAED,CAAC,CAACC,SAAS;EAAEC,SAAS,EAAE,WAAWF,CAAC,CAACG,IAAI;AAAG,CAAC,CAAC;AACpF,SAASC,UAAUA,CAAC;EAAEC,EAAE;EAAEC,OAAO,GAAG3B,iBAAiB,CAACgB,IAAI;EAC1D;EACAY,GAAG,GAAG,EAAE;EACR;EACAC,IAAI;EAAEzB,SAAS,GAAG,CAAC;EAAE0B,MAAM,GAAG,CAAC;EAAE5B,KAAK;EAAE6B,KAAK;EAAEC;AAAW,CAAC,EAAE;EACzD,MAAMC,GAAG,GAAGrC,MAAM,CAAC,IAAI,CAAC;EACxB,MAAM;IAAE0B,SAAS;IAAEC;EAAU,CAAC,GAAGzB,QAAQ,CAACsB,QAAQ,EAAErB,OAAO,CAAC;EAC5D,MAAMmC,YAAY,GAAGhC,KAAK,IAAIa,YAAY,CAACY,OAAO,CAAC;EACnD,MAAMQ,WAAW,GAAGN,IAAI,IAAIV,WAAW,CAACQ,OAAO,CAAC;EAChD,MAAMS,MAAM,GAAGT,OAAO,KAAK3B,iBAAiB,CAACgB,IAAI;EACjD,MAAMqB,OAAO,GAAGV,OAAO,KAAK3B,iBAAiB,CAACkB,KAAK;EACnD,MAAMoB,KAAK,GAAGC,KAAK,CAACC,OAAO,CAACZ,GAAG,CAAC,GAAGA,GAAG,GAAG,CAACA,GAAG,EAAEA,GAAG,CAAC;EACnD,MAAMa,SAAS,GAAG,CAACH,KAAK,CAAC,CAAC,CAAC,GAAGhB,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,EAAEgB,KAAK,CAAC,CAAC,CAAC,GAAGhB,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;EAC9E,MAAMoB,UAAU,GAAGP,WAAW,GAAGb,SAAS,CAAC,CAAC,CAAC;EAC7C,MAAMqB,iBAAiB,GAAGN,OAAO,GAAG,CAACK,UAAU,EAAEA,UAAU,CAAC,GAAGD,SAAS;EACxE,MAAMG,aAAa,GAAGR,MAAM,GACtB,CAACM,UAAU,GAAGZ,MAAM,EAAEY,UAAU,GAAGZ,MAAM,CAAC,GAC1C,CAACa,iBAAiB,CAAC,CAAC,CAAC,GAAGb,MAAM,EAAEa,iBAAiB,CAAC,CAAC,CAAC,GAAGb,MAAM,CAAC;EACpE,OAAQpC,KAAK,CAACW,aAAa,CAAC,KAAK,EAAE;IAAE2B,SAAS,EAAEnC,EAAE,CAAC,CAAC,wBAAwB,EAAEmC,SAAS,CAAC,CAAC;IAAED,KAAK,EAAE;MAC1F,GAAGA,KAAK;MACRc,QAAQ,EAAE,UAAU;MACpBC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,MAAM;MACdC,GAAG,EAAE,CAAC;MACNC,IAAI,EAAE;IACV,CAAC;IAAEhB,GAAG,EAAEA,GAAG;IAAE,aAAa,EAAE;EAAiB,CAAC,EAC9CvC,KAAK,CAACW,aAAa,CAAC,SAAS,EAAE;IAAEqB,EAAE,EAAEH,SAAS,GAAGG,EAAE;IAAEwB,CAAC,EAAE5B,SAAS,CAAC,CAAC,CAAC,GAAGmB,SAAS,CAAC,CAAC,CAAC;IAAEU,CAAC,EAAE7B,SAAS,CAAC,CAAC,CAAC,GAAGmB,SAAS,CAAC,CAAC,CAAC;IAAEK,KAAK,EAAEL,SAAS,CAAC,CAAC,CAAC;IAAEM,MAAM,EAAEN,SAAS,CAAC,CAAC,CAAC;IAAEW,YAAY,EAAE,gBAAgB;IAAEC,gBAAgB,EAAE,cAAcT,aAAa,CAAC,CAAC,CAAC,KAAKA,aAAa,CAAC,CAAC,CAAC;EAAI,CAAC,EAAER,MAAM,GAAI1C,KAAK,CAACW,aAAa,CAACI,UAAU,EAAE;IAAEP,KAAK,EAAEgC,YAAY;IAAExB,MAAM,EAAEgC,UAAU,GAAGZ;EAAO,CAAC,CAAC,GAAKpC,KAAK,CAACW,aAAa,CAACJ,WAAW,EAAE;IAAEE,UAAU,EAAEwC,iBAAiB;IAAEzC,KAAK,EAAEgC,YAAY;IAAE9B,SAAS,EAAEA;EAAU,CAAC,CAAE,CAAC,EACheV,KAAK,CAACW,aAAa,CAAC,MAAM,EAAE;IAAE6C,CAAC,EAAE,GAAG;IAAEC,CAAC,EAAE,GAAG;IAAEL,KAAK,EAAE,MAAM;IAAEC,MAAM,EAAE,MAAM;IAAEjC,IAAI,EAAE,QAAQS,SAAS,GAAGG,EAAE;EAAI,CAAC,CAAC,CAAC;AACxH;AACAD,UAAU,CAAC6B,WAAW,GAAG,YAAY;AACrC,IAAIC,YAAY,GAAG5D,IAAI,CAAC8B,UAAU,CAAC;AAEnC,SAAS8B,YAAY,IAAI9B,UAAU,EAAEzB,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}