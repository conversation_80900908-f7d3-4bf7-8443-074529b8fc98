"""
Text2SQL工具模块
提供查询分析、表结构检索、SQL处理等工具函数
"""
import re
import json
import time
import sqlparse
import sqlite3
import logging
from typing import Dict, Any, List, Optional, Tuple, Set
from sqlalchemy.orm import Session
from neo4j import GraphDatabase

from autogen_core.models import UserMessage
from app.core.config import settings
from app.core.llms import model_client
from app import crud

# 设置日志
logger = logging.getLogger(__name__)

# 查询分析缓存，避免重复的LLM调用
query_analysis_cache = {}


async def analyze_query_with_llm(query: str) -> Dict[str, Any]:
    """
    使用LLM分析自然语言查询，提取关键实体和意图
    返回包含实体、关系和查询意图的结构化分析
    """
    # 检查缓存
    if query in query_analysis_cache:
        return query_analysis_cache[query]

    try:
        # 为LLM准备提示
        prompt = f"""
        你是一名数据库专家，帮助分析自然语言查询以找到相关的数据库表和列。
        请分析以下查询并提取关键信息：

        查询: "{query}"

        请以以下JSON格式提供分析：
        {{
            "entities": [查询中提到或暗示的实体名称列表],
            "relationships": [查询中暗示的实体间关系列表],
            "query_intent": "查询试图找到什么的简要描述",
            "likely_aggregations": [可能需要的聚合操作列表，如count、sum、avg],
            "time_related": 布尔值，表示查询是否涉及时间/日期过滤或分组,
            "comparison_related": 布尔值，表示查询是否涉及值比较
        }}
        """

        # 调用LLM
        response = await model_client.create([UserMessage(content=prompt, source="user")])
        response_text = response.content

        # 提取并解析JSON响应
        json_match = re.search(r'\{[\s\S]*}', response_text)
        if json_match:
            json_str = json_match.group(0)
            analysis = json.loads(json_str)

            # 验证必需字段
            if not all(k in analysis for k in ["entities", "relationships", "query_intent"]):
                analysis = _create_fallback_analysis(query)
        else:
            analysis = _create_fallback_analysis(query)

        # 缓存结果
        query_analysis_cache[query] = analysis
        return analysis
    except Exception as e:
        # 如果发生任何错误，回退到关键词提取
        analysis = _create_fallback_analysis(query)
        query_analysis_cache[query] = analysis
        return analysis


def _create_fallback_analysis(query: str) -> Dict[str, Any]:
    """创建回退分析结果"""
    return {
        "entities": extract_keywords(query),
        "relationships": [],
        "query_intent": query,
        "likely_aggregations": [],
        "time_related": False,
        "comparison_related": False
    }


def extract_keywords(query: str) -> List[str]:
    """
    使用正则表达式从查询中提取关键词（回退方法）
    """
    keywords = re.findall(r'\b\w+\b', query.lower())
    return [k for k in keywords if len(k) > 2 and k not in {
        'the', 'and', 'for', 'from', 'where', 'what', 'which', 'when', 'who',
        'how', 'many', 'much', 'with', 'that', 'this', 'these', 'those',
        '什么', '哪个', '哪些', '什么时候', '谁', '怎么', '多少', '和', '的', '是'
    }]


async def find_relevant_tables_semantic(query: str, query_analysis: Dict[str, Any],
                                       all_tables: List[Dict[str, Any]]) -> List[Tuple[int, float]]:
    """
    使用LLM进行语义匹配找到相关表
    返回(table_id, relevance_score)元组列表
    """
    try:
        # 为LLM准备表信息
        tables_info = "\n".join([
            f"表ID: {t['id']} - 名称: {t['name']} - 描述: {t['description'] or '无描述'}"
            for t in all_tables
        ])

        # 准备提示
        prompt = f"""
        你是一名数据库专家，帮助为自然语言查询找到相关表。

        查询: "{query}"

        查询分析: {json.dumps(query_analysis, ensure_ascii=False)}

        可用表:
        {tables_info}

        请按相关性对表进行排序，返回包含table_id和relevance_score(0-10)的JSON数组。
        table_id必须是每个表描述开头显示的整数ID（例如"表ID: 123"）。
        只包含实际相关的表（分数>3）。格式：
        [
            {{
                "table_id": 123, // 表的整数ID，不是名称
                "relevance_score": 8.5, // 0-10之间的浮点数
                "reasoning": "为什么这个表相关的简要解释"
            }},
            ...
        ]
        """

        # 调用LLM
        response = await model_client.create([UserMessage(content=prompt, source="user")])
        response_text = response.content

        # 提取并解析JSON响应
        json_match = re.search(r'\[[\s\S]*\]', response_text)
        if json_match:
            json_str = json_match.group(0)
            ranked_tables = json.loads(json_str)

            # 确保每个表都有所需字段且table_id是整数
            valid_tables = []
            for t in ranked_tables:
                if "table_id" in t and "relevance_score" in t:
                    if t["relevance_score"] > 3:
                        table_id = t["table_id"]
                        if not isinstance(table_id, int):
                            try:
                                table_id = int(table_id)
                            except (ValueError, TypeError):
                                continue
                        valid_tables.append((table_id, t["relevance_score"]))

            return valid_tables
        else:
            return basic_table_matching(query, all_tables)
    except Exception as e:
        return basic_table_matching(query, all_tables)


def basic_table_matching(query: str, all_tables: List[Dict[str, Any]]) -> List[Tuple[int, float]]:
    """
    基本关键词匹配回退方法
    """
    keywords = extract_keywords(query)
    relevant_tables = []

    for table in all_tables:
        score = 0
        table_name = table["name"].lower()
        table_desc = (table["description"] or "").lower()

        for keyword in keywords:
            if keyword in table_name:
                score += 5  # 名称匹配更高分
            elif keyword in table_desc:
                score += 3  # 描述匹配较低分

        if score > 0:
            relevant_tables.append((table["id"], min(score, 10)))  # 最高10分

    return sorted(relevant_tables, key=lambda x: x[1], reverse=True)


async def filter_expanded_tables_with_llm(query: str, query_analysis: Dict[str, Any],
                                        expanded_tables: List[Tuple[int, str, str]],
                                        relevance_scores: Dict[int, float]) -> Set[Tuple[int, str, str]]:
    """
    使用LLM根据实际相关性过滤扩展表
    """
    try:
        # 准备扩展表信息
        tables_info = "\n".join([
            f"表ID: {t[0]}, 名称: {t[1]}, 描述: {t[2] or '无描述'}, 分数: {relevance_scores.get(t[0], 0)}"
            for t in expanded_tables
        ])

        # 准备提示
        prompt = f"""
        你是一名数据库专家，帮助确定相关表是否真正与查询相关。

        查询: "{query}"

        查询分析: {json.dumps(query_analysis, ensure_ascii=False)}

        以下表是通过关系连接找到的，但我们需要确定它们是否真正相关：
        {tables_info}

        请返回实际与回答查询相关的表ID的JSON数组。
        只包含回答查询所需的表。格式：
        [
            {{
                "table_id": table_id,
                "include": true/false,
                "reasoning": "为什么应该包含或排除此表的简要解释"
            }},
            ...
        ]
        """

        # 调用LLM
        response = await model_client.create([UserMessage(content=prompt, source="user")])
        response_text = response.content

        # 提取并解析JSON响应
        json_match = re.search(r'\[[\s\S]*\]', response_text)
        if json_match:
            json_str = json_match.group(0)
            filtered_tables = json.loads(json_str)

            # 获取应包含的表的ID
            include_ids = [t["table_id"] for t in filtered_tables if t.get("include", False)]

            # 返回应包含的原始表元组
            return set(t for t in expanded_tables if t[0] in include_ids)
        else:
            # 如果解析失败，包含所有扩展表
            return set(expanded_tables)
    except Exception as e:
        # 如果发生任何错误，包含所有扩展表
        return set(expanded_tables)


def format_schema_for_prompt(schema_context: Dict[str, Any]) -> str:
    """
    将表结构上下文格式化为LLM提示的字符串
    """
    tables = schema_context["tables"]
    columns = schema_context["columns"]
    relationships = schema_context["relationships"]

    # 按表分组列
    columns_by_table = {}
    for column in columns:
        table_name = column["table_name"]
        if table_name not in columns_by_table:
            columns_by_table[table_name] = []
        columns_by_table[table_name].append(column)

    # 格式化表结构
    schema_str = ""

    for table in tables:
        table_name = table["name"]
        table_desc = f" ({table['description']})" if table["description"] else ""

        schema_str += f"-- 表: {table_name}{table_desc}\n"
        schema_str += "-- 列:\n"

        if table_name in columns_by_table:
            for column in columns_by_table[table_name]:
                col_name = column["name"]
                col_type = column["type"]
                col_desc = f" ({column['description']})" if column["description"] else ""
                pk_flag = " PK" if column["is_primary_key"] else ""
                fk_flag = " FK" if column["is_foreign_key"] else ""

                schema_str += f"--   {col_name} {col_type}{pk_flag}{fk_flag}{col_desc}\n"

        schema_str += "\n"

    if relationships:
        schema_str += "-- 关系:\n"
        for rel in relationships:
            rel_type = f" ({rel['relationship_type']})" if rel["relationship_type"] else ""
            schema_str += f"-- {rel['source_table']}.{rel['source_column']} -> {rel['target_table']}.{rel['target_column']}{rel_type}\n"

    return schema_str


# 字段优先级配置
FIELD_PRIORITY_CONFIG = {
    # 公司相关字段优先级 (数字越大优先级越高)
    "company_fields": {
        "accounting_unit_name": 10,      # 最高优先级 - 首选字段
        "accounting_organization": 5,     # 较低优先级
    },

    # 科目相关字段优先级
    "account_fields": {
        "account_full_name": 10,         # 最高优先级 - 首选字段
        "account_name": 5,               # 较低优先级
        "account_code": 3                # 代码字段优先级较低
    }
}

# 语义映射分组 - 将相似含义的术语分组
SEMANTIC_FIELD_GROUPS = {
    "company_terms": {
        "preferred_field": "accounting_unit_name",
        "alternative_fields": ["accounting_organization"],
        "natural_language_terms": ["公司", "公司名称", "企业", "单位", "company", "company_name", "company_id"]
    },

    "account_name_terms": {
        "preferred_field": "account_full_name",
        "alternative_fields": ["account_name"],
        "natural_language_terms": ["科目名称", "会计科目名称", "科目全称", "account_name", "科目"]
    }
}


def select_preferred_field(natural_language_term: str, available_fields: List[str]) -> str:
    """
    根据自然语言术语和可用字段，智能选择最优字段
    """
    # 1. 检查是否属于已知的语义分组
    for group_name, group_config in SEMANTIC_FIELD_GROUPS.items():
        if natural_language_term.lower() in [term.lower() for term in group_config["natural_language_terms"]]:
            # 优先选择首选字段
            if group_config["preferred_field"] in available_fields:
                return group_config["preferred_field"]
            # 如果首选字段不可用，选择备选字段
            for alt_field in group_config["alternative_fields"]:
                if alt_field in available_fields:
                    return alt_field

    # 2. 如果不在语义分组中，返回第一个可用字段
    return available_fields[0] if available_fields else None


def get_value_mappings(db: Session, schema_context: Dict[str, Any]) -> Dict[str, Dict[str, str]]:
    """
    获取表结构上下文中列的值映射 - 支持字段优先级选择
    """
    mappings = {}
    # 用于跟踪冲突的映射术语
    term_to_fields = {}

    for column in schema_context["columns"]:
        column_id = column["id"]
        column_mappings = crud.value_mapping.get_by_column(db=db, column_id=column_id)

        if column_mappings:
            table_col = f"{column['table_name']}.{column['name']}"
            field_name = column['name']

            for mapping in column_mappings:
                nl_term = mapping.nl_term

                # 跟踪哪些字段映射到相同的自然语言术语
                if nl_term not in term_to_fields:
                    term_to_fields[nl_term] = []
                term_to_fields[nl_term].append(field_name)

    # 解决冲突：为每个术语选择优先级最高的字段
    resolved_mappings = {}
    for nl_term, fields in term_to_fields.items():
        if len(fields) > 1:
            # 存在冲突，选择优先级最高的字段
            preferred_field = select_preferred_field(nl_term, fields)
            if preferred_field:
                resolved_mappings[nl_term] = preferred_field
        else:
            # 无冲突，直接使用
            resolved_mappings[nl_term] = fields[0]

    # 重新构建映射字典，只包含解决冲突后的映射
    for column in schema_context["columns"]:
        column_id = column["id"]
        column_mappings = crud.value_mapping.get_by_column(db=db, column_id=column_id)

        if column_mappings:
            table_col = f"{column['table_name']}.{column['name']}"
            field_name = column['name']

            # 只添加被选中的映射
            field_mappings = {}
            for mapping in column_mappings:
                nl_term = mapping.nl_term
                if resolved_mappings.get(nl_term) == field_name:
                    field_mappings[nl_term] = mapping.db_value

            if field_mappings:
                mappings[table_col] = field_mappings

    return mappings


def apply_field_priority_corrections(sql: str) -> str:
    """
    应用字段优先级修正，确保使用正确的首选字段
    """
    import re

    # 字段优先级替换规则
    field_corrections = {
        # 公司相关字段：将 accounting_organization 替换为 accounting_unit_name
        r'\baccounting_organization\b': 'accounting_unit_name',

        # 科目相关字段：在特定上下文中将 account_name 替换为 account_full_name
        # 只在查询科目名称内容时替换，不影响其他用途
        r'\baccount_name\b(?=\s*(LIKE|=|IN)\s*[\'"][^\'\"]*[\u4e00-\u9fff])': 'account_full_name',

        # 禁用字段替换
        r'\bcompany_name\b': 'accounting_unit_name',
        r'\bcompany_id\b': 'accounting_unit_name',
    }

    corrected_sql = sql
    for pattern, replacement in field_corrections.items():
        corrected_sql = re.sub(pattern, replacement, corrected_sql, flags=re.IGNORECASE)

    return corrected_sql


def apply_subject_query_corrections(sql: str) -> str:
    """
    应用科目查询修正，确保科目相关查询使用正确的字段和语法
    """
    import re

    # 检测是否是科目相关查询
    subject_keywords = ['管理费用', '销售费用', '营业收入', '主营业务成本', '财务费用', '研发费用']
    is_subject_query = any(keyword in sql for keyword in subject_keywords)

    # 1. 移除错误的 account_code = 数值 条件（无论是否是科目查询）
    # 这是为了防止LLM幻觉生成具体的科目代码
    sql = re.sub(r'\baccount_code\s*=\s*[\'"]?\d+[\'"]?\s*(AND|OR)?\s*', '', sql, flags=re.IGNORECASE)

    if is_subject_query:
        # 2. 清理多余的 AND/OR
        sql = re.sub(r'\s+(AND|OR)\s+(AND|OR)\s+', r' \1 ', sql, flags=re.IGNORECASE)
        sql = re.sub(r'\bWHERE\s+(AND|OR)\s+', 'WHERE ', sql, flags=re.IGNORECASE)
        sql = re.sub(r'\s+(AND|OR)\s*\)', ')', sql, flags=re.IGNORECASE)

        # 3. 确保使用 account_full_name 而不是 account_name
        for keyword in subject_keywords:
            if keyword in sql:
                # 将 account_name LIKE '%keyword%' 替换为 account_full_name LIKE '%keyword%'
                pattern = rf'\baccount_name\s+LIKE\s+[\'"]%{re.escape(keyword)}%[\'"]'
                replacement = f"account_full_name LIKE '%{keyword}%'"
                sql = re.sub(pattern, replacement, sql, flags=re.IGNORECASE)

    # 4. 通用清理：移除可能导致语法错误的多余连接符
    sql = re.sub(r'\s+(AND|OR)\s+(AND|OR)\s+', r' \1 ', sql, flags=re.IGNORECASE)
    sql = re.sub(r'\bWHERE\s+(AND|OR)\s+', 'WHERE ', sql, flags=re.IGNORECASE)
    sql = re.sub(r'\s+(AND|OR)\s*\)', ')', sql, flags=re.IGNORECASE)

    return sql


def process_sql_with_value_mappings(sql: str, value_mappings: Dict[str, Dict[str, str]]) -> str:
    """
    处理SQL查询，将自然语言术语替换为数据库字段名 - 增强版支持字段优先级
    """
    if not value_mappings:
        # 即使没有值映射，也应用字段优先级修正和科目查询修正
        sql = apply_field_priority_corrections(sql)
        sql = apply_subject_query_corrections(sql)
        return sql

    import re

    # 第一步：应用字段优先级修正
    sql = apply_field_priority_corrections(sql)

    # 第二步：处理字段名替换 - 这是关键修复
    for column, mappings in value_mappings.items():
        table, col = column.split('.')

        for nl_term, db_value in mappings.items():
            # 跳过相同的术语，避免无意义替换
            if nl_term == db_value:
                continue

            # 1. 替换字段名（最重要的修复）
            # 匹配 SELECT, GROUP BY, ORDER BY 等位置的字段名
            # 使用单词边界确保精确匹配
            field_pattern = rf'\b{re.escape(nl_term)}\b'
            sql = re.sub(field_pattern, db_value, sql, flags=re.IGNORECASE)

            # 2. 处理值替换（保留原有功能）
            # 匹配 WHERE column = 'value' 模式
            value_pattern1 = rf"({re.escape(col)}\s*=\s*['\"])({re.escape(nl_term)})(['\"])"
            sql = re.sub(value_pattern1, f"\\1{db_value}\\3", sql, flags=re.IGNORECASE)

            # 匹配 WHERE table.column = 'value' 模式
            value_pattern2 = rf"({re.escape(table)}\.{re.escape(col)}\s*=\s*['\"])({re.escape(nl_term)})(['\"])"
            sql = re.sub(value_pattern2, f"\\1{db_value}\\3", sql, flags=re.IGNORECASE)

            # 匹配 LIKE 模式
            like_pattern1 = rf"({re.escape(col)}\s+LIKE\s+['\"])%?({re.escape(nl_term)})%?(['\"])"
            sql = re.sub(like_pattern1, f"\\1%{db_value}%\\3", sql, flags=re.IGNORECASE)

            like_pattern2 = rf"({re.escape(table)}\.{re.escape(col)}\s+LIKE\s+['\"])%?({re.escape(nl_term)})%?(['\"])"
            sql = re.sub(like_pattern2, f"\\1%{db_value}%\\3", sql, flags=re.IGNORECASE)

    # 应用科目查询专项修正
    sql = apply_subject_query_corrections(sql)

    return sql


def validate_sql(sql: str) -> bool:
    """
    验证SQL语法
    """
    try:
        parsed = sqlparse.parse(sql)
        if not parsed:
            return False

        # 检查是否是SELECT语句（为了安全）
        stmt = parsed[0]
        return stmt.get_type().upper() == 'SELECT'
    except Exception:
        return False


def extract_sql_from_llm_response(response: str) -> str:
    """
    从LLM响应中提取SQL查询
    """
    # 查找SQL代码块
    sql_match = re.search(r'```sql\n(.*?)\n```', response, re.DOTALL)
    if sql_match:
        return sql_match.group(1).strip()

    # 查找任何代码块
    code_match = re.search(r'```(.*?)```', response, re.DOTALL)
    if code_match:
        return code_match.group(1).strip()

    # 如果没有代码块，尝试找到类似SQL的内容
    lines = response.split('\n')
    sql_lines = []
    in_sql = False

    for line in lines:
        if line.strip().upper().startswith('SELECT'):
            in_sql = True

        if in_sql:
            sql_lines.append(line)

            if ';' in line:
                break

    if sql_lines:
        return '\n'.join(sql_lines)

    # 如果都失败了，返回整个响应
    return response


async def retrieve_relevant_schema(db: Session, connection_id: int, query: str) -> Dict[str, Any]:
    """
    基于自然语言查询检索相关的表结构信息
    使用Neo4j图数据库和LLM找到相关表和列
    优化版本：使用连接池和缓存
    增强监控版本：7个关键节点全程监控
    """
    # 初始化监控数据
    monitoring_data = {
        "connection_id": connection_id,
        "query": query[:100] + "..." if len(query) > 100 else query,
        "start_time": time.time(),
        "pipeline_stages": {},
        "errors": [],
        "warnings": []
    }

    try:
        logger.info(f"🚀 [Schema检索] 开始处理 - 连接ID: {connection_id}, 查询: {query[:50]}...")

        # 节点1: 缓存获取
        stage_start = time.time()
        logger.info("📋 [节点1/7] 尝试从增强缓存获取Schema信息...")

        from app.services.enhanced_cache_service import enhanced_cache
        cached_result = await enhanced_cache.get_schema_context_cached(connection_id, query)

        monitoring_data["pipeline_stages"]["cache_lookup"] = {
            "duration": time.time() - stage_start,
            "status": "SUCCESS" if cached_result and cached_result.get('tables') else "MISS",
            "result_count": len(cached_result.get('tables', [])) if cached_result else 0
        }

        if cached_result and cached_result.get('tables'):
            logger.info(f"✅ [节点1/7] 缓存命中 - 找到 {len(cached_result['tables'])} 个表")
            monitoring_data["total_duration"] = time.time() - monitoring_data["start_time"]
            monitoring_data["source"] = "CACHE"
            logger.info(f"🎯 [Schema检索] 完成(缓存) - 耗时: {monitoring_data['total_duration']:.3f}s")
            return cached_result
        else:
            logger.info("⚠️ [节点1/7] 缓存未命中，继续Neo4j检索流程")
            monitoring_data["warnings"].append("缓存未命中")

        # 节点2: LLM查询分析
        stage_start = time.time()
        logger.info("🧠 [节点2/7] 使用LLM分析查询意图和实体...")

        try:
            query_analysis = await analyze_query_with_llm(query)
            monitoring_data["pipeline_stages"]["llm_analysis"] = {
                "duration": time.time() - stage_start,
                "status": "SUCCESS",
                "entities_found": len(query_analysis.get("entities", [])),
                "intent": query_analysis.get("intent", "unknown")
            }
            logger.info(f"✅ [节点2/7] LLM分析完成 - 找到 {len(query_analysis.get('entities', []))} 个实体")
        except Exception as e:
            monitoring_data["pipeline_stages"]["llm_analysis"] = {
                "duration": time.time() - stage_start,
                "status": "ERROR",
                "error": str(e)
            }
            monitoring_data["errors"].append(f"LLM分析失败: {str(e)}")
            logger.error(f"❌ [节点2/7] LLM分析失败: {str(e)}")
            # 使用默认分析结果
            query_analysis = {"entities": [], "intent": "unknown"}

        # 节点3: Neo4j连接池获取
        stage_start = time.time()
        logger.info("🔗 [节点3/7] 获取Neo4j连接池...")

        try:
            from app.services.neo4j_connection_pool import get_neo4j_pool
            neo4j_pool = await get_neo4j_pool()
            monitoring_data["pipeline_stages"]["neo4j_connection"] = {
                "duration": time.time() - stage_start,
                "status": "SUCCESS",
                "pool_initialized": neo4j_pool._initialized
            }
            logger.info(f"✅ [节点3/7] Neo4j连接池获取成功 - 初始化状态: {neo4j_pool._initialized}")
        except Exception as e:
            monitoring_data["pipeline_stages"]["neo4j_connection"] = {
                "duration": time.time() - stage_start,
                "status": "ERROR",
                "error": str(e)
            }
            monitoring_data["errors"].append(f"Neo4j连接池获取失败: {str(e)}")
            logger.error(f"❌ [节点3/7] Neo4j连接池获取失败: {str(e)}")
            raise Exception(f"Neo4j连接池不可用: {str(e)}")

        # 初始化数据结构
        relevant_tables_dict = {}
        relevant_columns = set()
        table_relevance_scores = {}

        # 节点4: 基础表数据获取
        stage_start = time.time()
        logger.info("📊 [节点4/7] 从Neo4j获取基础表数据...")

        try:
            all_tables = await neo4j_pool.execute_read_query(
                """
                MATCH (t:Table {connection_id: $connection_id})
                RETURN t.id AS id, t.name AS name, t.description AS description
                """,
                {'connection_id': connection_id}
            )
            monitoring_data["pipeline_stages"]["base_tables_fetch"] = {
                "duration": time.time() - stage_start,
                "status": "SUCCESS",
                "tables_found": len(all_tables)
            }
            logger.info(f"✅ [节点4/7] 基础表数据获取完成 - 找到 {len(all_tables)} 个表")

            if not all_tables:
                monitoring_data["warnings"].append("Neo4j中未找到任何表数据")
                logger.warning("⚠️ [节点4/7] Neo4j中未找到任何表数据，可能需要数据同步")

        except Exception as e:
            monitoring_data["pipeline_stages"]["base_tables_fetch"] = {
                "duration": time.time() - stage_start,
                "status": "ERROR",
                "error": str(e)
            }
            monitoring_data["errors"].append(f"基础表数据获取失败: {str(e)}")
            logger.error(f"❌ [节点4/7] 基础表数据获取失败: {str(e)}")
            all_tables = []

        # 节点5: 语义搜索相关表
        stage_start = time.time()
        logger.info("🔍 [节点5/7] 使用语义搜索找到相关表...")

        try:
            relevant_table_ids = await find_relevant_tables_semantic(query, query_analysis, all_tables)
            monitoring_data["pipeline_stages"]["semantic_search"] = {
                "duration": time.time() - stage_start,
                "status": "SUCCESS",
                "relevant_tables_found": len(relevant_table_ids)
            }
            logger.info(f"✅ [节点5/7] 语义搜索完成 - 找到 {len(relevant_table_ids)} 个相关表")
        except Exception as e:
            monitoring_data["pipeline_stages"]["semantic_search"] = {
                "duration": time.time() - stage_start,
                "status": "ERROR",
                "error": str(e)
            }
            monitoring_data["errors"].append(f"语义搜索失败: {str(e)}")
            logger.error(f"❌ [节点5/7] 语义搜索失败: {str(e)}")
            relevant_table_ids = []

        # 7. 按ID获取表并设置相关性分数
        for table_id, relevance_score in relevant_table_ids:
            # 确保table_id是整数类型
            if not isinstance(table_id, int):
                try:
                    table_id = int(table_id)
                except (ValueError, TypeError):
                    continue

            # 查找表信息
            table_info = next((t for t in all_tables if t["id"] == table_id), None)
            if table_info:
                # 在字典中存储表，以ID为键
                relevant_tables_dict[table_info["id"]] = (
                    table_info["id"], table_info["name"], table_info["description"]
                )
                table_relevance_scores[table_info["id"]] = relevance_score

        # 节点6: 相关列检索
        stage_start = time.time()
        logger.info("🏛️ [节点6/7] 检索与查询相关的列...")

        columns_found = 0
        try:
            # 方法1: 基于实体的列检索
            for entity in query_analysis["entities"]:
                # 搜索匹配实体名称或描述的列
                columns_data = await neo4j_pool.execute_read_query(
                    """
                    MATCH (c:Column {connection_id: $connection_id})
                    WHERE toLower(c.name) CONTAINS $entity OR toLower(c.description) CONTAINS $entity
                    MATCH (t:Table)-[:HAS_COLUMN]->(c)
                    RETURN c.id AS id, c.name AS name, c.type AS type, c.description AS description,
                           c.is_pk AS is_pk, c.is_fk AS is_fk, t.id AS table_id, t.name AS table_name
                    """,
                    {
                        'connection_id': connection_id,
                        'entity': entity.lower()
                    }
                )

                for record in columns_data:
                    relevant_columns.add((
                        record["id"], record["name"], record["type"], record["description"],
                        record["is_pk"], record["is_fk"], record["table_id"], record["table_name"]
                    ))
                    # 添加表或更新（如果已存在且有更好的描述）
                    if record["table_id"] not in relevant_tables_dict or not relevant_tables_dict[record["table_id"]][2]:
                        relevant_tables_dict[record["table_id"]] = (
                            record["table_id"], record["table_name"], ""
                        )
                    # 为有匹配列的表增加相关性分数
                    table_relevance_scores[record["table_id"]] = table_relevance_scores.get(record["table_id"], 0) + 0.5
                    columns_found += 1

            # 方法2: 如果基于实体的检索没有找到足够的列，则获取相关表的所有列
            if columns_found < 10 and relevant_tables_dict:  # 如果找到的列少于10个
                logger.info("🔄 [节点6/7] 实体检索列数较少，获取相关表的所有列...")

                table_ids = list(relevant_tables_dict.keys())
                all_table_columns = await neo4j_pool.execute_read_query(
                    """
                    MATCH (t:Table {connection_id: $connection_id})-[:HAS_COLUMN]->(c:Column)
                    WHERE t.id IN $table_ids
                    RETURN c.id AS id, c.name AS name, c.type AS type, c.description AS description,
                           c.is_pk AS is_pk, c.is_fk AS is_fk, t.id AS table_id, t.name AS table_name
                    """,
                    {
                        'connection_id': connection_id,
                        'table_ids': table_ids
                    }
                )

                additional_columns = 0
                for record in all_table_columns:
                    # 检查是否已经存在（避免重复）
                    column_key = (record["id"], record["name"], record["type"], record["description"],
                                record["is_pk"], record["is_fk"], record["table_id"], record["table_name"])
                    if column_key not in relevant_columns:
                        relevant_columns.add(column_key)
                        additional_columns += 1

                columns_found += additional_columns
                logger.info(f"🔄 [节点6/7] 从相关表获取了额外 {additional_columns} 个列")

            # 方法3: 如果仍然没有找到列，则获取所有表的列（最后的回退方案）
            if columns_found == 0:
                logger.warning("⚠️ [节点6/7] 未找到任何列，使用回退方案获取所有表的列...")

                all_columns_fallback = await neo4j_pool.execute_read_query(
                    """
                    MATCH (t:Table {connection_id: $connection_id})-[:HAS_COLUMN]->(c:Column)
                    RETURN c.id AS id, c.name AS name, c.type AS type, c.description AS description,
                           c.is_pk AS is_pk, c.is_fk AS is_fk, t.id AS table_id, t.name AS table_name
                    LIMIT 100
                    """,
                    {'connection_id': connection_id}
                )

                for record in all_columns_fallback:
                    relevant_columns.add((
                        record["id"], record["name"], record["type"], record["description"],
                        record["is_pk"], record["is_fk"], record["table_id"], record["table_name"]
                    ))
                    # 确保表也在相关表字典中
                    if record["table_id"] not in relevant_tables_dict:
                        relevant_tables_dict[record["table_id"]] = (
                            record["table_id"], record["table_name"], ""
                        )
                    columns_found += 1

                logger.info(f"🔄 [节点6/7] 回退方案获取了 {len(all_columns_fallback)} 个列")

            monitoring_data["pipeline_stages"]["column_retrieval"] = {
                "duration": time.time() - stage_start,
                "status": "SUCCESS",
                "entities_processed": len(query_analysis["entities"]),
                "columns_found": columns_found,
                "retrieval_method": "entity_based" if columns_found > 0 and len(relevant_tables_dict) > 0 else "fallback"
            }
            logger.info(f"✅ [节点6/7] 列检索完成 - 处理 {len(query_analysis['entities'])} 个实体，找到 {columns_found} 个相关列")

        except Exception as e:
            monitoring_data["pipeline_stages"]["column_retrieval"] = {
                "duration": time.time() - stage_start,
                "status": "ERROR",
                "error": str(e)
            }
            monitoring_data["errors"].append(f"列检索失败: {str(e)}")
            logger.error(f"❌ [节点6/7] 列检索失败: {str(e)}")

        # 节点7: 关系扩展和LLM过滤
        stage_start = time.time()
        logger.info("🔗 [节点7/7] 扩展相关表关系并进行LLM过滤...")

        expanded_tables_count = 0
        filtered_tables_count = 0

        try:
            if relevant_tables_dict or relevant_columns:
                table_ids = list(relevant_tables_dict.keys())

                # 通过外键找到连接的表（1跳）- 使用连接池
                if table_ids:
                    try:
                        related_tables_data = await neo4j_pool.execute_read_query(
                            """
                            MATCH (t1:Table {connection_id: $connection_id})-[:HAS_COLUMN]->
                                  (c1:Column)-[:REFERENCES]->
                                  (c2:Column)<-[:HAS_COLUMN]-(t2:Table {connection_id: $connection_id})
                            WHERE t1.id IN $table_ids AND NOT t2.id IN $table_ids
                            RETURN t2.id AS id, t2.name AS name, t2.description AS description,
                                   c1.id AS source_column_id, c1.name AS source_column_name,
                                   c2.id AS target_column_id, c2.name AS target_column_name,
                                   t1.id AS source_table_id
                            """,
                            {
                                'connection_id': connection_id,
                                'table_ids': table_ids
                            }
                        )
                        expanded_tables_count = len(related_tables_data)
                        logger.info(f"🔗 [节点7/7] 通过外键关系找到 {expanded_tables_count} 个扩展表")

                    except Exception as e:
                        # 如果查询失败（比如REFERENCES关系不存在），记录警告但继续执行
                        logger.warning(f"⚠️ [节点7/7] 外键关系查询失败，可能数据库中没有REFERENCES关系: {str(e)}")
                        monitoring_data["warnings"].append(f"外键关系查询失败: {str(e)}")
                        related_tables_data = []

                    for record in related_tables_data:
                        # 添加表或更新（如果已存在且有更好的描述）
                        if record["id"] not in relevant_tables_dict or (
                            not relevant_tables_dict[record["id"]][2] and record["description"]
                        ):
                            relevant_tables_dict[record["id"]] = (
                                record["id"], record["name"], record["description"]
                            )
                        # 相关表基于源表的分数获得相关性分数
                        source_score = table_relevance_scores.get(record["source_table_id"], 0)
                        table_relevance_scores[record["id"]] = source_score * 0.7  # 相关表分数降低

                # 使用LLM评估扩展表是否真正与查询相关
                expanded_tables = [t for t in relevant_tables_dict.values() if t[0] not in table_ids]
                if expanded_tables:
                    try:
                        filtered_expanded_tables = await filter_expanded_tables_with_llm(
                            query, query_analysis, expanded_tables, table_relevance_scores
                        )
                        filtered_tables_count = len(filtered_expanded_tables)
                        logger.info(f"🧠 [节点7/7] LLM过滤后保留 {filtered_tables_count}/{len(expanded_tables)} 个扩展表")

                        # 只保留相关表
                        filtered_table_ids = set(table_ids).union({t[0] for t in filtered_expanded_tables})
                        relevant_tables_dict = {
                            tid: t for tid, t in relevant_tables_dict.items() if tid in filtered_table_ids
                        }
                    except Exception as e:
                        logger.warning(f"⚠️ [节点7/7] LLM过滤失败，保留所有扩展表: {str(e)}")
                        monitoring_data["warnings"].append(f"LLM过滤失败: {str(e)}")
                        filtered_tables_count = len(expanded_tables)

            monitoring_data["pipeline_stages"]["relationship_expansion"] = {
                "duration": time.time() - stage_start,
                "status": "SUCCESS",
                "expanded_tables_found": expanded_tables_count,
                "filtered_tables_kept": filtered_tables_count,
                "final_relevant_tables": len(relevant_tables_dict)
            }
            logger.info(f"✅ [节点7/7] 关系扩展完成 - 最终确定 {len(relevant_tables_dict)} 个相关表")

        except Exception as e:
            monitoring_data["pipeline_stages"]["relationship_expansion"] = {
                "duration": time.time() - stage_start,
                "status": "ERROR",
                "error": str(e)
            }
            monitoring_data["errors"].append(f"关系扩展失败: {str(e)}")
            logger.error(f"❌ [节点7/7] 关系扩展失败: {str(e)}")

        # 注意：不再需要手动关闭driver，连接池会自动管理连接

        # 11. 按相关性分数排序表
        sorted_tables = sorted(
            relevant_tables_dict.values(),
            key=lambda t: table_relevance_scores.get(t[0], 0),
            reverse=True
        )

        # 转换为字典列表
        tables_list = [{"id": t[0], "name": t[1], "description": t[2]} for t in sorted_tables]

        # 如果没有找到相关表，返回所有表
        if not tables_list:
            all_tables_from_db = crud.schema_table.get_by_connection(db=db, connection_id=connection_id)
            tables_list = [
                {
                    "id": table.id,
                    "name": table.table_name,
                    "description": table.description or ""
                }
                for table in all_tables_from_db
            ]

        columns_list = []

        # 优先使用Neo4j检索到的列数据
        if relevant_columns:
            logger.info(f"📊 使用Neo4j检索到的 {len(relevant_columns)} 个列")
            for column_tuple in relevant_columns:
                col_id, col_name, col_type, col_desc, is_pk, is_fk, table_id, table_name = column_tuple
                columns_list.append({
                    "id": col_id,
                    "name": col_name,
                    "type": col_type,
                    "description": col_desc or "",
                    "is_primary_key": is_pk,
                    "is_foreign_key": is_fk,
                    "table_id": table_id,
                    "table_name": table_name
                })
        else:
            # 回退到SQLite获取列数据
            logger.info("📊 回退到SQLite获取列数据")
            for table in tables_list:
                table_columns = crud.schema_column.get_by_table(db=db, table_id=table["id"])
                for column in table_columns:
                    columns_list.append({
                        "id": column.id,
                        "name": column.column_name,
                        "type": column.data_type,
                        "description": column.description,
                        "is_primary_key": column.is_primary_key,
                        "is_foreign_key": column.is_foreign_key,
                        "table_id": table["id"],
                        "table_name": table["name"]
                    })

        # 获取表之间的关系
        relationships_list = []
        table_ids = [t["id"] for t in tables_list]

        # 如果返回所有表，则获取所有关系
        all_tables_count = len(crud.schema_table.get_by_connection(db=db, connection_id=connection_id))
        if len(tables_list) == all_tables_count:
            all_relationships = crud.schema_relationship.get_by_connection(db=db, connection_id=connection_id)

            for rel in all_relationships:
                source_table = next((t for t in tables_list if t["id"] == rel.source_table_id), None)
                target_table = next((t for t in tables_list if t["id"] == rel.target_table_id), None)
                source_column = next((c for c in columns_list if c["id"] == rel.source_column_id), None)
                target_column = next((c for c in columns_list if c["id"] == rel.target_column_id), None)

                if source_table and target_table and source_column and target_column:
                    relationships_list.append({
                        "id": rel.id,
                        "source_table": source_table["name"],
                        "source_column": source_column["name"],
                        "target_table": target_table["name"],
                        "target_column": target_column["name"],
                        "relationship_type": rel.relationship_type
                    })
        else:
            # 如果只返回相关表，则获取这些表之间的关系
            for table in tables_list:
                source_rels = crud.schema_relationship.get_by_source_table(db=db, source_table_id=table["id"])
                target_rels = crud.schema_relationship.get_by_target_table(db=db, target_table_id=table["id"])

                for rel in source_rels + target_rels:
                    # 只包含相关表集中的表之间的关系
                    if rel.source_table_id in table_ids and rel.target_table_id in table_ids:
                        source_table = next((t for t in tables_list if t["id"] == rel.source_table_id), None)
                        target_table = next((t for t in tables_list if t["id"] == rel.target_table_id), None)
                        source_column = next((c for c in columns_list if c["id"] == rel.source_column_id), None)
                        target_column = next((c for c in columns_list if c["id"] == rel.target_column_id), None)

                        if source_table and target_table and source_column and target_column:
                            # 确保不重复添加关系
                            rel_dict = {
                                "id": rel.id,
                                "source_table": source_table["name"],
                                "source_column": source_column["name"],
                                "target_table": target_table["name"],
                                "target_column": target_column["name"],
                                "relationship_type": rel.relationship_type
                            }
                            if rel_dict not in relationships_list:
                                relationships_list.append(rel_dict)

        # 最终结果构建和监控总结
        final_result = {
            "tables": tables_list,
            "columns": columns_list,
            "relationships": relationships_list
        }

        # 添加监控数据到结果中（调试模式）
        monitoring_data["total_duration"] = time.time() - monitoring_data["start_time"]
        monitoring_data["source"] = "NEO4J"
        monitoring_data["final_result_stats"] = {
            "tables_count": len(tables_list),
            "columns_count": len(columns_list),
            "relationships_count": len(relationships_list)
        }

        # 记录完整的监控信息
        logger.info(f"🎯 [Schema检索] 完成 - 总耗时: {monitoring_data['total_duration']:.3f}s")
        logger.info(f"📊 [Schema检索] 结果统计: {len(tables_list)}表/{len(columns_list)}列/{len(relationships_list)}关系")

        # 如果有错误或警告，记录详细信息
        if monitoring_data["errors"]:
            logger.error(f"⚠️ [Schema检索] 发现 {len(monitoring_data['errors'])} 个错误: {monitoring_data['errors']}")
        if monitoring_data["warnings"]:
            logger.warning(f"⚠️ [Schema检索] 发现 {len(monitoring_data['warnings'])} 个警告: {monitoring_data['warnings']}")

        # 在调试模式下，将监控数据添加到结果中
        if logger.isEnabledFor(logging.DEBUG):
            final_result["_monitoring"] = monitoring_data

        return final_result

    except Exception as e:
        # 记录完整的错误监控信息
        monitoring_data["total_duration"] = time.time() - monitoring_data["start_time"]
        monitoring_data["final_error"] = str(e)

        logger.error(f"❌ [Schema检索] 严重错误 - 耗时: {monitoring_data['total_duration']:.3f}s")
        logger.error(f"❌ [Schema检索] 错误详情: {str(e)}")
        logger.error(f"❌ [Schema检索] 管道状态: {monitoring_data['pipeline_stages']}")

        # 尝试从SQLite获取回退数据
        try:
            logger.info("🔄 [Schema检索] 尝试SQLite回退方案...")
            all_tables_from_db = crud.schema_table.get_by_connection(db=db, connection_id=connection_id)
            if all_tables_from_db:
                fallback_tables = [
                    {
                        "id": table.id,
                        "name": table.table_name,
                        "description": table.description or ""
                    }
                    for table in all_tables_from_db
                ]

                fallback_columns = []
                for table in fallback_tables:
                    table_columns = crud.schema_column.get_by_table(db=db, table_id=table["id"])
                    for column in table_columns:
                        fallback_columns.append({
                            "id": column.id,
                            "name": column.column_name,
                            "type": column.data_type,
                            "description": column.description,
                            "is_primary_key": column.is_primary_key,
                            "is_foreign_key": column.is_foreign_key,
                            "table_id": table["id"],
                            "table_name": table["name"]
                        })

                logger.info(f"✅ [Schema检索] SQLite回退成功 - {len(fallback_tables)}表/{len(fallback_columns)}列")

                fallback_result = {
                    "tables": fallback_tables,
                    "columns": fallback_columns,
                    "relationships": [],
                    "_fallback": True,
                    "_original_error": str(e)
                }

                if logger.isEnabledFor(logging.DEBUG):
                    fallback_result["_monitoring"] = monitoring_data

                return fallback_result
            else:
                logger.error("❌ [Schema检索] SQLite回退也失败 - 未找到任何表数据")

        except Exception as fallback_error:
            logger.error(f"❌ [Schema检索] SQLite回退失败: {str(fallback_error)}")

        raise Exception(f"检索表结构上下文时出错: {str(e)}")


def get_financial_metadata(table_name: str = "financial_data") -> Dict[str, Any]:
    """从元数据库获取财务表的元数据信息"""

    # 检查是否启用元数据增强
    if not getattr(settings, 'ENABLE_METADATA_ENHANCEMENT', True):
        return {"has_metadata": False}

    try:
        # 获取元数据库路径
        metadata_db_path = getattr(settings, 'METADATA_DB_PATH', settings.SQLITE_DB_PATH)

        conn = sqlite3.connect(metadata_db_path)
        cursor = conn.cursor()

        metadata = {
            "table_description": None,
            "meta_column_descriptions": [],
            "meta_business_rules": [],
            "has_metadata": False
        }

        # 检查元数据表是否存在
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='meta_table_descriptions'")
        if not cursor.fetchone():
            logger.warning("元数据表不存在于数据库中")
            conn.close()
            return metadata

        # 查询表描述
        cursor.execute("""
            SELECT description, business_purpose, data_scale
            FROM meta_table_descriptions
            WHERE table_name = ?
        """, (table_name,))

        table_desc = cursor.fetchone()
        if table_desc:
            metadata["table_description"] = {
                "description": table_desc[0],
                "business_purpose": table_desc[1],
                "data_scale": table_desc[2]
            }

        # 查询字段描述
        cursor.execute("""
            SELECT column_name, chinese_name, description, data_type,
                   business_rules, ai_understanding_points
            FROM meta_column_descriptions
            WHERE table_name = ?
            ORDER BY column_name
        """, (table_name,))

        for col in cursor.fetchall():
            metadata["meta_column_descriptions"].append({
                "column_name": col[0],
                "chinese_name": col[1],
                "description": col[2],
                "data_type": col[3],
                "meta_business_rules": col[4],
                "ai_understanding_points": col[5]
            })

        # 查询业务规则
        cursor.execute("""
            SELECT rule_category, rule_description, sql_example, importance_level
            FROM meta_business_rules
            WHERE table_name = ?
            ORDER BY
                CASE importance_level
                    WHEN 'CRITICAL' THEN 1
                    WHEN 'HIGH' THEN 2
                    WHEN 'MEDIUM' THEN 3
                    ELSE 4
                END
        """, (table_name,))

        for rule in cursor.fetchall():
            metadata["meta_business_rules"].append({
                "category": rule[0],
                "description": rule[1],
                "sql_example": rule[2],
                "importance": rule[3]
            })

        metadata["has_metadata"] = True
        conn.close()

        logger.info(f"成功加载元数据: {len(metadata['meta_column_descriptions'])}个字段, {len(metadata['meta_business_rules'])}个规则")
        return metadata

    except Exception as e:
        logger.error(f"获取元数据失败: {e}")
        return {"has_metadata": False}


def enhance_schema_with_metadata(schema_context: Dict[str, Any], metadata: Dict[str, Any]) -> Dict[str, Any]:
    """使用元数据增强schema上下文"""

    if not metadata.get("has_metadata"):
        return schema_context

    try:
        # 增强表信息
        for table in schema_context.get("tables", []):
            if table.get("name") == "financial_data":
                # 添加表描述
                if metadata.get("table_description"):
                    table["enhanced_description"] = metadata["table_description"]["description"]
                    table["business_purpose"] = metadata["table_description"]["business_purpose"]

                # 增强列信息
                column_meta_map = {col["column_name"]: col for col in metadata.get("meta_column_descriptions", [])}

                for column in table.get("columns", []):
                    col_name = column.get("column_name")
                    if col_name in column_meta_map:
                        meta_col = column_meta_map[col_name]
                        column["chinese_name"] = meta_col["chinese_name"]
                        column["enhanced_description"] = meta_col["ai_understanding_points"]
                        column["meta_business_rules"] = meta_col["meta_business_rules"]

        logger.info("Schema上下文已使用元数据增强")
        return schema_context

    except Exception as e:
        logger.error(f"Schema增强失败: {e}")
        return schema_context
