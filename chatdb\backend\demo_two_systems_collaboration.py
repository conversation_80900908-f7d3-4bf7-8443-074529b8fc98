#!/usr/bin/env python3
"""
演示valuemapping表和subject_keywords配置系统的协同工作
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def demo_systems_collaboration():
    """演示两个系统的协同工作"""
    print("🎭 演示valuemapping表和subject_keywords配置系统的协同工作")
    print("=" * 80)
    
    # 模拟用户查询
    user_query = "分析2024年11月各公司的管理费用"
    print(f"👤 用户查询: {user_query}")
    
    print(f"\n" + "="*60)
    print("🔍 阶段1: subject_keywords系统 - 查询意图识别")
    print("="*60)
    
    try:
        from app.services.text2sql_service import detect_subject_query_intent
        
        # 检测查询意图
        intent = detect_subject_query_intent(user_query)
        
        print(f"📋 意图检测结果:")
        print(f"  有科目查询: {'✅' if intent['has_subject_query'] else '❌'}")
        
        if intent['subjects']:
            for subject in intent['subjects']:
                print(f"  检测到科目: {subject['standard_name']}")
                print(f"  关键词: '{subject['keyword']}'")
                print(f"  生成条件: {subject['condition']}")
        
        # 模拟生成的特殊提示
        if intent['has_subject_query']:
            special_prompt = f"""
🚨 科目查询特别提醒:
检测到您的查询涉及特定科目，请注意：
- financial_data表包含所有科目的数据，不仅仅是您要查询的科目
- 必须添加科目筛选条件，否则会返回所有科目的汇总数据
- 检测到的科目及对应条件：
  * '{intent['subjects'][0]['keyword']}' → 必须添加条件: `{intent['subjects'][0]['condition']}`

⚠️ 强制要求: 生成的SQL必须包含上述科目筛选条件！
"""
            print(f"📝 生成的特殊提示:")
            print(special_prompt)
        
    except Exception as e:
        print(f"❌ 意图检测失败: {e}")
        return False
    
    print(f"\n" + "="*60)
    print("🔍 阶段2: valuemapping系统 - 字段映射准备")
    print("="*60)
    
    try:
        from app.db.session import SessionLocal
        from app.services.text2sql_utils import get_value_mappings
        
        # 模拟schema_context（简化版）
        schema_context = {
            "columns": [
                {"id": 226, "name": "accounting_unit_name", "table_name": "financial_data"},
                {"id": 228, "name": "account_full_name", "table_name": "financial_data"},
                {"id": 230, "name": "debit_amount", "table_name": "financial_data"}
            ]
        }
        
        # 获取值映射
        db = SessionLocal()
        value_mappings = get_value_mappings(db, schema_context)
        db.close()
        
        print(f"📋 valuemapping系统准备的映射:")
        for table_col, mappings in value_mappings.items():
            print(f"  {table_col}:")
            for nl_term, db_value in list(mappings.items())[:2]:  # 只显示前2个
                print(f"    '{nl_term}' → '{db_value}'")
        
    except Exception as e:
        print(f"❌ 值映射获取失败: {e}")
        value_mappings = {}
    
    print(f"\n" + "="*60)
    print("🤖 阶段3: LLM生成SQL（模拟）")
    print("="*60)
    
    # 模拟LLM在接收到增强提示后生成的SQL
    print(f"📝 LLM接收到的提示包含:")
    print(f"  1. 基础Schema信息")
    print(f"  2. valuemapping字段映射提示")
    print(f"  3. subject_keywords科目筛选提示 ← 关键！")
    
    # 模拟生成的SQL
    generated_sql = """
SELECT 
    accounting_unit_name AS 公司名称,
    SUM(debit_amount) AS 管理费用总额
FROM financial_data 
WHERE year = 2024 
    AND month = 11 
    AND account_full_name LIKE '%管理费用%'
GROUP BY accounting_unit_name 
ORDER BY 管理费用总额 DESC;
"""
    
    print(f"🎯 LLM生成的SQL:")
    print(generated_sql)
    
    print(f"✅ 关键成功点:")
    print(f"  - 使用了正确的字段名 accounting_unit_name")
    print(f"  - 包含了科目筛选条件 account_full_name LIKE '%管理费用%'")
    print(f"  - SQL结构完整且逻辑正确")
    
    print(f"\n" + "="*60)
    print("🔧 阶段4: SQL后处理（valuemapping系统）")
    print("="*60)
    
    try:
        from app.services.text2sql_utils import process_sql_with_value_mappings
        
        # 应用值映射后处理
        processed_sql = process_sql_with_value_mappings(generated_sql, value_mappings)
        
        print(f"📝 后处理结果:")
        if processed_sql != generated_sql:
            print(f"  SQL已被修正")
            print(f"修正后的SQL:")
            print(processed_sql)
        else:
            print(f"  SQL无需修正（已经正确）")
        
    except Exception as e:
        print(f"❌ SQL后处理失败: {e}")
        processed_sql = generated_sql
    
    print(f"\n" + "="*60)
    print("📊 对比分析：两个系统的不同作用")
    print("="*60)
    
    print(f"🔸 如果只有valuemapping系统:")
    print(f"  问题：LLM可能生成 SELECT SUM(debit_amount) WHERE year=2024")
    print(f"  结果：返回所有科目的汇总，不仅仅是管理费用")
    print(f"  原因：缺少科目筛选意图识别")
    
    print(f"\n🔸 如果只有subject_keywords系统:")
    print(f"  问题：LLM可能使用错误的字段名")
    print(f"  结果：SQL语法错误或查询错误字段")
    print(f"  原因：缺少字段级别的值映射")
    
    print(f"\n🔸 两个系统协同工作:")
    print(f"  ✅ subject_keywords确保包含科目筛选条件")
    print(f"  ✅ valuemapping确保使用正确的字段名")
    print(f"  ✅ 生成准确、完整的SQL查询")
    
    return True

def demo_different_scenarios():
    """演示不同场景下两个系统的作用"""
    print(f"\n" + "="*80)
    print("🎯 不同场景下的系统作用演示")
    print("="*80)
    
    scenarios = [
        {
            "name": "纯字段映射场景",
            "query": "查询所有公司的科目名称",
            "subject_keywords_role": "无作用（非科目筛选查询）",
            "valuemapping_role": "映射'公司'→'accounting_unit_name'，'科目名称'→'account_full_name'"
        },
        {
            "name": "纯科目筛选场景", 
            "query": "分析管理费用趋势",
            "subject_keywords_role": "识别'管理费用'，要求添加account_full_name LIKE '%管理费用%'",
            "valuemapping_role": "后处理阶段确保字段名正确"
        },
        {
            "name": "复合场景",
            "query": "查询各公司的管理费用明细",
            "subject_keywords_role": "识别'管理费用'科目筛选需求",
            "valuemapping_role": "映射'公司'→'accounting_unit_name'等字段"
        }
    ]
    
    for i, scenario in enumerate(scenarios, 1):
        print(f"\n📋 场景 {i}: {scenario['name']}")
        print(f"  查询: {scenario['query']}")
        print(f"  subject_keywords作用: {scenario['subject_keywords_role']}")
        print(f"  valuemapping作用: {scenario['valuemapping_role']}")

def main():
    """主函数"""
    print("🚀 两个系统协同工作演示")
    print("=" * 80)
    
    # 主要演示
    success = demo_systems_collaboration()
    
    # 不同场景演示
    demo_different_scenarios()
    
    print(f"\n" + "="*80)
    print("💡 总结：两个系统的设计哲学")
    print("="*80)
    
    print(f"""
🎯 设计原则：
1. **分层处理**: 不同阶段解决不同问题
2. **职责分离**: 避免功能重叠和耦合
3. **互补增强**: 两个系统协同提升整体效果

🔧 技术优势：
1. **subject_keywords**: 解决"查询意图理解"问题
   - 识别用户真正想要什么
   - 指导SQL结构生成
   - 防止遗漏关键筛选条件

2. **valuemapping**: 解决"字段名称映射"问题
   - 确保使用正确的数据库字段名
   - 处理自然语言到SQL的转换
   - 应用字段优先级规则

🚀 协同效果：
- 准确性提升：两层验证确保SQL正确性
- 可维护性：配置分离，便于独立维护
- 可扩展性：新增科目或字段映射互不影响
""")
    
    if success:
        print(f"\n✅ 演示完成！两个系统协同工作正常")
    else:
        print(f"\n❌ 演示过程中出现问题")

if __name__ == "__main__":
    main()
