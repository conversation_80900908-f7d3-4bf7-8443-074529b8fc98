#!/usr/bin/env python3
"""
检查resource.db的实际表结构
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import sqlite3

def check_database_structure():
    """检查数据库表结构"""
    print("🔍 检查resource.db表结构")
    print("=" * 60)
    
    try:
        project_root = os.path.dirname(os.path.dirname(__file__))
        db_path = os.path.join(project_root, 'resource.db')
        
        print(f"📁 数据库路径: {db_path}")
        
        if not os.path.exists(db_path):
            print(f"❌ 数据库文件不存在")
            return
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 获取所有表名
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        
        print(f"📋 数据库中的表:")
        for table in tables:
            table_name = table[0]
            print(f"  - {table_name}")
            
            # 获取表结构
            cursor.execute(f"PRAGMA table_info({table_name})")
            columns = cursor.fetchall()
            
            if table_name in ['valuemapping', 'schemacolumn', 'schematable', 'dbconnection']:
                print(f"    结构:")
                for col in columns:
                    col_id, col_name, col_type, not_null, default_val, pk = col
                    print(f"      {col_name} ({col_type})")
        
        # 检查是否有值映射数据
        if 'valuemapping' in [t[0] for t in tables]:
            cursor.execute("SELECT COUNT(*) FROM valuemapping")
            count = cursor.fetchone()[0]
            print(f"\n📊 valuemapping表记录数: {count}")
            
            if count > 0:
                # 显示前几条记录
                cursor.execute("SELECT * FROM valuemapping LIMIT 5")
                records = cursor.fetchall()
                print(f"前5条记录:")
                for record in records:
                    print(f"  {record}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 检查数据库结构失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    print("🚀 检查数据库结构")
    print("=" * 80)
    
    check_database_structure()

if __name__ == "__main__":
    main()
