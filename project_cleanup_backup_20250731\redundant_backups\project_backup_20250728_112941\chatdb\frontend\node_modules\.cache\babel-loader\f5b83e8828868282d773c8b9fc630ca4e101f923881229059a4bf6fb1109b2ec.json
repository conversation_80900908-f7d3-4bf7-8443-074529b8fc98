{"ast": null, "code": "// http://www.w3.org/TR/CSS21/grammar.html\n// https://github.com/visionmedia/css-parse/pull/49#issuecomment-30088027\nvar COMMENT_REGEX = /\\/\\*[^*]*\\*+([^/*][^*]*\\*+)*\\//g;\nvar NEWLINE_REGEX = /\\n/g;\nvar WHITESPACE_REGEX = /^\\s*/;\n\n// declaration\nvar PROPERTY_REGEX = /^(\\*?[-#/*\\\\\\w]+(\\[[0-9a-z_-]+\\])?)\\s*/;\nvar COLON_REGEX = /^:\\s*/;\nvar VALUE_REGEX = /^((?:'(?:\\\\'|.)*?'|\"(?:\\\\\"|.)*?\"|\\([^)]*?\\)|[^};])+)/;\nvar SEMICOLON_REGEX = /^[;\\s]*/;\n\n// https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String/Trim#Polyfill\nvar TRIM_REGEX = /^\\s+|\\s+$/g;\n\n// strings\nvar NEWLINE = '\\n';\nvar FORWARD_SLASH = '/';\nvar ASTERISK = '*';\nvar EMPTY_STRING = '';\n\n// types\nvar TYPE_COMMENT = 'comment';\nvar TYPE_DECLARATION = 'declaration';\n\n/**\n * @param {String} style\n * @param {Object} [options]\n * @return {Object[]}\n * @throws {TypeError}\n * @throws {Error}\n */\nmodule.exports = function (style, options) {\n  if (typeof style !== 'string') {\n    throw new TypeError('First argument must be a string');\n  }\n  if (!style) return [];\n  options = options || {};\n\n  /**\n   * Positional.\n   */\n  var lineno = 1;\n  var column = 1;\n\n  /**\n   * Update lineno and column based on `str`.\n   *\n   * @param {String} str\n   */\n  function updatePosition(str) {\n    var lines = str.match(NEWLINE_REGEX);\n    if (lines) lineno += lines.length;\n    var i = str.lastIndexOf(NEWLINE);\n    column = ~i ? str.length - i : column + str.length;\n  }\n\n  /**\n   * Mark position and patch `node.position`.\n   *\n   * @return {Function}\n   */\n  function position() {\n    var start = {\n      line: lineno,\n      column: column\n    };\n    return function (node) {\n      node.position = new Position(start);\n      whitespace();\n      return node;\n    };\n  }\n\n  /**\n   * Store position information for a node.\n   *\n   * @constructor\n   * @property {Object} start\n   * @property {Object} end\n   * @property {undefined|String} source\n   */\n  function Position(start) {\n    this.start = start;\n    this.end = {\n      line: lineno,\n      column: column\n    };\n    this.source = options.source;\n  }\n\n  /**\n   * Non-enumerable source string.\n   */\n  Position.prototype.content = style;\n  var errorsList = [];\n\n  /**\n   * Error `msg`.\n   *\n   * @param {String} msg\n   * @throws {Error}\n   */\n  function error(msg) {\n    var err = new Error(options.source + ':' + lineno + ':' + column + ': ' + msg);\n    err.reason = msg;\n    err.filename = options.source;\n    err.line = lineno;\n    err.column = column;\n    err.source = style;\n    if (options.silent) {\n      errorsList.push(err);\n    } else {\n      throw err;\n    }\n  }\n\n  /**\n   * Match `re` and return captures.\n   *\n   * @param {RegExp} re\n   * @return {undefined|Array}\n   */\n  function match(re) {\n    var m = re.exec(style);\n    if (!m) return;\n    var str = m[0];\n    updatePosition(str);\n    style = style.slice(str.length);\n    return m;\n  }\n\n  /**\n   * Parse whitespace.\n   */\n  function whitespace() {\n    match(WHITESPACE_REGEX);\n  }\n\n  /**\n   * Parse comments.\n   *\n   * @param {Object[]} [rules]\n   * @return {Object[]}\n   */\n  function comments(rules) {\n    var c;\n    rules = rules || [];\n    while (c = comment()) {\n      if (c !== false) {\n        rules.push(c);\n      }\n    }\n    return rules;\n  }\n\n  /**\n   * Parse comment.\n   *\n   * @return {Object}\n   * @throws {Error}\n   */\n  function comment() {\n    var pos = position();\n    if (FORWARD_SLASH != style.charAt(0) || ASTERISK != style.charAt(1)) return;\n    var i = 2;\n    while (EMPTY_STRING != style.charAt(i) && (ASTERISK != style.charAt(i) || FORWARD_SLASH != style.charAt(i + 1))) {\n      ++i;\n    }\n    i += 2;\n    if (EMPTY_STRING === style.charAt(i - 1)) {\n      return error('End of comment missing');\n    }\n    var str = style.slice(2, i - 2);\n    column += 2;\n    updatePosition(str);\n    style = style.slice(i);\n    column += 2;\n    return pos({\n      type: TYPE_COMMENT,\n      comment: str\n    });\n  }\n\n  /**\n   * Parse declaration.\n   *\n   * @return {Object}\n   * @throws {Error}\n   */\n  function declaration() {\n    var pos = position();\n\n    // prop\n    var prop = match(PROPERTY_REGEX);\n    if (!prop) return;\n    comment();\n\n    // :\n    if (!match(COLON_REGEX)) return error(\"property missing ':'\");\n\n    // val\n    var val = match(VALUE_REGEX);\n    var ret = pos({\n      type: TYPE_DECLARATION,\n      property: trim(prop[0].replace(COMMENT_REGEX, EMPTY_STRING)),\n      value: val ? trim(val[0].replace(COMMENT_REGEX, EMPTY_STRING)) : EMPTY_STRING\n    });\n\n    // ;\n    match(SEMICOLON_REGEX);\n    return ret;\n  }\n\n  /**\n   * Parse declarations.\n   *\n   * @return {Object[]}\n   */\n  function declarations() {\n    var decls = [];\n    comments(decls);\n\n    // declarations\n    var decl;\n    while (decl = declaration()) {\n      if (decl !== false) {\n        decls.push(decl);\n        comments(decls);\n      }\n    }\n    return decls;\n  }\n  whitespace();\n  return declarations();\n};\n\n/**\n * Trim `str`.\n *\n * @param {String} str\n * @return {String}\n */\nfunction trim(str) {\n  return str ? str.replace(TRIM_REGEX, EMPTY_STRING) : EMPTY_STRING;\n}", "map": {"version": 3, "names": ["COMMENT_REGEX", "NEWLINE_REGEX", "WHITESPACE_REGEX", "PROPERTY_REGEX", "COLON_REGEX", "VALUE_REGEX", "SEMICOLON_REGEX", "TRIM_REGEX", "NEWLINE", "FORWARD_SLASH", "ASTERISK", "EMPTY_STRING", "TYPE_COMMENT", "TYPE_DECLARATION", "module", "exports", "style", "options", "TypeError", "lineno", "column", "updatePosition", "str", "lines", "match", "length", "i", "lastIndexOf", "position", "start", "line", "node", "Position", "whitespace", "end", "source", "prototype", "content", "errorsList", "error", "msg", "err", "Error", "reason", "filename", "silent", "push", "re", "m", "exec", "slice", "comments", "rules", "c", "comment", "pos", "char<PERSON>t", "type", "declaration", "prop", "val", "ret", "property", "trim", "replace", "value", "declarations", "decls", "decl"], "sources": ["C:/Users/<USER>/PycharmProjects/智能数据分析系统/chatdb/frontend/node_modules/inline-style-parser/index.js"], "sourcesContent": ["// http://www.w3.org/TR/CSS21/grammar.html\n// https://github.com/visionmedia/css-parse/pull/49#issuecomment-30088027\nvar COMMENT_REGEX = /\\/\\*[^*]*\\*+([^/*][^*]*\\*+)*\\//g;\n\nvar NEWLINE_REGEX = /\\n/g;\nvar WHITESPACE_REGEX = /^\\s*/;\n\n// declaration\nvar PROPERTY_REGEX = /^(\\*?[-#/*\\\\\\w]+(\\[[0-9a-z_-]+\\])?)\\s*/;\nvar COLON_REGEX = /^:\\s*/;\nvar VALUE_REGEX = /^((?:'(?:\\\\'|.)*?'|\"(?:\\\\\"|.)*?\"|\\([^)]*?\\)|[^};])+)/;\nvar SEMICOLON_REGEX = /^[;\\s]*/;\n\n// https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String/Trim#Polyfill\nvar TRIM_REGEX = /^\\s+|\\s+$/g;\n\n// strings\nvar NEWLINE = '\\n';\nvar FORWARD_SLASH = '/';\nvar ASTERISK = '*';\nvar EMPTY_STRING = '';\n\n// types\nvar TYPE_COMMENT = 'comment';\nvar TYPE_DECLARATION = 'declaration';\n\n/**\n * @param {String} style\n * @param {Object} [options]\n * @return {Object[]}\n * @throws {TypeError}\n * @throws {Error}\n */\nmodule.exports = function(style, options) {\n  if (typeof style !== 'string') {\n    throw new TypeError('First argument must be a string');\n  }\n\n  if (!style) return [];\n\n  options = options || {};\n\n  /**\n   * Positional.\n   */\n  var lineno = 1;\n  var column = 1;\n\n  /**\n   * Update lineno and column based on `str`.\n   *\n   * @param {String} str\n   */\n  function updatePosition(str) {\n    var lines = str.match(NEWLINE_REGEX);\n    if (lines) lineno += lines.length;\n    var i = str.lastIndexOf(NEWLINE);\n    column = ~i ? str.length - i : column + str.length;\n  }\n\n  /**\n   * Mark position and patch `node.position`.\n   *\n   * @return {Function}\n   */\n  function position() {\n    var start = { line: lineno, column: column };\n    return function(node) {\n      node.position = new Position(start);\n      whitespace();\n      return node;\n    };\n  }\n\n  /**\n   * Store position information for a node.\n   *\n   * @constructor\n   * @property {Object} start\n   * @property {Object} end\n   * @property {undefined|String} source\n   */\n  function Position(start) {\n    this.start = start;\n    this.end = { line: lineno, column: column };\n    this.source = options.source;\n  }\n\n  /**\n   * Non-enumerable source string.\n   */\n  Position.prototype.content = style;\n\n  var errorsList = [];\n\n  /**\n   * Error `msg`.\n   *\n   * @param {String} msg\n   * @throws {Error}\n   */\n  function error(msg) {\n    var err = new Error(\n      options.source + ':' + lineno + ':' + column + ': ' + msg\n    );\n    err.reason = msg;\n    err.filename = options.source;\n    err.line = lineno;\n    err.column = column;\n    err.source = style;\n\n    if (options.silent) {\n      errorsList.push(err);\n    } else {\n      throw err;\n    }\n  }\n\n  /**\n   * Match `re` and return captures.\n   *\n   * @param {RegExp} re\n   * @return {undefined|Array}\n   */\n  function match(re) {\n    var m = re.exec(style);\n    if (!m) return;\n    var str = m[0];\n    updatePosition(str);\n    style = style.slice(str.length);\n    return m;\n  }\n\n  /**\n   * Parse whitespace.\n   */\n  function whitespace() {\n    match(WHITESPACE_REGEX);\n  }\n\n  /**\n   * Parse comments.\n   *\n   * @param {Object[]} [rules]\n   * @return {Object[]}\n   */\n  function comments(rules) {\n    var c;\n    rules = rules || [];\n    while ((c = comment())) {\n      if (c !== false) {\n        rules.push(c);\n      }\n    }\n    return rules;\n  }\n\n  /**\n   * Parse comment.\n   *\n   * @return {Object}\n   * @throws {Error}\n   */\n  function comment() {\n    var pos = position();\n    if (FORWARD_SLASH != style.charAt(0) || ASTERISK != style.charAt(1)) return;\n\n    var i = 2;\n    while (\n      EMPTY_STRING != style.charAt(i) &&\n      (ASTERISK != style.charAt(i) || FORWARD_SLASH != style.charAt(i + 1))\n    ) {\n      ++i;\n    }\n    i += 2;\n\n    if (EMPTY_STRING === style.charAt(i - 1)) {\n      return error('End of comment missing');\n    }\n\n    var str = style.slice(2, i - 2);\n    column += 2;\n    updatePosition(str);\n    style = style.slice(i);\n    column += 2;\n\n    return pos({\n      type: TYPE_COMMENT,\n      comment: str\n    });\n  }\n\n  /**\n   * Parse declaration.\n   *\n   * @return {Object}\n   * @throws {Error}\n   */\n  function declaration() {\n    var pos = position();\n\n    // prop\n    var prop = match(PROPERTY_REGEX);\n    if (!prop) return;\n    comment();\n\n    // :\n    if (!match(COLON_REGEX)) return error(\"property missing ':'\");\n\n    // val\n    var val = match(VALUE_REGEX);\n\n    var ret = pos({\n      type: TYPE_DECLARATION,\n      property: trim(prop[0].replace(COMMENT_REGEX, EMPTY_STRING)),\n      value: val\n        ? trim(val[0].replace(COMMENT_REGEX, EMPTY_STRING))\n        : EMPTY_STRING\n    });\n\n    // ;\n    match(SEMICOLON_REGEX);\n\n    return ret;\n  }\n\n  /**\n   * Parse declarations.\n   *\n   * @return {Object[]}\n   */\n  function declarations() {\n    var decls = [];\n\n    comments(decls);\n\n    // declarations\n    var decl;\n    while ((decl = declaration())) {\n      if (decl !== false) {\n        decls.push(decl);\n        comments(decls);\n      }\n    }\n\n    return decls;\n  }\n\n  whitespace();\n  return declarations();\n};\n\n/**\n * Trim `str`.\n *\n * @param {String} str\n * @return {String}\n */\nfunction trim(str) {\n  return str ? str.replace(TRIM_REGEX, EMPTY_STRING) : EMPTY_STRING;\n}\n"], "mappings": "AAAA;AACA;AACA,IAAIA,aAAa,GAAG,iCAAiC;AAErD,IAAIC,aAAa,GAAG,KAAK;AACzB,IAAIC,gBAAgB,GAAG,MAAM;;AAE7B;AACA,IAAIC,cAAc,GAAG,wCAAwC;AAC7D,IAAIC,WAAW,GAAG,OAAO;AACzB,IAAIC,WAAW,GAAG,sDAAsD;AACxE,IAAIC,eAAe,GAAG,SAAS;;AAE/B;AACA,IAAIC,UAAU,GAAG,YAAY;;AAE7B;AACA,IAAIC,OAAO,GAAG,IAAI;AAClB,IAAIC,aAAa,GAAG,GAAG;AACvB,IAAIC,QAAQ,GAAG,GAAG;AAClB,IAAIC,YAAY,GAAG,EAAE;;AAErB;AACA,IAAIC,YAAY,GAAG,SAAS;AAC5B,IAAIC,gBAAgB,GAAG,aAAa;;AAEpC;AACA;AACA;AACA;AACA;AACA;AACA;AACAC,MAAM,CAACC,OAAO,GAAG,UAASC,KAAK,EAAEC,OAAO,EAAE;EACxC,IAAI,OAAOD,KAAK,KAAK,QAAQ,EAAE;IAC7B,MAAM,IAAIE,SAAS,CAAC,iCAAiC,CAAC;EACxD;EAEA,IAAI,CAACF,KAAK,EAAE,OAAO,EAAE;EAErBC,OAAO,GAAGA,OAAO,IAAI,CAAC,CAAC;;EAEvB;AACF;AACA;EACE,IAAIE,MAAM,GAAG,CAAC;EACd,IAAIC,MAAM,GAAG,CAAC;;EAEd;AACF;AACA;AACA;AACA;EACE,SAASC,cAAcA,CAACC,GAAG,EAAE;IAC3B,IAAIC,KAAK,GAAGD,GAAG,CAACE,KAAK,CAACvB,aAAa,CAAC;IACpC,IAAIsB,KAAK,EAAEJ,MAAM,IAAII,KAAK,CAACE,MAAM;IACjC,IAAIC,CAAC,GAAGJ,GAAG,CAACK,WAAW,CAACnB,OAAO,CAAC;IAChCY,MAAM,GAAG,CAACM,CAAC,GAAGJ,GAAG,CAACG,MAAM,GAAGC,CAAC,GAAGN,MAAM,GAAGE,GAAG,CAACG,MAAM;EACpD;;EAEA;AACF;AACA;AACA;AACA;EACE,SAASG,QAAQA,CAAA,EAAG;IAClB,IAAIC,KAAK,GAAG;MAAEC,IAAI,EAAEX,MAAM;MAAEC,MAAM,EAAEA;IAAO,CAAC;IAC5C,OAAO,UAASW,IAAI,EAAE;MACpBA,IAAI,CAACH,QAAQ,GAAG,IAAII,QAAQ,CAACH,KAAK,CAAC;MACnCI,UAAU,CAAC,CAAC;MACZ,OAAOF,IAAI;IACb,CAAC;EACH;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASC,QAAQA,CAACH,KAAK,EAAE;IACvB,IAAI,CAACA,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACK,GAAG,GAAG;MAAEJ,IAAI,EAAEX,MAAM;MAAEC,MAAM,EAAEA;IAAO,CAAC;IAC3C,IAAI,CAACe,MAAM,GAAGlB,OAAO,CAACkB,MAAM;EAC9B;;EAEA;AACF;AACA;EACEH,QAAQ,CAACI,SAAS,CAACC,OAAO,GAAGrB,KAAK;EAElC,IAAIsB,UAAU,GAAG,EAAE;;EAEnB;AACF;AACA;AACA;AACA;AACA;EACE,SAASC,KAAKA,CAACC,GAAG,EAAE;IAClB,IAAIC,GAAG,GAAG,IAAIC,KAAK,CACjBzB,OAAO,CAACkB,MAAM,GAAG,GAAG,GAAGhB,MAAM,GAAG,GAAG,GAAGC,MAAM,GAAG,IAAI,GAAGoB,GACxD,CAAC;IACDC,GAAG,CAACE,MAAM,GAAGH,GAAG;IAChBC,GAAG,CAACG,QAAQ,GAAG3B,OAAO,CAACkB,MAAM;IAC7BM,GAAG,CAACX,IAAI,GAAGX,MAAM;IACjBsB,GAAG,CAACrB,MAAM,GAAGA,MAAM;IACnBqB,GAAG,CAACN,MAAM,GAAGnB,KAAK;IAElB,IAAIC,OAAO,CAAC4B,MAAM,EAAE;MAClBP,UAAU,CAACQ,IAAI,CAACL,GAAG,CAAC;IACtB,CAAC,MAAM;MACL,MAAMA,GAAG;IACX;EACF;;EAEA;AACF;AACA;AACA;AACA;AACA;EACE,SAASjB,KAAKA,CAACuB,EAAE,EAAE;IACjB,IAAIC,CAAC,GAAGD,EAAE,CAACE,IAAI,CAACjC,KAAK,CAAC;IACtB,IAAI,CAACgC,CAAC,EAAE;IACR,IAAI1B,GAAG,GAAG0B,CAAC,CAAC,CAAC,CAAC;IACd3B,cAAc,CAACC,GAAG,CAAC;IACnBN,KAAK,GAAGA,KAAK,CAACkC,KAAK,CAAC5B,GAAG,CAACG,MAAM,CAAC;IAC/B,OAAOuB,CAAC;EACV;;EAEA;AACF;AACA;EACE,SAASf,UAAUA,CAAA,EAAG;IACpBT,KAAK,CAACtB,gBAAgB,CAAC;EACzB;;EAEA;AACF;AACA;AACA;AACA;AACA;EACE,SAASiD,QAAQA,CAACC,KAAK,EAAE;IACvB,IAAIC,CAAC;IACLD,KAAK,GAAGA,KAAK,IAAI,EAAE;IACnB,OAAQC,CAAC,GAAGC,OAAO,CAAC,CAAC,EAAG;MACtB,IAAID,CAAC,KAAK,KAAK,EAAE;QACfD,KAAK,CAACN,IAAI,CAACO,CAAC,CAAC;MACf;IACF;IACA,OAAOD,KAAK;EACd;;EAEA;AACF;AACA;AACA;AACA;AACA;EACE,SAASE,OAAOA,CAAA,EAAG;IACjB,IAAIC,GAAG,GAAG3B,QAAQ,CAAC,CAAC;IACpB,IAAInB,aAAa,IAAIO,KAAK,CAACwC,MAAM,CAAC,CAAC,CAAC,IAAI9C,QAAQ,IAAIM,KAAK,CAACwC,MAAM,CAAC,CAAC,CAAC,EAAE;IAErE,IAAI9B,CAAC,GAAG,CAAC;IACT,OACEf,YAAY,IAAIK,KAAK,CAACwC,MAAM,CAAC9B,CAAC,CAAC,KAC9BhB,QAAQ,IAAIM,KAAK,CAACwC,MAAM,CAAC9B,CAAC,CAAC,IAAIjB,aAAa,IAAIO,KAAK,CAACwC,MAAM,CAAC9B,CAAC,GAAG,CAAC,CAAC,CAAC,EACrE;MACA,EAAEA,CAAC;IACL;IACAA,CAAC,IAAI,CAAC;IAEN,IAAIf,YAAY,KAAKK,KAAK,CAACwC,MAAM,CAAC9B,CAAC,GAAG,CAAC,CAAC,EAAE;MACxC,OAAOa,KAAK,CAAC,wBAAwB,CAAC;IACxC;IAEA,IAAIjB,GAAG,GAAGN,KAAK,CAACkC,KAAK,CAAC,CAAC,EAAExB,CAAC,GAAG,CAAC,CAAC;IAC/BN,MAAM,IAAI,CAAC;IACXC,cAAc,CAACC,GAAG,CAAC;IACnBN,KAAK,GAAGA,KAAK,CAACkC,KAAK,CAACxB,CAAC,CAAC;IACtBN,MAAM,IAAI,CAAC;IAEX,OAAOmC,GAAG,CAAC;MACTE,IAAI,EAAE7C,YAAY;MAClB0C,OAAO,EAAEhC;IACX,CAAC,CAAC;EACJ;;EAEA;AACF;AACA;AACA;AACA;AACA;EACE,SAASoC,WAAWA,CAAA,EAAG;IACrB,IAAIH,GAAG,GAAG3B,QAAQ,CAAC,CAAC;;IAEpB;IACA,IAAI+B,IAAI,GAAGnC,KAAK,CAACrB,cAAc,CAAC;IAChC,IAAI,CAACwD,IAAI,EAAE;IACXL,OAAO,CAAC,CAAC;;IAET;IACA,IAAI,CAAC9B,KAAK,CAACpB,WAAW,CAAC,EAAE,OAAOmC,KAAK,CAAC,sBAAsB,CAAC;;IAE7D;IACA,IAAIqB,GAAG,GAAGpC,KAAK,CAACnB,WAAW,CAAC;IAE5B,IAAIwD,GAAG,GAAGN,GAAG,CAAC;MACZE,IAAI,EAAE5C,gBAAgB;MACtBiD,QAAQ,EAAEC,IAAI,CAACJ,IAAI,CAAC,CAAC,CAAC,CAACK,OAAO,CAAChE,aAAa,EAAEW,YAAY,CAAC,CAAC;MAC5DsD,KAAK,EAAEL,GAAG,GACNG,IAAI,CAACH,GAAG,CAAC,CAAC,CAAC,CAACI,OAAO,CAAChE,aAAa,EAAEW,YAAY,CAAC,CAAC,GACjDA;IACN,CAAC,CAAC;;IAEF;IACAa,KAAK,CAAClB,eAAe,CAAC;IAEtB,OAAOuD,GAAG;EACZ;;EAEA;AACF;AACA;AACA;AACA;EACE,SAASK,YAAYA,CAAA,EAAG;IACtB,IAAIC,KAAK,GAAG,EAAE;IAEdhB,QAAQ,CAACgB,KAAK,CAAC;;IAEf;IACA,IAAIC,IAAI;IACR,OAAQA,IAAI,GAAGV,WAAW,CAAC,CAAC,EAAG;MAC7B,IAAIU,IAAI,KAAK,KAAK,EAAE;QAClBD,KAAK,CAACrB,IAAI,CAACsB,IAAI,CAAC;QAChBjB,QAAQ,CAACgB,KAAK,CAAC;MACjB;IACF;IAEA,OAAOA,KAAK;EACd;EAEAlC,UAAU,CAAC,CAAC;EACZ,OAAOiC,YAAY,CAAC,CAAC;AACvB,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,SAASH,IAAIA,CAACzC,GAAG,EAAE;EACjB,OAAOA,GAAG,GAAGA,GAAG,CAAC0C,OAAO,CAACzD,UAAU,EAAEI,YAAY,CAAC,GAAGA,YAAY;AACnE", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}