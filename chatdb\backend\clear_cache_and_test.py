#!/usr/bin/env python3
"""
清除缓存并测试字段映射修复效果
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import sqlite3
import time

def clear_cache_files():
    """清除可能的缓存文件"""
    print("🧹 清除缓存文件...")
    
    cache_patterns = [
        "*.pyc",
        "__pycache__",
        ".cache",
        "*.cache"
    ]
    
    # 清除Python字节码缓存
    import subprocess
    try:
        result = subprocess.run(
            ["find", ".", "-name", "*.pyc", "-delete"], 
            capture_output=True, 
            text=True,
            cwd=os.path.dirname(__file__)
        )
        print("✅ 已清除Python字节码缓存")
    except:
        print("⚠️ 无法自动清除缓存文件，请手动重启服务")

def test_database_mappings():
    """测试数据库映射"""
    print("\n🔍 测试数据库映射")
    print("=" * 60)
    
    try:
        # 连接到resource.db
        project_root = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
        db_path = os.path.join(project_root, 'resource.db')
        
        print(f"📁 数据库路径: {db_path}")
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 测试关键映射
        test_cases = [
            ('公司', 'accounting_unit_name'),
            ('企业', 'accounting_unit_name'),
            ('科目名称', 'account_full_name'),
            ('account_name', 'account_full_name')
        ]
        
        print("📋 数据库映射测试:")
        all_correct = True
        
        for nl_term, expected_field in test_cases:
            cursor.execute('''
                SELECT sc.column_name, vm.db_value
                FROM valuemapping vm
                JOIN schemacolumn sc ON vm.column_id = sc.id
                JOIN schematable st ON sc.table_id = st.id
                JOIN dbconnection dc ON st.connection_id = dc.id
                WHERE vm.nl_term = ? AND st.table_name = 'financial_data' AND dc.name = 'fin_data'
            ''', (nl_term,))
            
            result = cursor.fetchone()
            if result:
                actual_field, db_value = result
                if actual_field == expected_field:
                    print(f"  ✅ '{nl_term}' → {actual_field} (值: {db_value})")
                else:
                    print(f"  ❌ '{nl_term}' → {actual_field} (期望: {expected_field})")
                    all_correct = False
            else:
                print(f"  ❌ '{nl_term}' → 未找到映射")
                all_correct = False
        
        conn.close()
        return all_correct
        
    except Exception as e:
        print(f"❌ 数据库映射测试失败: {e}")
        return False

def test_code_logic():
    """测试代码逻辑"""
    print("\n🧪 测试代码逻辑")
    print("=" * 60)
    
    try:
        from app.services.text2sql_utils import (
            select_preferred_field,
            apply_field_priority_corrections,
            process_sql_with_value_mappings,
            SEMANTIC_FIELD_GROUPS
        )
        
        print("✅ 成功导入修改的模块")
        
        # 测试字段选择逻辑
        print("\n📋 字段选择逻辑测试:")
        test_cases = [
            ("公司", ["accounting_unit_name", "accounting_organization"], "accounting_unit_name"),
            ("科目名称", ["account_full_name", "account_name"], "account_full_name"),
            ("account_name", ["account_full_name", "account_name"], "account_full_name")
        ]
        
        for term, fields, expected in test_cases:
            result = select_preferred_field(term, fields)
            status = "✅" if result == expected else "❌"
            print(f"  '{term}' → {result} (期望: {expected}) {status}")
        
        # 测试SQL修正逻辑
        print("\n📋 SQL修正逻辑测试:")
        test_sql = "SELECT accounting_organization, account_name FROM financial_data WHERE account_name LIKE '%管理费用%'"
        corrected_sql = apply_field_priority_corrections(test_sql)
        
        print(f"原始SQL: {test_sql}")
        print(f"修正SQL: {corrected_sql}")
        
        uses_preferred_company = "accounting_unit_name" in corrected_sql
        uses_preferred_account = "account_full_name" in corrected_sql
        
        print(f"使用优先公司字段: {'✅' if uses_preferred_company else '❌'}")
        print(f"使用优先科目字段: {'✅' if uses_preferred_account else '❌'}")
        
        return uses_preferred_company and uses_preferred_account
        
    except Exception as e:
        print(f"❌ 代码逻辑测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def simulate_full_pipeline():
    """模拟完整的处理流程"""
    print("\n🔄 模拟完整处理流程")
    print("=" * 60)
    
    try:
        from app.services.text2sql_utils import process_sql_with_value_mappings
        
        # 模拟值映射数据（基于数据库中的实际映射）
        mock_value_mappings = {
            "financial_data.accounting_unit_name": {
                "公司": "accounting_unit_name",
                "企业": "accounting_unit_name",
                "company_name": "accounting_unit_name"
            },
            "financial_data.account_full_name": {
                "科目名称": "account_full_name",
                "会计科目名称": "account_full_name",
                "account_name": "account_full_name"
            }
        }
        
        # 测试场景
        test_scenarios = [
            {
                "name": "公司查询场景",
                "sql": "SELECT accounting_organization, debit_amount FROM financial_data WHERE year = 2024",
                "expected_fields": ["accounting_unit_name"]
            },
            {
                "name": "科目查询场景",
                "sql": "SELECT account_name FROM financial_data WHERE account_name LIKE '%管理费用%'",
                "expected_fields": ["account_full_name"]
            },
            {
                "name": "复合查询场景",
                "sql": "SELECT company_name, account_name FROM financial_data WHERE account_name = '销售费用'",
                "expected_fields": ["accounting_unit_name", "account_full_name"]
            }
        ]
        
        all_passed = True
        
        for scenario in test_scenarios:
            print(f"\n🔸 {scenario['name']}:")
            print(f"   原始SQL: {scenario['sql']}")
            
            processed_sql = process_sql_with_value_mappings(scenario['sql'], mock_value_mappings)
            print(f"   处理后SQL: {processed_sql}")
            
            # 检查是否包含期望的字段
            fields_found = []
            for expected_field in scenario['expected_fields']:
                if expected_field in processed_sql:
                    fields_found.append(expected_field)
            
            success = len(fields_found) == len(scenario['expected_fields'])
            status = "✅" if success else "❌"
            
            print(f"   期望字段: {scenario['expected_fields']}")
            print(f"   找到字段: {fields_found}")
            print(f"   测试结果: {status}")
            
            if not success:
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"❌ 完整流程模拟失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def provide_next_steps():
    """提供下一步操作建议"""
    print("\n📝 下一步操作建议")
    print("=" * 60)
    
    print("立即操作:")
    print("1. 🔄 重启后端服务以清除内存缓存")
    print("2. 🧪 在前端测试智能查询功能")
    print("3. 📊 观察生成的SQL是否使用了正确的优先字段")
    
    print("\n测试用例:")
    print("- 输入: '查询各公司2024年的费用情况'")
    print("  期望: 使用 accounting_unit_name 字段")
    print("- 输入: '查询科目名称包含管理费用的记录'")
    print("  期望: 使用 account_full_name 字段")
    
    print("\n如果问题仍然存在:")
    print("1. 检查后端服务是否正确重启")
    print("2. 检查是否有其他缓存机制（Redis等）")
    print("3. 查看后端日志中的字段映射处理信息")

def main():
    """主函数"""
    print("🚀 清除缓存并测试字段映射修复")
    print("=" * 80)
    
    # 执行测试步骤
    steps = [
        ("清除缓存文件", clear_cache_files),
        ("测试数据库映射", test_database_mappings),
        ("测试代码逻辑", test_code_logic),
        ("模拟完整流程", simulate_full_pipeline)
    ]
    
    results = []
    
    for step_name, step_func in steps:
        print(f"\n{'='*20} {step_name} {'='*20}")
        try:
            result = step_func()
            results.append((step_name, result))
        except Exception as e:
            print(f"❌ {step_name} 执行失败: {e}")
            results.append((step_name, False))
    
    # 总结结果
    print("\n" + "=" * 80)
    print("📊 测试结果总结")
    print("=" * 80)
    
    for step_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{step_name}: {status}")
    
    all_passed = all(result for _, result in results)
    
    if all_passed:
        print("\n🎉 所有测试通过！字段映射修复应该已经生效。")
        print("请重启后端服务并测试前端功能。")
    else:
        print("\n⚠️ 部分测试失败，可能需要进一步调试。")
    
    provide_next_steps()

if __name__ == "__main__":
    main()
