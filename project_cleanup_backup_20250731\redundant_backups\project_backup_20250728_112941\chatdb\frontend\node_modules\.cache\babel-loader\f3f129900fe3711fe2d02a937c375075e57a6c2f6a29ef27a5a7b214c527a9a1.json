{"ast": null, "code": "/*\nLanguage: 1C:Enterprise\nAuthor: <PERSON><PERSON> <<EMAIL>>\nDescription: built-in language 1C:Enterprise (v7, v8)\nCategory: enterprise\n*/\n\nfunction _1c(hljs) {\n  // общий паттерн для определения идентификаторов\n  var UNDERSCORE_IDENT_RE = '[A-Za-zА-Яа-яёЁ_][A-Za-zА-Яа-яёЁ_0-9]+';\n\n  // v7 уникальные ключевые слова, отсутствующие в v8 ==> keyword\n  var v7_keywords = 'далее ';\n\n  // v8 ключевые слова ==> keyword\n  var v8_keywords = 'возврат вызватьисключение выполнить для если и из или иначе иначеесли исключение каждого конецесли ' + 'конецпопытки конеццикла не новый перейти перем по пока попытка прервать продолжить тогда цикл экспорт ';\n\n  // keyword : ключевые слова\n  var KEYWORD = v7_keywords + v8_keywords;\n\n  // v7 уникальные директивы, отсутствующие в v8 ==> meta-keyword\n  var v7_meta_keywords = 'загрузитьизфайла ';\n\n  // v8 ключевые слова в инструкциях препроцессора, директивах компиляции, аннотациях ==> meta-keyword\n  var v8_meta_keywords = 'вебклиент вместо внешнеесоединение клиент конецобласти мобильноеприложениеклиент мобильноеприложениесервер ' + 'наклиенте наклиентенасервере наклиентенасерверебезконтекста насервере насерверебезконтекста область перед ' + 'после сервер толстыйклиентобычноеприложение толстыйклиентуправляемоеприложение тонкийклиент ';\n\n  // meta-keyword : ключевые слова в инструкциях препроцессора, директивах компиляции, аннотациях\n  var METAKEYWORD = v7_meta_keywords + v8_meta_keywords;\n\n  // v7 системные константы ==> built_in\n  var v7_system_constants = 'разделительстраниц разделительстрок символтабуляции ';\n\n  // v7 уникальные методы глобального контекста, отсутствующие в v8 ==> built_in\n  var v7_global_context_methods = 'ansitooem oemtoansi ввестивидсубконто ввестиперечисление ввестипериод ввестиплансчетов выбранныйплансчетов ' + 'датагод датамесяц датачисло заголовоксистемы значениевстроку значениеизстроки каталогиб каталогпользователя ' + 'кодсимв конгода конецпериодаби конецрассчитанногопериодаби конецстандартногоинтервала конквартала конмесяца ' + 'коннедели лог лог10 максимальноеколичествосубконто названиеинтерфейса названиенабораправ назначитьвид ' + 'назначитьсчет найтиссылки началопериодаби началостандартногоинтервала начгода начквартала начмесяца ' + 'начнедели номерднягода номерднянедели номернеделигода обработкаожидания основнойжурналрасчетов ' + 'основнойплансчетов основнойязык очиститьокносообщений периодстр получитьвремята получитьдатута ' + 'получитьдокументта получитьзначенияотбора получитьпозициюта получитьпустоезначение получитьта ' + 'префиксавтонумерации пропись пустоезначение разм разобратьпозициюдокумента рассчитатьрегистрына ' + 'рассчитатьрегистрыпо симв создатьобъект статусвозврата стрколичествострок сформироватьпозициюдокумента ' + 'счетпокоду текущеевремя типзначения типзначениястр установитьтана установитьтапо фиксшаблон шаблон ';\n\n  // v8 методы глобального контекста ==> built_in\n  var v8_global_context_methods = 'acos asin atan base64значение base64строка cos exp log log10 pow sin sqrt tan xmlзначение xmlстрока ' + 'xmlтип xmlтипзнч активноеокно безопасныйрежим безопасныйрежимразделенияданных булево ввестидату ввестизначение ' + 'ввестистроку ввестичисло возможностьчтенияxml вопрос восстановитьзначение врег выгрузитьжурналрегистрации ' + 'выполнитьобработкуоповещения выполнитьпроверкуправдоступа вычислить год данныеформывзначение дата день деньгода ' + 'деньнедели добавитьмесяц заблокироватьданныедляредактирования заблокироватьработупользователя завершитьработусистемы ' + 'загрузитьвнешнююкомпоненту закрытьсправку записатьjson записатьxml записатьдатуjson записьжурналарегистрации ' + 'заполнитьзначениясвойств запроситьразрешениепользователя запуститьприложение запуститьсистему зафиксироватьтранзакцию ' + 'значениевданныеформы значениевстрокувнутр значениевфайл значениезаполнено значениеизстрокивнутр значениеизфайла ' + 'изxmlтипа импортмоделиxdto имякомпьютера имяпользователя инициализироватьпредопределенныеданные информацияобошибке ' + 'каталогбиблиотекимобильногоустройства каталогвременныхфайлов каталогдокументов каталогпрограммы кодироватьстроку ' + 'кодлокализацииинформационнойбазы кодсимвола командасистемы конецгода конецдня конецквартала конецмесяца конецминуты ' + 'конецнедели конецчаса конфигурациябазыданныхизмененадинамически конфигурацияизменена копироватьданныеформы ' + 'копироватьфайл краткоепредставлениеошибки лев макс местноевремя месяц мин минута монопольныйрежим найти ' + 'найтинедопустимыесимволыxml найтиокнопонавигационнойссылке найтипомеченныенаудаление найтипоссылкам найтифайлы ' + 'началогода началодня началоквартала началомесяца началоминуты началонедели началочаса начатьзапросразрешенияпользователя ' + 'начатьзапускприложения начатькопированиефайла начатьперемещениефайла начатьподключениевнешнейкомпоненты ' + 'начатьподключениерасширенияработыскриптографией начатьподключениерасширенияработысфайлами начатьпоискфайлов ' + 'начатьполучениекаталогавременныхфайлов начатьполучениекаталогадокументов начатьполучениерабочегокаталогаданныхпользователя ' + 'начатьполучениефайлов начатьпомещениефайла начатьпомещениефайлов начатьсозданиедвоичныхданныхизфайла начатьсозданиекаталога ' + 'начатьтранзакцию начатьудалениефайлов начатьустановкувнешнейкомпоненты начатьустановкурасширенияработыскриптографией ' + 'начатьустановкурасширенияработысфайлами неделягода необходимостьзавершениясоединения номерсеансаинформационнойбазы ' + 'номерсоединенияинформационнойбазы нрег нстр обновитьинтерфейс обновитьнумерациюобъектов обновитьповторноиспользуемыезначения ' + 'обработкапрерыванияпользователя объединитьфайлы окр описаниеошибки оповестить оповеститьобизменении ' + 'отключитьобработчикзапросанастроекклиенталицензирования отключитьобработчикожидания отключитьобработчикоповещения ' + 'открытьзначение открытьиндекссправки открытьсодержаниесправки открытьсправку открытьформу открытьформумодально ' + 'отменитьтранзакцию очиститьжурналрегистрации очиститьнастройкипользователя очиститьсообщения параметрыдоступа ' + 'перейтипонавигационнойссылке переместитьфайл подключитьвнешнююкомпоненту ' + 'подключитьобработчикзапросанастроекклиенталицензирования подключитьобработчикожидания подключитьобработчикоповещения ' + 'подключитьрасширениеработыскриптографией подключитьрасширениеработысфайлами подробноепредставлениеошибки ' + 'показатьвводдаты показатьвводзначения показатьвводстроки показатьвводчисла показатьвопрос показатьзначение ' + 'показатьинформациюобошибке показатьнакарте показатьоповещениепользователя показатьпредупреждение полноеимяпользователя ' + 'получитьcomобъект получитьxmlтип получитьадреспоместоположению получитьблокировкусеансов получитьвремязавершенияспящегосеанса ' + 'получитьвремязасыпанияпассивногосеанса получитьвремяожиданияблокировкиданных получитьданныевыбора ' + 'получитьдополнительныйпараметрклиенталицензирования получитьдопустимыекодылокализации получитьдопустимыечасовыепояса ' + 'получитьзаголовокклиентскогоприложения получитьзаголовоксистемы получитьзначенияотборажурналарегистрации ' + 'получитьидентификаторконфигурации получитьизвременногохранилища получитьимявременногофайла ' + 'получитьимяклиенталицензирования получитьинформациюэкрановклиента получитьиспользованиежурналарегистрации ' + 'получитьиспользованиесобытияжурналарегистрации получитькраткийзаголовокприложения получитьмакетоформления ' + 'получитьмаскувсефайлы получитьмаскувсефайлыклиента получитьмаскувсефайлысервера получитьместоположениепоадресу ' + 'получитьминимальнуюдлинупаролейпользователей получитьнавигационнуюссылку получитьнавигационнуюссылкуинформационнойбазы ' + 'получитьобновлениеконфигурациибазыданных получитьобновлениепредопределенныхданныхинформационнойбазы получитьобщиймакет ' + 'получитьобщуюформу получитьокна получитьоперативнуюотметкувремени получитьотключениебезопасногорежима ' + 'получитьпараметрыфункциональныхопцийинтерфейса получитьполноеимяпредопределенногозначения ' + 'получитьпредставлениянавигационныхссылок получитьпроверкусложностипаролейпользователей получитьразделительпути ' + 'получитьразделительпутиклиента получитьразделительпутисервера получитьсеансыинформационнойбазы ' + 'получитьскоростьклиентскогосоединения получитьсоединенияинформационнойбазы получитьсообщенияпользователю ' + 'получитьсоответствиеобъектаиформы получитьсоставстандартногоинтерфейсаodata получитьструктурухранениябазыданных ' + 'получитьтекущийсеансинформационнойбазы получитьфайл получитьфайлы получитьформу получитьфункциональнуюопцию ' + 'получитьфункциональнуюопциюинтерфейса получитьчасовойпоясинформационнойбазы пользователиос поместитьвовременноехранилище ' + 'поместитьфайл поместитьфайлы прав праводоступа предопределенноезначение представлениекодалокализации представлениепериода ' + 'представлениеправа представлениеприложения представлениесобытияжурналарегистрации представлениечасовогопояса предупреждение ' + 'прекратитьработусистемы привилегированныйрежим продолжитьвызов прочитатьjson прочитатьxml прочитатьдатуjson пустаястрока ' + 'рабочийкаталогданныхпользователя разблокироватьданныедляредактирования разделитьфайл разорватьсоединениесвнешнимисточникомданных ' + 'раскодироватьстроку рольдоступна секунда сигнал символ скопироватьжурналрегистрации смещениелетнеговремени ' + 'смещениестандартноговремени соединитьбуферыдвоичныхданных создатькаталог создатьфабрикуxdto сокрл сокрлп сокрп сообщить ' + 'состояние сохранитьзначение сохранитьнастройкипользователя сред стрдлина стрзаканчиваетсяна стрзаменить стрнайти стрначинаетсяс ' + 'строка строкасоединенияинформационнойбазы стрполучитьстроку стрразделить стрсоединить стрсравнить стрчисловхождений ' + 'стрчислострок стршаблон текущаядата текущаядатасеанса текущаяуниверсальнаядата текущаяуниверсальнаядатавмиллисекундах ' + 'текущийвариантинтерфейсаклиентскогоприложения текущийвариантосновногошрифтаклиентскогоприложения текущийкодлокализации ' + 'текущийрежимзапуска текущийязык текущийязыксистемы тип типзнч транзакцияактивна трег удалитьданныеинформационнойбазы ' + 'удалитьизвременногохранилища удалитьобъекты удалитьфайлы универсальноевремя установитьбезопасныйрежим ' + 'установитьбезопасныйрежимразделенияданных установитьблокировкусеансов установитьвнешнююкомпоненту ' + 'установитьвремязавершенияспящегосеанса установитьвремязасыпанияпассивногосеанса установитьвремяожиданияблокировкиданных ' + 'установитьзаголовокклиентскогоприложения установитьзаголовоксистемы установитьиспользованиежурналарегистрации ' + 'установитьиспользованиесобытияжурналарегистрации установитькраткийзаголовокприложения ' + 'установитьминимальнуюдлинупаролейпользователей установитьмонопольныйрежим установитьнастройкиклиенталицензирования ' + 'установитьобновлениепредопределенныхданныхинформационнойбазы установитьотключениебезопасногорежима ' + 'установитьпараметрыфункциональныхопцийинтерфейса установитьпривилегированныйрежим ' + 'установитьпроверкусложностипаролейпользователей установитьрасширениеработыскриптографией ' + 'установитьрасширениеработысфайлами установитьсоединениесвнешнимисточникомданных установитьсоответствиеобъектаиформы ' + 'установитьсоставстандартногоинтерфейсаodata установитьчасовойпоясинформационнойбазы установитьчасовойпояссеанса ' + 'формат цел час часовойпояс часовойпояссеанса число числопрописью этоадресвременногохранилища ';\n\n  // v8 свойства глобального контекста ==> built_in\n  var v8_global_context_property = 'wsссылки библиотекакартинок библиотекамакетовоформлениякомпоновкиданных библиотекастилей бизнеспроцессы ' + 'внешниеисточникиданных внешниеобработки внешниеотчеты встроенныепокупки главныйинтерфейс главныйстиль ' + 'документы доставляемыеуведомления журналыдокументов задачи информацияобинтернетсоединении использованиерабочейдаты ' + 'историяработыпользователя константы критерииотбора метаданные обработки отображениерекламы отправкадоставляемыхуведомлений ' + 'отчеты панельзадачос параметрзапуска параметрысеанса перечисления планывидоврасчета планывидовхарактеристик ' + 'планыобмена планысчетов полнотекстовыйпоиск пользователиинформационнойбазы последовательности проверкавстроенныхпокупок ' + 'рабочаядата расширенияконфигурации регистрыбухгалтерии регистрынакопления регистрырасчета регистрысведений ' + 'регламентныезадания сериализаторxdto справочники средствагеопозиционирования средствакриптографии средствамультимедиа ' + 'средстваотображениярекламы средствапочты средствателефонии фабрикаxdto файловыепотоки фоновыезадания хранилищанастроек ' + 'хранилищевариантовотчетов хранилищенастроекданныхформ хранилищеобщихнастроек хранилищепользовательскихнастроекдинамическихсписков ' + 'хранилищепользовательскихнастроекотчетов хранилищесистемныхнастроек ';\n\n  // built_in : встроенные или библиотечные объекты (константы, классы, функции)\n  var BUILTIN = v7_system_constants + v7_global_context_methods + v8_global_context_methods + v8_global_context_property;\n\n  // v8 системные наборы значений ==> class\n  var v8_system_sets_of_values = 'webцвета windowsцвета windowsшрифты библиотекакартинок рамкистиля символы цветастиля шрифтыстиля ';\n\n  // v8 системные перечисления - интерфейсные ==> class\n  var v8_system_enums_interface = 'автоматическоесохранениеданныхформывнастройках автонумерациявформе автораздвижениесерий ' + 'анимациядиаграммы вариантвыравниванияэлементовизаголовков вариантуправлениявысотойтаблицы ' + 'вертикальнаяпрокруткаформы вертикальноеположение вертикальноеположениеэлемента видгруппыформы ' + 'виддекорацииформы виддополненияэлементаформы видизмененияданных видкнопкиформы видпереключателя ' + 'видподписейкдиаграмме видполяформы видфлажка влияниеразмеранапузырекдиаграммы горизонтальноеположение ' + 'горизонтальноеположениеэлемента группировкаколонок группировкаподчиненныхэлементовформы ' + 'группыиэлементы действиеперетаскивания дополнительныйрежимотображения допустимыедействияперетаскивания ' + 'интервалмеждуэлементамиформы использованиевывода использованиеполосыпрокрутки ' + 'используемоезначениеточкибиржевойдиаграммы историявыборапривводе источникзначенийоситочекдиаграммы ' + 'источникзначенияразмерапузырькадиаграммы категориягруппыкоманд максимумсерий начальноеотображениедерева ' + 'начальноеотображениесписка обновлениетекстаредактирования ориентациядендрограммы ориентациядиаграммы ' + 'ориентацияметокдиаграммы ориентацияметоксводнойдиаграммы ориентацияэлементаформы отображениевдиаграмме ' + 'отображениевлегендедиаграммы отображениегруппыкнопок отображениезаголовкашкалыдиаграммы ' + 'отображениезначенийсводнойдиаграммы отображениезначенияизмерительнойдиаграммы ' + 'отображениеинтерваладиаграммыганта отображениекнопки отображениекнопкивыбора отображениеобсужденийформы ' + 'отображениеобычнойгруппы отображениеотрицательныхзначенийпузырьковойдиаграммы отображениепанелипоиска ' + 'отображениеподсказки отображениепредупрежденияприредактировании отображениеразметкиполосырегулирования ' + 'отображениестраницформы отображениетаблицы отображениетекстазначениядиаграммыганта ' + 'отображениеуправленияобычнойгруппы отображениефигурыкнопки палитрацветовдиаграммы поведениеобычнойгруппы ' + 'поддержкамасштабадендрограммы поддержкамасштабадиаграммыганта поддержкамасштабасводнойдиаграммы ' + 'поисквтаблицепривводе положениезаголовкаэлементаформы положениекартинкикнопкиформы ' + 'положениекартинкиэлементаграфическойсхемы положениекоманднойпанелиформы положениекоманднойпанелиэлементаформы ' + 'положениеопорнойточкиотрисовки положениеподписейкдиаграмме положениеподписейшкалызначенийизмерительнойдиаграммы ' + 'положениесостоянияпросмотра положениестрокипоиска положениетекстасоединительнойлинии положениеуправленияпоиском ' + 'положениешкалывремени порядокотображенияточекгоризонтальнойгистограммы порядоксерийвлегендедиаграммы ' + 'размеркартинки расположениезаголовкашкалыдиаграммы растягиваниеповертикалидиаграммыганта ' + 'режимавтоотображениясостояния режимвводастроктаблицы режимвыборанезаполненного режимвыделениядаты ' + 'режимвыделениястрокитаблицы режимвыделениятаблицы режимизмененияразмера режимизменениясвязанногозначения ' + 'режимиспользованиядиалогапечати режимиспользованияпараметракоманды режиммасштабированияпросмотра ' + 'режимосновногоокнаклиентскогоприложения режимоткрытияокнаформы режимотображениявыделения ' + 'режимотображениягеографическойсхемы режимотображениязначенийсерии режимотрисовкисеткиграфическойсхемы ' + 'режимполупрозрачностидиаграммы режимпробеловдиаграммы режимразмещениянастранице режимредактированияколонки ' + 'режимсглаживаниядиаграммы режимсглаживанияиндикатора режимсписказадач сквозноевыравнивание ' + 'сохранениеданныхформывнастройках способзаполнениятекстазаголовкашкалыдиаграммы ' + 'способопределенияограничивающегозначениядиаграммы стандартнаягруппакоманд стандартноеоформление ' + 'статусоповещенияпользователя стильстрелки типаппроксимациилиниитрендадиаграммы типдиаграммы ' + 'типединицышкалывремени типимпортасерийслоягеографическойсхемы типлиниигеографическойсхемы типлиниидиаграммы ' + 'типмаркерагеографическойсхемы типмаркерадиаграммы типобластиоформления ' + 'типорганизацииисточникаданныхгеографическойсхемы типотображениясериислоягеографическойсхемы ' + 'типотображенияточечногообъектагеографическойсхемы типотображенияшкалыэлементалегендыгеографическойсхемы ' + 'типпоискаобъектовгеографическойсхемы типпроекциигеографическойсхемы типразмещенияизмерений ' + 'типразмещенияреквизитовизмерений типрамкиэлементауправления типсводнойдиаграммы ' + 'типсвязидиаграммыганта типсоединениязначенийпосериямдиаграммы типсоединенияточекдиаграммы ' + 'типсоединительнойлинии типстороныэлементаграфическойсхемы типформыотчета типшкалырадарнойдиаграммы ' + 'факторлиниитрендадиаграммы фигуракнопки фигурыграфическойсхемы фиксациявтаблице форматдняшкалывремени ' + 'форматкартинки ширинаподчиненныхэлементовформы ';\n\n  // v8 системные перечисления - свойства прикладных объектов ==> class\n  var v8_system_enums_objects_properties = 'виддвижениябухгалтерии виддвижениянакопления видпериодарегистрарасчета видсчета видточкимаршрутабизнеспроцесса ' + 'использованиеагрегатарегистранакопления использованиегруппиэлементов использованиережимапроведения ' + 'использованиесреза периодичностьагрегатарегистранакопления режимавтовремя режимзаписидокумента режимпроведениядокумента ';\n\n  // v8 системные перечисления - планы обмена ==> class\n  var v8_system_enums_exchange_plans = 'авторегистрацияизменений допустимыйномерсообщения отправкаэлементаданных получениеэлементаданных ';\n\n  // v8 системные перечисления - табличный документ ==> class\n  var v8_system_enums_tabular_document = 'использованиерасшифровкитабличногодокумента ориентациястраницы положениеитоговколоноксводнойтаблицы ' + 'положениеитоговстроксводнойтаблицы положениетекстаотносительнокартинки расположениезаголовкагруппировкитабличногодокумента ' + 'способчтениязначенийтабличногодокумента типдвустороннейпечати типзаполненияобластитабличногодокумента ' + 'типкурсоровтабличногодокумента типлиниирисункатабличногодокумента типлинииячейкитабличногодокумента ' + 'типнаправленияпереходатабличногодокумента типотображениявыделениятабличногодокумента типотображениялинийсводнойтаблицы ' + 'типразмещениятекстатабличногодокумента типрисункатабличногодокумента типсмещениятабличногодокумента ' + 'типузоратабличногодокумента типфайлатабличногодокумента точностьпечати чередованиерасположениястраниц ';\n\n  // v8 системные перечисления - планировщик ==> class\n  var v8_system_enums_sheduler = 'отображениевремениэлементовпланировщика ';\n\n  // v8 системные перечисления - форматированный документ ==> class\n  var v8_system_enums_formatted_document = 'типфайлаформатированногодокумента ';\n\n  // v8 системные перечисления - запрос ==> class\n  var v8_system_enums_query = 'обходрезультатазапроса типзаписизапроса ';\n\n  // v8 системные перечисления - построитель отчета ==> class\n  var v8_system_enums_report_builder = 'видзаполнениярасшифровкипостроителяотчета типдобавленияпредставлений типизмеренияпостроителяотчета типразмещенияитогов ';\n\n  // v8 системные перечисления - работа с файлами ==> class\n  var v8_system_enums_files = 'доступкфайлу режимдиалогавыборафайла режимоткрытияфайла ';\n\n  // v8 системные перечисления - построитель запроса ==> class\n  var v8_system_enums_query_builder = 'типизмеренияпостроителязапроса ';\n\n  // v8 системные перечисления - анализ данных ==> class\n  var v8_system_enums_data_analysis = 'видданныханализа методкластеризации типединицыинтервалавременианализаданных типзаполнениятаблицырезультатаанализаданных ' + 'типиспользованиячисловыхзначенийанализаданных типисточникаданныхпоискаассоциаций типколонкианализаданныхдереворешений ' + 'типколонкианализаданныхкластеризация типколонкианализаданныхобщаястатистика типколонкианализаданныхпоискассоциаций ' + 'типколонкианализаданныхпоискпоследовательностей типколонкимоделипрогноза типмерырасстоянияанализаданных ' + 'типотсеченияправилассоциации типполяанализаданных типстандартизациианализаданных типупорядочиванияправилассоциациианализаданных ' + 'типупорядочиванияшаблоновпоследовательностейанализаданных типупрощениядереварешений ';\n\n  // v8 системные перечисления - xml, json, xs, dom, xdto, web-сервисы ==> class\n  var v8_system_enums_xml_json_xs_dom_xdto_ws = 'wsнаправлениепараметра вариантxpathxs вариантзаписидатыjson вариантпростоготипаxs видгруппымоделиxs видфасетаxdto ' + 'действиепостроителяdom завершенностьпростоготипаxs завершенностьсоставноготипаxs завершенностьсхемыxs запрещенныеподстановкиxs ' + 'исключениягруппподстановкиxs категорияиспользованияатрибутаxs категорияограниченияидентичностиxs категорияограниченияпространствименxs ' + 'методнаследованияxs модельсодержимогоxs назначениетипаxml недопустимыеподстановкиxs обработкапробельныхсимволовxs обработкасодержимогоxs ' + 'ограничениезначенияxs параметрыотбораузловdom переносстрокjson позициявдокументеdom пробельныесимволыxml типатрибутаxml типзначенияjson ' + 'типканоническогоxml типкомпонентыxs типпроверкиxml типрезультатаdomxpath типузлаdom типузлаxml формаxml формапредставленияxs ' + 'форматдатыjson экранированиесимволовjson ';\n\n  // v8 системные перечисления - система компоновки данных ==> class\n  var v8_system_enums_data_composition_system = 'видсравнениякомпоновкиданных действиеобработкирасшифровкикомпоновкиданных направлениесортировкикомпоновкиданных ' + 'расположениевложенныхэлементоврезультатакомпоновкиданных расположениеитоговкомпоновкиданных расположениегруппировкикомпоновкиданных ' + 'расположениеполейгруппировкикомпоновкиданных расположениеполякомпоновкиданных расположениереквизитовкомпоновкиданных ' + 'расположениересурсовкомпоновкиданных типбухгалтерскогоостаткакомпоновкиданных типвыводатекстакомпоновкиданных ' + 'типгруппировкикомпоновкиданных типгруппыэлементовотборакомпоновкиданных типдополненияпериодакомпоновкиданных ' + 'типзаголовкаполейкомпоновкиданных типмакетагруппировкикомпоновкиданных типмакетаобластикомпоновкиданных типостаткакомпоновкиданных ' + 'типпериодакомпоновкиданных типразмещениятекстакомпоновкиданных типсвязинаборовданныхкомпоновкиданных типэлементарезультатакомпоновкиданных ' + 'расположениелегендыдиаграммыкомпоновкиданных типпримененияотборакомпоновкиданных режимотображенияэлементанастройкикомпоновкиданных ' + 'режимотображениянастроеккомпоновкиданных состояниеэлементанастройкикомпоновкиданных способвосстановлениянастроеккомпоновкиданных ' + 'режимкомпоновкирезультата использованиепараметракомпоновкиданных автопозицияресурсовкомпоновкиданных ' + 'вариантиспользованиягруппировкикомпоновкиданных расположениересурсоввдиаграммекомпоновкиданных фиксациякомпоновкиданных ' + 'использованиеусловногооформлениякомпоновкиданных ';\n\n  // v8 системные перечисления - почта ==> class\n  var v8_system_enums_email = 'важностьинтернетпочтовогосообщения обработкатекстаинтернетпочтовогосообщения способкодированияинтернетпочтовоговложения ' + 'способкодированиянеasciiсимволовинтернетпочтовогосообщения типтекстапочтовогосообщения протоколинтернетпочты ' + 'статусразборапочтовогосообщения ';\n\n  // v8 системные перечисления - журнал регистрации ==> class\n  var v8_system_enums_logbook = 'режимтранзакциизаписижурналарегистрации статустранзакциизаписижурналарегистрации уровеньжурналарегистрации ';\n\n  // v8 системные перечисления - криптография ==> class\n  var v8_system_enums_cryptography = 'расположениехранилищасертификатовкриптографии режимвключениясертификатовкриптографии режимпроверкисертификатакриптографии ' + 'типхранилищасертификатовкриптографии ';\n\n  // v8 системные перечисления - ZIP ==> class\n  var v8_system_enums_zip = 'кодировкаименфайловвzipфайле методсжатияzip методшифрованияzip режимвосстановленияпутейфайловzip режимобработкиподкаталоговzip ' + 'режимсохраненияпутейzip уровеньсжатияzip ';\n\n  // v8 системные перечисления -\n  // Блокировка данных, Фоновые задания, Автоматизированное тестирование,\n  // Доставляемые уведомления, Встроенные покупки, Интернет, Работа с двоичными данными ==> class\n  var v8_system_enums_other = 'звуковоеоповещение направлениепереходакстроке позициявпотоке порядокбайтов режимблокировкиданных режимуправленияблокировкойданных ' + 'сервисвстроенныхпокупок состояниефоновогозадания типподписчикадоставляемыхуведомлений уровеньиспользованиязащищенногосоединенияftp ';\n\n  // v8 системные перечисления - схема запроса ==> class\n  var v8_system_enums_request_schema = 'направлениепорядкасхемызапроса типдополненияпериодамисхемызапроса типконтрольнойточкисхемызапроса типобъединениясхемызапроса ' + 'типпараметрадоступнойтаблицысхемызапроса типсоединениясхемызапроса ';\n\n  // v8 системные перечисления - свойства объектов метаданных ==> class\n  var v8_system_enums_properties_of_metadata_objects = 'httpметод автоиспользованиеобщегореквизита автопрефиксномеразадачи вариантвстроенногоязыка видиерархии видрегистранакопления ' + 'видтаблицывнешнегоисточникаданных записьдвиженийприпроведении заполнениепоследовательностей индексирование ' + 'использованиебазыпланавидоврасчета использованиебыстроговыбора использованиеобщегореквизита использованиеподчинения ' + 'использованиеполнотекстовогопоиска использованиеразделяемыхданныхобщегореквизита использованиереквизита ' + 'назначениеиспользованияприложения назначениерасширенияконфигурации направлениепередачи обновлениепредопределенныхданных ' + 'оперативноепроведение основноепредставлениевидарасчета основноепредставлениевидахарактеристики основноепредставлениезадачи ' + 'основноепредставлениепланаобмена основноепредставлениесправочника основноепредставлениесчета перемещениеграницыприпроведении ' + 'периодичностьномерабизнеспроцесса периодичностьномерадокумента периодичностьрегистрарасчета периодичностьрегистрасведений ' + 'повторноеиспользованиевозвращаемыхзначений полнотекстовыйпоискпривводепостроке принадлежностьобъекта проведение ' + 'разделениеаутентификацииобщегореквизита разделениеданныхобщегореквизита разделениерасширенийконфигурацииобщегореквизита ' + 'режимавтонумерацииобъектов режимзаписирегистра режимиспользованиямодальности ' + 'режимиспользованиясинхронныхвызововрасширенийплатформыивнешнихкомпонент режимповторногоиспользованиясеансов ' + 'режимполученияданныхвыборапривводепостроке режимсовместимости режимсовместимостиинтерфейса ' + 'режимуправленияблокировкойданныхпоумолчанию сериикодовпланавидовхарактеристик сериикодовпланасчетов ' + 'сериикодовсправочника созданиепривводе способвыбора способпоискастрокипривводепостроке способредактирования ' + 'типданныхтаблицывнешнегоисточникаданных типкодапланавидоврасчета типкодасправочника типмакета типномерабизнеспроцесса ' + 'типномерадокумента типномеразадачи типформы удалениедвижений ';\n\n  // v8 системные перечисления - разные ==> class\n  var v8_system_enums_differents = 'важностьпроблемыприменениярасширенияконфигурации вариантинтерфейсаклиентскогоприложения вариантмасштабаформклиентскогоприложения ' + 'вариантосновногошрифтаклиентскогоприложения вариантстандартногопериода вариантстандартнойдатыначала видграницы видкартинки ' + 'видотображенияполнотекстовогопоиска видрамки видсравнения видцвета видчисловогозначения видшрифта допустимаядлина допустимыйзнак ' + 'использованиеbyteordermark использованиеметаданныхполнотекстовогопоиска источникрасширенийконфигурации клавиша кодвозвратадиалога ' + 'кодировкаxbase кодировкатекста направлениепоиска направлениесортировки обновлениепредопределенныхданных обновлениеприизмененииданных ' + 'отображениепанелиразделов проверказаполнения режимдиалогавопрос режимзапускаклиентскогоприложения режимокругления режимоткрытияформприложения ' + 'режимполнотекстовогопоиска скоростьклиентскогосоединения состояниевнешнегоисточникаданных состояниеобновленияконфигурациибазыданных ' + 'способвыборасертификатаwindows способкодированиястроки статуссообщения типвнешнейкомпоненты типплатформы типповеденияклавишиenter ' + 'типэлементаинформацииовыполненииобновленияконфигурациибазыданных уровеньизоляциитранзакций хешфункция частидаты';\n\n  // class: встроенные наборы значений, системные перечисления (содержат дочерние значения, обращения к которым через разыменование)\n  var CLASS = v8_system_sets_of_values + v8_system_enums_interface + v8_system_enums_objects_properties + v8_system_enums_exchange_plans + v8_system_enums_tabular_document + v8_system_enums_sheduler + v8_system_enums_formatted_document + v8_system_enums_query + v8_system_enums_report_builder + v8_system_enums_files + v8_system_enums_query_builder + v8_system_enums_data_analysis + v8_system_enums_xml_json_xs_dom_xdto_ws + v8_system_enums_data_composition_system + v8_system_enums_email + v8_system_enums_logbook + v8_system_enums_cryptography + v8_system_enums_zip + v8_system_enums_other + v8_system_enums_request_schema + v8_system_enums_properties_of_metadata_objects + v8_system_enums_differents;\n\n  // v8 общие объекты (у объектов есть конструктор, экземпляры создаются методом НОВЫЙ) ==> type\n  var v8_shared_object = 'comобъект ftpсоединение httpзапрос httpсервисответ httpсоединение wsопределения wsпрокси xbase анализданных аннотацияxs ' + 'блокировкаданных буфердвоичныхданных включениеxs выражениекомпоновкиданных генераторслучайныхчисел географическаясхема ' + 'географическиекоординаты графическаясхема группамоделиxs данныерасшифровкикомпоновкиданных двоичныеданные дендрограмма ' + 'диаграмма диаграммаганта диалогвыборафайла диалогвыборацвета диалогвыборашрифта диалограсписаниярегламентногозадания ' + 'диалогредактированиястандартногопериода диапазон документdom документhtml документацияxs доставляемоеуведомление ' + 'записьdom записьfastinfoset записьhtml записьjson записьxml записьzipфайла записьданных записьтекста записьузловdom ' + 'запрос защищенноесоединениеopenssl значенияполейрасшифровкикомпоновкиданных извлечениетекста импортxs интернетпочта ' + 'интернетпочтовоесообщение интернетпочтовыйпрофиль интернетпрокси интернетсоединение информациядляприложенияxs ' + 'использованиеатрибутаxs использованиесобытияжурналарегистрации источникдоступныхнастроеккомпоновкиданных ' + 'итераторузловdom картинка квалификаторыдаты квалификаторыдвоичныхданных квалификаторыстроки квалификаторычисла ' + 'компоновщикмакетакомпоновкиданных компоновщикнастроеккомпоновкиданных конструктормакетаоформлениякомпоновкиданных ' + 'конструкторнастроеккомпоновкиданных конструкторформатнойстроки линия макеткомпоновкиданных макетобластикомпоновкиданных ' + 'макетоформлениякомпоновкиданных маскаxs менеджеркриптографии наборсхемxml настройкикомпоновкиданных настройкисериализацииjson ' + 'обработкакартинок обработкарасшифровкикомпоновкиданных обходдереваdom объявлениеатрибутаxs объявлениенотацииxs ' + 'объявлениеэлементаxs описаниеиспользованиясобытиядоступжурналарегистрации ' + 'описаниеиспользованиясобытияотказвдоступежурналарегистрации описаниеобработкирасшифровкикомпоновкиданных ' + 'описаниепередаваемогофайла описаниетипов определениегруппыатрибутовxs определениегруппымоделиxs ' + 'определениеограниченияидентичностиxs определениепростоготипаxs определениесоставноготипаxs определениетипадокументаdom ' + 'определенияxpathxs отборкомпоновкиданных пакетотображаемыхдокументов параметрвыбора параметркомпоновкиданных ' + 'параметрызаписиjson параметрызаписиxml параметрычтенияxml переопределениеxs планировщик полеанализаданных ' + 'полекомпоновкиданных построительdom построительзапроса построительотчета построительотчетаанализаданных ' + 'построительсхемxml поток потоквпамяти почта почтовоесообщение преобразованиеxsl преобразованиекканоническомуxml ' + 'процессорвыводарезультатакомпоновкиданныхвколлекциюзначений процессорвыводарезультатакомпоновкиданныхвтабличныйдокумент ' + 'процессоркомпоновкиданных разыменовательпространствименdom рамка расписаниерегламентногозадания расширенноеимяxml ' + 'результатчтенияданных своднаядиаграмма связьпараметравыбора связьпотипу связьпотипукомпоновкиданных сериализаторxdto ' + 'сертификатклиентаwindows сертификатклиентафайл сертификаткриптографии сертификатыудостоверяющихцентровwindows ' + 'сертификатыудостоверяющихцентровфайл сжатиеданных системнаяинформация сообщениепользователю сочетаниеклавиш ' + 'сравнениезначений стандартнаядатаначала стандартныйпериод схемаxml схемакомпоновкиданных табличныйдокумент ' + 'текстовыйдокумент тестируемоеприложение типданныхxml уникальныйидентификатор фабрикаxdto файл файловыйпоток ' + 'фасетдлиныxs фасетколичестваразрядовдробнойчастиxs фасетмаксимальноговключающегозначенияxs ' + 'фасетмаксимальногоисключающегозначенияxs фасетмаксимальнойдлиныxs фасетминимальноговключающегозначенияxs ' + 'фасетминимальногоисключающегозначенияxs фасетминимальнойдлиныxs фасетобразцаxs фасетобщегоколичестваразрядовxs ' + 'фасетперечисленияxs фасетпробельныхсимволовxs фильтрузловdom форматированнаястрока форматированныйдокумент ' + 'фрагментxs хешированиеданных хранилищезначения цвет чтениеfastinfoset чтениеhtml чтениеjson чтениеxml чтениеzipфайла ' + 'чтениеданных чтениетекста чтениеузловdom шрифт элементрезультатакомпоновкиданных ';\n\n  // v8 универсальные коллекции значений ==> type\n  var v8_universal_collection = 'comsafearray деревозначений массив соответствие списокзначений структура таблицазначений фиксированнаяструктура ' + 'фиксированноесоответствие фиксированныймассив ';\n\n  // type : встроенные типы\n  var TYPE = v8_shared_object + v8_universal_collection;\n\n  // literal : примитивные типы\n  var LITERAL = 'null истина ложь неопределено';\n\n  // number : числа\n  var NUMBERS = hljs.inherit(hljs.NUMBER_MODE);\n\n  // string : строки\n  var STRINGS = {\n    className: 'string',\n    begin: '\"|\\\\|',\n    end: '\"|$',\n    contains: [{\n      begin: '\"\"'\n    }]\n  };\n\n  // number : даты\n  var DATE = {\n    begin: \"'\",\n    end: \"'\",\n    excludeBegin: true,\n    excludeEnd: true,\n    contains: [{\n      className: 'number',\n      begin: '\\\\d{4}([\\\\.\\\\\\\\/:-]?\\\\d{2}){0,5}'\n    }]\n  };\n\n  // comment : комментарии\n  var COMMENTS = hljs.inherit(hljs.C_LINE_COMMENT_MODE);\n\n  // meta : инструкции препроцессора, директивы компиляции\n  var META = {\n    className: 'meta',\n    begin: '#|&',\n    end: '$',\n    keywords: {\n      $pattern: UNDERSCORE_IDENT_RE,\n      'meta-keyword': KEYWORD + METAKEYWORD\n    },\n    contains: [COMMENTS]\n  };\n\n  // symbol : метка goto\n  var SYMBOL = {\n    className: 'symbol',\n    begin: '~',\n    end: ';|:',\n    excludeEnd: true\n  };\n\n  // function : объявление процедур и функций\n  var FUNCTION = {\n    className: 'function',\n    variants: [{\n      begin: 'процедура|функция',\n      end: '\\\\)',\n      keywords: 'процедура функция'\n    }, {\n      begin: 'конецпроцедуры|конецфункции',\n      keywords: 'конецпроцедуры конецфункции'\n    }],\n    contains: [{\n      begin: '\\\\(',\n      end: '\\\\)',\n      endsParent: true,\n      contains: [{\n        className: 'params',\n        begin: UNDERSCORE_IDENT_RE,\n        end: ',',\n        excludeEnd: true,\n        endsWithParent: true,\n        keywords: {\n          $pattern: UNDERSCORE_IDENT_RE,\n          keyword: 'знач',\n          literal: LITERAL\n        },\n        contains: [NUMBERS, STRINGS, DATE]\n      }, COMMENTS]\n    }, hljs.inherit(hljs.TITLE_MODE, {\n      begin: UNDERSCORE_IDENT_RE\n    })]\n  };\n  return {\n    name: '1C:Enterprise',\n    case_insensitive: true,\n    keywords: {\n      $pattern: UNDERSCORE_IDENT_RE,\n      keyword: KEYWORD,\n      built_in: BUILTIN,\n      class: CLASS,\n      type: TYPE,\n      literal: LITERAL\n    },\n    contains: [META, FUNCTION, COMMENTS, SYMBOL, NUMBERS, STRINGS, DATE]\n  };\n}\nmodule.exports = _1c;", "map": {"version": 3, "names": ["_1c", "hljs", "UNDERSCORE_IDENT_RE", "v7_keywords", "v8_keywords", "KEYWORD", "v7_meta_keywords", "v8_meta_keywords", "METAKEYWORD", "v7_system_constants", "v7_global_context_methods", "v8_global_context_methods", "v8_global_context_property", "BUILTIN", "v8_system_sets_of_values", "v8_system_enums_interface", "v8_system_enums_objects_properties", "v8_system_enums_exchange_plans", "v8_system_enums_tabular_document", "v8_system_enums_sheduler", "v8_system_enums_formatted_document", "v8_system_enums_query", "v8_system_enums_report_builder", "v8_system_enums_files", "v8_system_enums_query_builder", "v8_system_enums_data_analysis", "v8_system_enums_xml_json_xs_dom_xdto_ws", "v8_system_enums_data_composition_system", "v8_system_enums_email", "v8_system_enums_logbook", "v8_system_enums_cryptography", "v8_system_enums_zip", "v8_system_enums_other", "v8_system_enums_request_schema", "v8_system_enums_properties_of_metadata_objects", "v8_system_enums_differents", "CLASS", "v8_shared_object", "v8_universal_collection", "TYPE", "LITERAL", "NUMBERS", "inherit", "NUMBER_MODE", "STRINGS", "className", "begin", "end", "contains", "DATE", "excludeBegin", "excludeEnd", "COMMENTS", "C_LINE_COMMENT_MODE", "META", "keywords", "$pattern", "SYMBOL", "FUNCTION", "variants", "endsParent", "endsWithParent", "keyword", "literal", "TITLE_MODE", "name", "case_insensitive", "built_in", "class", "type", "module", "exports"], "sources": ["C:/Users/<USER>/PycharmProjects/智能数据分析系统/chatdb/frontend/node_modules/highlight.js/lib/languages/1c.js"], "sourcesContent": ["/*\nLanguage: 1C:Enterprise\nAuthor: <PERSON><PERSON> <<EMAIL>>\nDescription: built-in language 1C:Enterprise (v7, v8)\nCategory: enterprise\n*/\n\nfunction _1c(hljs) {\n\n  // общий паттерн для определения идентификаторов\n  var UNDERSCORE_IDENT_RE = '[A-Za-zА-Яа-яёЁ_][A-Za-zА-Яа-яёЁ_0-9]+';\n\n  // v7 уникальные ключевые слова, отсутствующие в v8 ==> keyword\n  var v7_keywords =\n  'далее ';\n\n  // v8 ключевые слова ==> keyword\n  var v8_keywords =\n  'возврат вызватьисключение выполнить для если и из или иначе иначеесли исключение каждого конецесли ' +\n  'конецпопытки конеццикла не новый перейти перем по пока попытка прервать продолжить тогда цикл экспорт ';\n\n  // keyword : ключевые слова\n  var KEYWORD = v7_keywords + v8_keywords;\n\n  // v7 уникальные директивы, отсутствующие в v8 ==> meta-keyword\n  var v7_meta_keywords =\n  'загрузитьизфайла ';\n\n  // v8 ключевые слова в инструкциях препроцессора, директивах компиляции, аннотациях ==> meta-keyword\n  var v8_meta_keywords =\n  'вебклиент вместо внешнеесоединение клиент конецобласти мобильноеприложениеклиент мобильноеприложениесервер ' +\n  'наклиенте наклиентенасервере наклиентенасерверебезконтекста насервере насерверебезконтекста область перед ' +\n  'после сервер толстыйклиентобычноеприложение толстыйклиентуправляемоеприложение тонкийклиент ';\n\n  // meta-keyword : ключевые слова в инструкциях препроцессора, директивах компиляции, аннотациях\n  var METAKEYWORD = v7_meta_keywords + v8_meta_keywords;\n\n  // v7 системные константы ==> built_in\n  var v7_system_constants =\n  'разделительстраниц разделительстрок символтабуляции ';\n\n  // v7 уникальные методы глобального контекста, отсутствующие в v8 ==> built_in\n  var v7_global_context_methods =\n  'ansitooem oemtoansi ввестивидсубконто ввестиперечисление ввестипериод ввестиплансчетов выбранныйплансчетов ' +\n  'датагод датамесяц датачисло заголовоксистемы значениевстроку значениеизстроки каталогиб каталогпользователя ' +\n  'кодсимв конгода конецпериодаби конецрассчитанногопериодаби конецстандартногоинтервала конквартала конмесяца ' +\n  'коннедели лог лог10 максимальноеколичествосубконто названиеинтерфейса названиенабораправ назначитьвид ' +\n  'назначитьсчет найтиссылки началопериодаби началостандартногоинтервала начгода начквартала начмесяца ' +\n  'начнедели номерднягода номерднянедели номернеделигода обработкаожидания основнойжурналрасчетов ' +\n  'основнойплансчетов основнойязык очиститьокносообщений периодстр получитьвремята получитьдатута ' +\n  'получитьдокументта получитьзначенияотбора получитьпозициюта получитьпустоезначение получитьта ' +\n  'префиксавтонумерации пропись пустоезначение разм разобратьпозициюдокумента рассчитатьрегистрына ' +\n  'рассчитатьрегистрыпо симв создатьобъект статусвозврата стрколичествострок сформироватьпозициюдокумента ' +\n  'счетпокоду текущеевремя типзначения типзначениястр установитьтана установитьтапо фиксшаблон шаблон ';\n\n  // v8 методы глобального контекста ==> built_in\n  var v8_global_context_methods =\n  'acos asin atan base64значение base64строка cos exp log log10 pow sin sqrt tan xmlзначение xmlстрока ' +\n  'xmlтип xmlтипзнч активноеокно безопасныйрежим безопасныйрежимразделенияданных булево ввестидату ввестизначение ' +\n  'ввестистроку ввестичисло возможностьчтенияxml вопрос восстановитьзначение врег выгрузитьжурналрегистрации ' +\n  'выполнитьобработкуоповещения выполнитьпроверкуправдоступа вычислить год данныеформывзначение дата день деньгода ' +\n  'деньнедели добавитьмесяц заблокироватьданныедляредактирования заблокироватьработупользователя завершитьработусистемы ' +\n  'загрузитьвнешнююкомпоненту закрытьсправку записатьjson записатьxml записатьдатуjson записьжурналарегистрации ' +\n  'заполнитьзначениясвойств запроситьразрешениепользователя запуститьприложение запуститьсистему зафиксироватьтранзакцию ' +\n  'значениевданныеформы значениевстрокувнутр значениевфайл значениезаполнено значениеизстрокивнутр значениеизфайла ' +\n  'изxmlтипа импортмоделиxdto имякомпьютера имяпользователя инициализироватьпредопределенныеданные информацияобошибке ' +\n  'каталогбиблиотекимобильногоустройства каталогвременныхфайлов каталогдокументов каталогпрограммы кодироватьстроку ' +\n  'кодлокализацииинформационнойбазы кодсимвола командасистемы конецгода конецдня конецквартала конецмесяца конецминуты ' +\n  'конецнедели конецчаса конфигурациябазыданныхизмененадинамически конфигурацияизменена копироватьданныеформы ' +\n  'копироватьфайл краткоепредставлениеошибки лев макс местноевремя месяц мин минута монопольныйрежим найти ' +\n  'найтинедопустимыесимволыxml найтиокнопонавигационнойссылке найтипомеченныенаудаление найтипоссылкам найтифайлы ' +\n  'началогода началодня началоквартала началомесяца началоминуты началонедели началочаса начатьзапросразрешенияпользователя ' +\n  'начатьзапускприложения начатькопированиефайла начатьперемещениефайла начатьподключениевнешнейкомпоненты ' +\n  'начатьподключениерасширенияработыскриптографией начатьподключениерасширенияработысфайлами начатьпоискфайлов ' +\n  'начатьполучениекаталогавременныхфайлов начатьполучениекаталогадокументов начатьполучениерабочегокаталогаданныхпользователя ' +\n  'начатьполучениефайлов начатьпомещениефайла начатьпомещениефайлов начатьсозданиедвоичныхданныхизфайла начатьсозданиекаталога ' +\n  'начатьтранзакцию начатьудалениефайлов начатьустановкувнешнейкомпоненты начатьустановкурасширенияработыскриптографией ' +\n  'начатьустановкурасширенияработысфайлами неделягода необходимостьзавершениясоединения номерсеансаинформационнойбазы ' +\n  'номерсоединенияинформационнойбазы нрег нстр обновитьинтерфейс обновитьнумерациюобъектов обновитьповторноиспользуемыезначения ' +\n  'обработкапрерыванияпользователя объединитьфайлы окр описаниеошибки оповестить оповеститьобизменении ' +\n  'отключитьобработчикзапросанастроекклиенталицензирования отключитьобработчикожидания отключитьобработчикоповещения ' +\n  'открытьзначение открытьиндекссправки открытьсодержаниесправки открытьсправку открытьформу открытьформумодально ' +\n  'отменитьтранзакцию очиститьжурналрегистрации очиститьнастройкипользователя очиститьсообщения параметрыдоступа ' +\n  'перейтипонавигационнойссылке переместитьфайл подключитьвнешнююкомпоненту ' +\n  'подключитьобработчикзапросанастроекклиенталицензирования подключитьобработчикожидания подключитьобработчикоповещения ' +\n  'подключитьрасширениеработыскриптографией подключитьрасширениеработысфайлами подробноепредставлениеошибки ' +\n  'показатьвводдаты показатьвводзначения показатьвводстроки показатьвводчисла показатьвопрос показатьзначение ' +\n  'показатьинформациюобошибке показатьнакарте показатьоповещениепользователя показатьпредупреждение полноеимяпользователя ' +\n  'получитьcomобъект получитьxmlтип получитьадреспоместоположению получитьблокировкусеансов получитьвремязавершенияспящегосеанса ' +\n  'получитьвремязасыпанияпассивногосеанса получитьвремяожиданияблокировкиданных получитьданныевыбора ' +\n  'получитьдополнительныйпараметрклиенталицензирования получитьдопустимыекодылокализации получитьдопустимыечасовыепояса ' +\n  'получитьзаголовокклиентскогоприложения получитьзаголовоксистемы получитьзначенияотборажурналарегистрации ' +\n  'получитьидентификаторконфигурации получитьизвременногохранилища получитьимявременногофайла ' +\n  'получитьимяклиенталицензирования получитьинформациюэкрановклиента получитьиспользованиежурналарегистрации ' +\n  'получитьиспользованиесобытияжурналарегистрации получитькраткийзаголовокприложения получитьмакетоформления ' +\n  'получитьмаскувсефайлы получитьмаскувсефайлыклиента получитьмаскувсефайлысервера получитьместоположениепоадресу ' +\n  'получитьминимальнуюдлинупаролейпользователей получитьнавигационнуюссылку получитьнавигационнуюссылкуинформационнойбазы ' +\n  'получитьобновлениеконфигурациибазыданных получитьобновлениепредопределенныхданныхинформационнойбазы получитьобщиймакет ' +\n  'получитьобщуюформу получитьокна получитьоперативнуюотметкувремени получитьотключениебезопасногорежима ' +\n  'получитьпараметрыфункциональныхопцийинтерфейса получитьполноеимяпредопределенногозначения ' +\n  'получитьпредставлениянавигационныхссылок получитьпроверкусложностипаролейпользователей получитьразделительпути ' +\n  'получитьразделительпутиклиента получитьразделительпутисервера получитьсеансыинформационнойбазы ' +\n  'получитьскоростьклиентскогосоединения получитьсоединенияинформационнойбазы получитьсообщенияпользователю ' +\n  'получитьсоответствиеобъектаиформы получитьсоставстандартногоинтерфейсаodata получитьструктурухранениябазыданных ' +\n  'получитьтекущийсеансинформационнойбазы получитьфайл получитьфайлы получитьформу получитьфункциональнуюопцию ' +\n  'получитьфункциональнуюопциюинтерфейса получитьчасовойпоясинформационнойбазы пользователиос поместитьвовременноехранилище ' +\n  'поместитьфайл поместитьфайлы прав праводоступа предопределенноезначение представлениекодалокализации представлениепериода ' +\n  'представлениеправа представлениеприложения представлениесобытияжурналарегистрации представлениечасовогопояса предупреждение ' +\n  'прекратитьработусистемы привилегированныйрежим продолжитьвызов прочитатьjson прочитатьxml прочитатьдатуjson пустаястрока ' +\n  'рабочийкаталогданныхпользователя разблокироватьданныедляредактирования разделитьфайл разорватьсоединениесвнешнимисточникомданных ' +\n  'раскодироватьстроку рольдоступна секунда сигнал символ скопироватьжурналрегистрации смещениелетнеговремени ' +\n  'смещениестандартноговремени соединитьбуферыдвоичныхданных создатькаталог создатьфабрикуxdto сокрл сокрлп сокрп сообщить ' +\n  'состояние сохранитьзначение сохранитьнастройкипользователя сред стрдлина стрзаканчиваетсяна стрзаменить стрнайти стрначинаетсяс ' +\n  'строка строкасоединенияинформационнойбазы стрполучитьстроку стрразделить стрсоединить стрсравнить стрчисловхождений '+\n  'стрчислострок стршаблон текущаядата текущаядатасеанса текущаяуниверсальнаядата текущаяуниверсальнаядатавмиллисекундах ' +\n  'текущийвариантинтерфейсаклиентскогоприложения текущийвариантосновногошрифтаклиентскогоприложения текущийкодлокализации ' +\n  'текущийрежимзапуска текущийязык текущийязыксистемы тип типзнч транзакцияактивна трег удалитьданныеинформационнойбазы ' +\n  'удалитьизвременногохранилища удалитьобъекты удалитьфайлы универсальноевремя установитьбезопасныйрежим ' +\n  'установитьбезопасныйрежимразделенияданных установитьблокировкусеансов установитьвнешнююкомпоненту ' +\n  'установитьвремязавершенияспящегосеанса установитьвремязасыпанияпассивногосеанса установитьвремяожиданияблокировкиданных ' +\n  'установитьзаголовокклиентскогоприложения установитьзаголовоксистемы установитьиспользованиежурналарегистрации ' +\n  'установитьиспользованиесобытияжурналарегистрации установитькраткийзаголовокприложения ' +\n  'установитьминимальнуюдлинупаролейпользователей установитьмонопольныйрежим установитьнастройкиклиенталицензирования ' +\n  'установитьобновлениепредопределенныхданныхинформационнойбазы установитьотключениебезопасногорежима ' +\n  'установитьпараметрыфункциональныхопцийинтерфейса установитьпривилегированныйрежим ' +\n  'установитьпроверкусложностипаролейпользователей установитьрасширениеработыскриптографией ' +\n  'установитьрасширениеработысфайлами установитьсоединениесвнешнимисточникомданных установитьсоответствиеобъектаиформы ' +\n  'установитьсоставстандартногоинтерфейсаodata установитьчасовойпоясинформационнойбазы установитьчасовойпояссеанса ' +\n  'формат цел час часовойпояс часовойпояссеанса число числопрописью этоадресвременногохранилища ';\n\n  // v8 свойства глобального контекста ==> built_in\n  var v8_global_context_property =\n  'wsссылки библиотекакартинок библиотекамакетовоформлениякомпоновкиданных библиотекастилей бизнеспроцессы ' +\n  'внешниеисточникиданных внешниеобработки внешниеотчеты встроенныепокупки главныйинтерфейс главныйстиль ' +\n  'документы доставляемыеуведомления журналыдокументов задачи информацияобинтернетсоединении использованиерабочейдаты ' +\n  'историяработыпользователя константы критерииотбора метаданные обработки отображениерекламы отправкадоставляемыхуведомлений ' +\n  'отчеты панельзадачос параметрзапуска параметрысеанса перечисления планывидоврасчета планывидовхарактеристик ' +\n  'планыобмена планысчетов полнотекстовыйпоиск пользователиинформационнойбазы последовательности проверкавстроенныхпокупок ' +\n  'рабочаядата расширенияконфигурации регистрыбухгалтерии регистрынакопления регистрырасчета регистрысведений ' +\n  'регламентныезадания сериализаторxdto справочники средствагеопозиционирования средствакриптографии средствамультимедиа ' +\n  'средстваотображениярекламы средствапочты средствателефонии фабрикаxdto файловыепотоки фоновыезадания хранилищанастроек ' +\n  'хранилищевариантовотчетов хранилищенастроекданныхформ хранилищеобщихнастроек хранилищепользовательскихнастроекдинамическихсписков ' +\n  'хранилищепользовательскихнастроекотчетов хранилищесистемныхнастроек ';\n\n  // built_in : встроенные или библиотечные объекты (константы, классы, функции)\n  var BUILTIN =\n  v7_system_constants +\n  v7_global_context_methods + v8_global_context_methods +\n  v8_global_context_property;\n\n  // v8 системные наборы значений ==> class\n  var v8_system_sets_of_values =\n  'webцвета windowsцвета windowsшрифты библиотекакартинок рамкистиля символы цветастиля шрифтыстиля ';\n\n  // v8 системные перечисления - интерфейсные ==> class\n  var v8_system_enums_interface =\n  'автоматическоесохранениеданныхформывнастройках автонумерациявформе автораздвижениесерий ' +\n  'анимациядиаграммы вариантвыравниванияэлементовизаголовков вариантуправлениявысотойтаблицы ' +\n  'вертикальнаяпрокруткаформы вертикальноеположение вертикальноеположениеэлемента видгруппыформы ' +\n  'виддекорацииформы виддополненияэлементаформы видизмененияданных видкнопкиформы видпереключателя ' +\n  'видподписейкдиаграмме видполяформы видфлажка влияниеразмеранапузырекдиаграммы горизонтальноеположение ' +\n  'горизонтальноеположениеэлемента группировкаколонок группировкаподчиненныхэлементовформы ' +\n  'группыиэлементы действиеперетаскивания дополнительныйрежимотображения допустимыедействияперетаскивания ' +\n  'интервалмеждуэлементамиформы использованиевывода использованиеполосыпрокрутки ' +\n  'используемоезначениеточкибиржевойдиаграммы историявыборапривводе источникзначенийоситочекдиаграммы ' +\n  'источникзначенияразмерапузырькадиаграммы категориягруппыкоманд максимумсерий начальноеотображениедерева ' +\n  'начальноеотображениесписка обновлениетекстаредактирования ориентациядендрограммы ориентациядиаграммы ' +\n  'ориентацияметокдиаграммы ориентацияметоксводнойдиаграммы ориентацияэлементаформы отображениевдиаграмме ' +\n  'отображениевлегендедиаграммы отображениегруппыкнопок отображениезаголовкашкалыдиаграммы ' +\n  'отображениезначенийсводнойдиаграммы отображениезначенияизмерительнойдиаграммы ' +\n  'отображениеинтерваладиаграммыганта отображениекнопки отображениекнопкивыбора отображениеобсужденийформы ' +\n  'отображениеобычнойгруппы отображениеотрицательныхзначенийпузырьковойдиаграммы отображениепанелипоиска ' +\n  'отображениеподсказки отображениепредупрежденияприредактировании отображениеразметкиполосырегулирования ' +\n  'отображениестраницформы отображениетаблицы отображениетекстазначениядиаграммыганта ' +\n  'отображениеуправленияобычнойгруппы отображениефигурыкнопки палитрацветовдиаграммы поведениеобычнойгруппы ' +\n  'поддержкамасштабадендрограммы поддержкамасштабадиаграммыганта поддержкамасштабасводнойдиаграммы ' +\n  'поисквтаблицепривводе положениезаголовкаэлементаформы положениекартинкикнопкиформы ' +\n  'положениекартинкиэлементаграфическойсхемы положениекоманднойпанелиформы положениекоманднойпанелиэлементаформы ' +\n  'положениеопорнойточкиотрисовки положениеподписейкдиаграмме положениеподписейшкалызначенийизмерительнойдиаграммы ' +\n  'положениесостоянияпросмотра положениестрокипоиска положениетекстасоединительнойлинии положениеуправленияпоиском ' +\n  'положениешкалывремени порядокотображенияточекгоризонтальнойгистограммы порядоксерийвлегендедиаграммы ' +\n  'размеркартинки расположениезаголовкашкалыдиаграммы растягиваниеповертикалидиаграммыганта ' +\n  'режимавтоотображениясостояния режимвводастроктаблицы режимвыборанезаполненного режимвыделениядаты ' +\n  'режимвыделениястрокитаблицы режимвыделениятаблицы режимизмененияразмера режимизменениясвязанногозначения ' +\n  'режимиспользованиядиалогапечати режимиспользованияпараметракоманды режиммасштабированияпросмотра ' +\n  'режимосновногоокнаклиентскогоприложения режимоткрытияокнаформы режимотображениявыделения ' +\n  'режимотображениягеографическойсхемы режимотображениязначенийсерии режимотрисовкисеткиграфическойсхемы ' +\n  'режимполупрозрачностидиаграммы режимпробеловдиаграммы режимразмещениянастранице режимредактированияколонки ' +\n  'режимсглаживаниядиаграммы режимсглаживанияиндикатора режимсписказадач сквозноевыравнивание ' +\n  'сохранениеданныхформывнастройках способзаполнениятекстазаголовкашкалыдиаграммы ' +\n  'способопределенияограничивающегозначениядиаграммы стандартнаягруппакоманд стандартноеоформление ' +\n  'статусоповещенияпользователя стильстрелки типаппроксимациилиниитрендадиаграммы типдиаграммы ' +\n  'типединицышкалывремени типимпортасерийслоягеографическойсхемы типлиниигеографическойсхемы типлиниидиаграммы ' +\n  'типмаркерагеографическойсхемы типмаркерадиаграммы типобластиоформления ' +\n  'типорганизацииисточникаданныхгеографическойсхемы типотображениясериислоягеографическойсхемы ' +\n  'типотображенияточечногообъектагеографическойсхемы типотображенияшкалыэлементалегендыгеографическойсхемы ' +\n  'типпоискаобъектовгеографическойсхемы типпроекциигеографическойсхемы типразмещенияизмерений ' +\n  'типразмещенияреквизитовизмерений типрамкиэлементауправления типсводнойдиаграммы ' +\n  'типсвязидиаграммыганта типсоединениязначенийпосериямдиаграммы типсоединенияточекдиаграммы ' +\n  'типсоединительнойлинии типстороныэлементаграфическойсхемы типформыотчета типшкалырадарнойдиаграммы ' +\n  'факторлиниитрендадиаграммы фигуракнопки фигурыграфическойсхемы фиксациявтаблице форматдняшкалывремени ' +\n  'форматкартинки ширинаподчиненныхэлементовформы ';\n\n  // v8 системные перечисления - свойства прикладных объектов ==> class\n  var v8_system_enums_objects_properties =\n  'виддвижениябухгалтерии виддвижениянакопления видпериодарегистрарасчета видсчета видточкимаршрутабизнеспроцесса ' +\n  'использованиеагрегатарегистранакопления использованиегруппиэлементов использованиережимапроведения ' +\n  'использованиесреза периодичностьагрегатарегистранакопления режимавтовремя режимзаписидокумента режимпроведениядокумента ';\n\n  // v8 системные перечисления - планы обмена ==> class\n  var v8_system_enums_exchange_plans =\n  'авторегистрацияизменений допустимыйномерсообщения отправкаэлементаданных получениеэлементаданных ';\n\n  // v8 системные перечисления - табличный документ ==> class\n  var v8_system_enums_tabular_document =\n  'использованиерасшифровкитабличногодокумента ориентациястраницы положениеитоговколоноксводнойтаблицы ' +\n  'положениеитоговстроксводнойтаблицы положениетекстаотносительнокартинки расположениезаголовкагруппировкитабличногодокумента ' +\n  'способчтениязначенийтабличногодокумента типдвустороннейпечати типзаполненияобластитабличногодокумента ' +\n  'типкурсоровтабличногодокумента типлиниирисункатабличногодокумента типлинииячейкитабличногодокумента ' +\n  'типнаправленияпереходатабличногодокумента типотображениявыделениятабличногодокумента типотображениялинийсводнойтаблицы ' +\n  'типразмещениятекстатабличногодокумента типрисункатабличногодокумента типсмещениятабличногодокумента ' +\n  'типузоратабличногодокумента типфайлатабличногодокумента точностьпечати чередованиерасположениястраниц ';\n\n  // v8 системные перечисления - планировщик ==> class\n  var v8_system_enums_sheduler =\n  'отображениевремениэлементовпланировщика ';\n\n  // v8 системные перечисления - форматированный документ ==> class\n  var v8_system_enums_formatted_document =\n  'типфайлаформатированногодокумента ';\n\n  // v8 системные перечисления - запрос ==> class\n  var v8_system_enums_query =\n  'обходрезультатазапроса типзаписизапроса ';\n\n  // v8 системные перечисления - построитель отчета ==> class\n  var v8_system_enums_report_builder =\n  'видзаполнениярасшифровкипостроителяотчета типдобавленияпредставлений типизмеренияпостроителяотчета типразмещенияитогов ';\n\n  // v8 системные перечисления - работа с файлами ==> class\n  var v8_system_enums_files =\n  'доступкфайлу режимдиалогавыборафайла режимоткрытияфайла ';\n\n  // v8 системные перечисления - построитель запроса ==> class\n  var v8_system_enums_query_builder =\n  'типизмеренияпостроителязапроса ';\n\n  // v8 системные перечисления - анализ данных ==> class\n  var v8_system_enums_data_analysis =\n  'видданныханализа методкластеризации типединицыинтервалавременианализаданных типзаполнениятаблицырезультатаанализаданных ' +\n  'типиспользованиячисловыхзначенийанализаданных типисточникаданныхпоискаассоциаций типколонкианализаданныхдереворешений ' +\n  'типколонкианализаданныхкластеризация типколонкианализаданныхобщаястатистика типколонкианализаданныхпоискассоциаций ' +\n  'типколонкианализаданныхпоискпоследовательностей типколонкимоделипрогноза типмерырасстоянияанализаданных ' +\n  'типотсеченияправилассоциации типполяанализаданных типстандартизациианализаданных типупорядочиванияправилассоциациианализаданных ' +\n  'типупорядочиванияшаблоновпоследовательностейанализаданных типупрощениядереварешений ';\n\n  // v8 системные перечисления - xml, json, xs, dom, xdto, web-сервисы ==> class\n  var v8_system_enums_xml_json_xs_dom_xdto_ws =\n  'wsнаправлениепараметра вариантxpathxs вариантзаписидатыjson вариантпростоготипаxs видгруппымоделиxs видфасетаxdto ' +\n  'действиепостроителяdom завершенностьпростоготипаxs завершенностьсоставноготипаxs завершенностьсхемыxs запрещенныеподстановкиxs ' +\n  'исключениягруппподстановкиxs категорияиспользованияатрибутаxs категорияограниченияидентичностиxs категорияограниченияпространствименxs ' +\n  'методнаследованияxs модельсодержимогоxs назначениетипаxml недопустимыеподстановкиxs обработкапробельныхсимволовxs обработкасодержимогоxs ' +\n  'ограничениезначенияxs параметрыотбораузловdom переносстрокjson позициявдокументеdom пробельныесимволыxml типатрибутаxml типзначенияjson ' +\n  'типканоническогоxml типкомпонентыxs типпроверкиxml типрезультатаdomxpath типузлаdom типузлаxml формаxml формапредставленияxs ' +\n  'форматдатыjson экранированиесимволовjson ';\n\n  // v8 системные перечисления - система компоновки данных ==> class\n  var v8_system_enums_data_composition_system =\n  'видсравнениякомпоновкиданных действиеобработкирасшифровкикомпоновкиданных направлениесортировкикомпоновкиданных ' +\n  'расположениевложенныхэлементоврезультатакомпоновкиданных расположениеитоговкомпоновкиданных расположениегруппировкикомпоновкиданных ' +\n  'расположениеполейгруппировкикомпоновкиданных расположениеполякомпоновкиданных расположениереквизитовкомпоновкиданных ' +\n  'расположениересурсовкомпоновкиданных типбухгалтерскогоостаткакомпоновкиданных типвыводатекстакомпоновкиданных ' +\n  'типгруппировкикомпоновкиданных типгруппыэлементовотборакомпоновкиданных типдополненияпериодакомпоновкиданных ' +\n  'типзаголовкаполейкомпоновкиданных типмакетагруппировкикомпоновкиданных типмакетаобластикомпоновкиданных типостаткакомпоновкиданных ' +\n  'типпериодакомпоновкиданных типразмещениятекстакомпоновкиданных типсвязинаборовданныхкомпоновкиданных типэлементарезультатакомпоновкиданных ' +\n  'расположениелегендыдиаграммыкомпоновкиданных типпримененияотборакомпоновкиданных режимотображенияэлементанастройкикомпоновкиданных ' +\n  'режимотображениянастроеккомпоновкиданных состояниеэлементанастройкикомпоновкиданных способвосстановлениянастроеккомпоновкиданных ' +\n  'режимкомпоновкирезультата использованиепараметракомпоновкиданных автопозицияресурсовкомпоновкиданных '+\n  'вариантиспользованиягруппировкикомпоновкиданных расположениересурсоввдиаграммекомпоновкиданных фиксациякомпоновкиданных ' +\n  'использованиеусловногооформлениякомпоновкиданных ';\n\n  // v8 системные перечисления - почта ==> class\n  var v8_system_enums_email =\n  'важностьинтернетпочтовогосообщения обработкатекстаинтернетпочтовогосообщения способкодированияинтернетпочтовоговложения ' +\n  'способкодированиянеasciiсимволовинтернетпочтовогосообщения типтекстапочтовогосообщения протоколинтернетпочты ' +\n  'статусразборапочтовогосообщения ';\n\n  // v8 системные перечисления - журнал регистрации ==> class\n  var v8_system_enums_logbook =\n  'режимтранзакциизаписижурналарегистрации статустранзакциизаписижурналарегистрации уровеньжурналарегистрации ';\n\n  // v8 системные перечисления - криптография ==> class\n  var v8_system_enums_cryptography =\n  'расположениехранилищасертификатовкриптографии режимвключениясертификатовкриптографии режимпроверкисертификатакриптографии ' +\n  'типхранилищасертификатовкриптографии ';\n\n  // v8 системные перечисления - ZIP ==> class\n  var v8_system_enums_zip =\n  'кодировкаименфайловвzipфайле методсжатияzip методшифрованияzip режимвосстановленияпутейфайловzip режимобработкиподкаталоговzip ' +\n  'режимсохраненияпутейzip уровеньсжатияzip ';\n\n  // v8 системные перечисления -\n  // Блокировка данных, Фоновые задания, Автоматизированное тестирование,\n  // Доставляемые уведомления, Встроенные покупки, Интернет, Работа с двоичными данными ==> class\n  var v8_system_enums_other =\n  'звуковоеоповещение направлениепереходакстроке позициявпотоке порядокбайтов режимблокировкиданных режимуправленияблокировкойданных ' +\n  'сервисвстроенныхпокупок состояниефоновогозадания типподписчикадоставляемыхуведомлений уровеньиспользованиязащищенногосоединенияftp ';\n\n  // v8 системные перечисления - схема запроса ==> class\n  var v8_system_enums_request_schema =\n  'направлениепорядкасхемызапроса типдополненияпериодамисхемызапроса типконтрольнойточкисхемызапроса типобъединениясхемызапроса ' +\n  'типпараметрадоступнойтаблицысхемызапроса типсоединениясхемызапроса ';\n\n  // v8 системные перечисления - свойства объектов метаданных ==> class\n  var v8_system_enums_properties_of_metadata_objects =\n  'httpметод автоиспользованиеобщегореквизита автопрефиксномеразадачи вариантвстроенногоязыка видиерархии видрегистранакопления ' +\n  'видтаблицывнешнегоисточникаданных записьдвиженийприпроведении заполнениепоследовательностей индексирование ' +\n  'использованиебазыпланавидоврасчета использованиебыстроговыбора использованиеобщегореквизита использованиеподчинения ' +\n  'использованиеполнотекстовогопоиска использованиеразделяемыхданныхобщегореквизита использованиереквизита ' +\n  'назначениеиспользованияприложения назначениерасширенияконфигурации направлениепередачи обновлениепредопределенныхданных ' +\n  'оперативноепроведение основноепредставлениевидарасчета основноепредставлениевидахарактеристики основноепредставлениезадачи ' +\n  'основноепредставлениепланаобмена основноепредставлениесправочника основноепредставлениесчета перемещениеграницыприпроведении ' +\n  'периодичностьномерабизнеспроцесса периодичностьномерадокумента периодичностьрегистрарасчета периодичностьрегистрасведений ' +\n  'повторноеиспользованиевозвращаемыхзначений полнотекстовыйпоискпривводепостроке принадлежностьобъекта проведение ' +\n  'разделениеаутентификацииобщегореквизита разделениеданныхобщегореквизита разделениерасширенийконфигурацииобщегореквизита '+\n  'режимавтонумерацииобъектов режимзаписирегистра режимиспользованиямодальности ' +\n  'режимиспользованиясинхронныхвызововрасширенийплатформыивнешнихкомпонент режимповторногоиспользованиясеансов ' +\n  'режимполученияданныхвыборапривводепостроке режимсовместимости режимсовместимостиинтерфейса ' +\n  'режимуправленияблокировкойданныхпоумолчанию сериикодовпланавидовхарактеристик сериикодовпланасчетов ' +\n  'сериикодовсправочника созданиепривводе способвыбора способпоискастрокипривводепостроке способредактирования ' +\n  'типданныхтаблицывнешнегоисточникаданных типкодапланавидоврасчета типкодасправочника типмакета типномерабизнеспроцесса ' +\n  'типномерадокумента типномеразадачи типформы удалениедвижений ';\n\n  // v8 системные перечисления - разные ==> class\n  var v8_system_enums_differents =\n  'важностьпроблемыприменениярасширенияконфигурации вариантинтерфейсаклиентскогоприложения вариантмасштабаформклиентскогоприложения ' +\n  'вариантосновногошрифтаклиентскогоприложения вариантстандартногопериода вариантстандартнойдатыначала видграницы видкартинки ' +\n  'видотображенияполнотекстовогопоиска видрамки видсравнения видцвета видчисловогозначения видшрифта допустимаядлина допустимыйзнак ' +\n  'использованиеbyteordermark использованиеметаданныхполнотекстовогопоиска источникрасширенийконфигурации клавиша кодвозвратадиалога ' +\n  'кодировкаxbase кодировкатекста направлениепоиска направлениесортировки обновлениепредопределенныхданных обновлениеприизмененииданных ' +\n  'отображениепанелиразделов проверказаполнения режимдиалогавопрос режимзапускаклиентскогоприложения режимокругления режимоткрытияформприложения ' +\n  'режимполнотекстовогопоиска скоростьклиентскогосоединения состояниевнешнегоисточникаданных состояниеобновленияконфигурациибазыданных ' +\n  'способвыборасертификатаwindows способкодированиястроки статуссообщения типвнешнейкомпоненты типплатформы типповеденияклавишиenter ' +\n  'типэлементаинформацииовыполненииобновленияконфигурациибазыданных уровеньизоляциитранзакций хешфункция частидаты';\n\n  // class: встроенные наборы значений, системные перечисления (содержат дочерние значения, обращения к которым через разыменование)\n  var CLASS =\n  v8_system_sets_of_values +\n  v8_system_enums_interface +\n  v8_system_enums_objects_properties +\n  v8_system_enums_exchange_plans +\n  v8_system_enums_tabular_document +\n  v8_system_enums_sheduler +\n  v8_system_enums_formatted_document +\n  v8_system_enums_query +\n  v8_system_enums_report_builder +\n  v8_system_enums_files +\n  v8_system_enums_query_builder +\n  v8_system_enums_data_analysis +\n  v8_system_enums_xml_json_xs_dom_xdto_ws +\n  v8_system_enums_data_composition_system +\n  v8_system_enums_email +\n  v8_system_enums_logbook +\n  v8_system_enums_cryptography +\n  v8_system_enums_zip +\n  v8_system_enums_other +\n  v8_system_enums_request_schema +\n  v8_system_enums_properties_of_metadata_objects +\n  v8_system_enums_differents;\n\n  // v8 общие объекты (у объектов есть конструктор, экземпляры создаются методом НОВЫЙ) ==> type\n  var v8_shared_object =\n  'comобъект ftpсоединение httpзапрос httpсервисответ httpсоединение wsопределения wsпрокси xbase анализданных аннотацияxs ' +\n  'блокировкаданных буфердвоичныхданных включениеxs выражениекомпоновкиданных генераторслучайныхчисел географическаясхема ' +\n  'географическиекоординаты графическаясхема группамоделиxs данныерасшифровкикомпоновкиданных двоичныеданные дендрограмма ' +\n  'диаграмма диаграммаганта диалогвыборафайла диалогвыборацвета диалогвыборашрифта диалограсписаниярегламентногозадания ' +\n  'диалогредактированиястандартногопериода диапазон документdom документhtml документацияxs доставляемоеуведомление ' +\n  'записьdom записьfastinfoset записьhtml записьjson записьxml записьzipфайла записьданных записьтекста записьузловdom ' +\n  'запрос защищенноесоединениеopenssl значенияполейрасшифровкикомпоновкиданных извлечениетекста импортxs интернетпочта ' +\n  'интернетпочтовоесообщение интернетпочтовыйпрофиль интернетпрокси интернетсоединение информациядляприложенияxs ' +\n  'использованиеатрибутаxs использованиесобытияжурналарегистрации источникдоступныхнастроеккомпоновкиданных ' +\n  'итераторузловdom картинка квалификаторыдаты квалификаторыдвоичныхданных квалификаторыстроки квалификаторычисла ' +\n  'компоновщикмакетакомпоновкиданных компоновщикнастроеккомпоновкиданных конструктормакетаоформлениякомпоновкиданных ' +\n  'конструкторнастроеккомпоновкиданных конструкторформатнойстроки линия макеткомпоновкиданных макетобластикомпоновкиданных ' +\n  'макетоформлениякомпоновкиданных маскаxs менеджеркриптографии наборсхемxml настройкикомпоновкиданных настройкисериализацииjson ' +\n  'обработкакартинок обработкарасшифровкикомпоновкиданных обходдереваdom объявлениеатрибутаxs объявлениенотацииxs ' +\n  'объявлениеэлементаxs описаниеиспользованиясобытиядоступжурналарегистрации ' +\n  'описаниеиспользованиясобытияотказвдоступежурналарегистрации описаниеобработкирасшифровкикомпоновкиданных ' +\n  'описаниепередаваемогофайла описаниетипов определениегруппыатрибутовxs определениегруппымоделиxs ' +\n  'определениеограниченияидентичностиxs определениепростоготипаxs определениесоставноготипаxs определениетипадокументаdom ' +\n  'определенияxpathxs отборкомпоновкиданных пакетотображаемыхдокументов параметрвыбора параметркомпоновкиданных ' +\n  'параметрызаписиjson параметрызаписиxml параметрычтенияxml переопределениеxs планировщик полеанализаданных ' +\n  'полекомпоновкиданных построительdom построительзапроса построительотчета построительотчетаанализаданных ' +\n  'построительсхемxml поток потоквпамяти почта почтовоесообщение преобразованиеxsl преобразованиекканоническомуxml ' +\n  'процессорвыводарезультатакомпоновкиданныхвколлекциюзначений процессорвыводарезультатакомпоновкиданныхвтабличныйдокумент ' +\n  'процессоркомпоновкиданных разыменовательпространствименdom рамка расписаниерегламентногозадания расширенноеимяxml ' +\n  'результатчтенияданных своднаядиаграмма связьпараметравыбора связьпотипу связьпотипукомпоновкиданных сериализаторxdto ' +\n  'сертификатклиентаwindows сертификатклиентафайл сертификаткриптографии сертификатыудостоверяющихцентровwindows ' +\n  'сертификатыудостоверяющихцентровфайл сжатиеданных системнаяинформация сообщениепользователю сочетаниеклавиш ' +\n  'сравнениезначений стандартнаядатаначала стандартныйпериод схемаxml схемакомпоновкиданных табличныйдокумент ' +\n  'текстовыйдокумент тестируемоеприложение типданныхxml уникальныйидентификатор фабрикаxdto файл файловыйпоток ' +\n  'фасетдлиныxs фасетколичестваразрядовдробнойчастиxs фасетмаксимальноговключающегозначенияxs ' +\n  'фасетмаксимальногоисключающегозначенияxs фасетмаксимальнойдлиныxs фасетминимальноговключающегозначенияxs ' +\n  'фасетминимальногоисключающегозначенияxs фасетминимальнойдлиныxs фасетобразцаxs фасетобщегоколичестваразрядовxs ' +\n  'фасетперечисленияxs фасетпробельныхсимволовxs фильтрузловdom форматированнаястрока форматированныйдокумент ' +\n  'фрагментxs хешированиеданных хранилищезначения цвет чтениеfastinfoset чтениеhtml чтениеjson чтениеxml чтениеzipфайла ' +\n  'чтениеданных чтениетекста чтениеузловdom шрифт элементрезультатакомпоновкиданных ';\n\n  // v8 универсальные коллекции значений ==> type\n  var v8_universal_collection =\n  'comsafearray деревозначений массив соответствие списокзначений структура таблицазначений фиксированнаяструктура ' +\n  'фиксированноесоответствие фиксированныймассив ';\n\n  // type : встроенные типы\n  var TYPE =\n  v8_shared_object +\n  v8_universal_collection;\n\n  // literal : примитивные типы\n  var LITERAL = 'null истина ложь неопределено';\n\n  // number : числа\n  var NUMBERS = hljs.inherit(hljs.NUMBER_MODE);\n\n  // string : строки\n  var STRINGS = {\n    className: 'string',\n    begin: '\"|\\\\|', end: '\"|$',\n    contains: [{begin: '\"\"'}]\n  };\n\n  // number : даты\n  var DATE = {\n    begin: \"'\", end: \"'\", excludeBegin: true, excludeEnd: true,\n    contains: [\n      {\n        className: 'number',\n        begin: '\\\\d{4}([\\\\.\\\\\\\\/:-]?\\\\d{2}){0,5}'\n      }\n    ]\n  };\n\n  // comment : комментарии\n  var COMMENTS = hljs.inherit(hljs.C_LINE_COMMENT_MODE);\n\n  // meta : инструкции препроцессора, директивы компиляции\n  var META = {\n    className: 'meta',\n\n    begin: '#|&', end: '$',\n    keywords: {\n      $pattern: UNDERSCORE_IDENT_RE,\n      'meta-keyword': KEYWORD + METAKEYWORD\n    },\n    contains: [\n      COMMENTS\n    ]\n  };\n\n  // symbol : метка goto\n  var SYMBOL = {\n    className: 'symbol',\n    begin: '~', end: ';|:', excludeEnd: true\n  };\n\n  // function : объявление процедур и функций\n  var FUNCTION = {\n    className: 'function',\n    variants: [\n      {begin: 'процедура|функция', end: '\\\\)', keywords: 'процедура функция'},\n      {begin: 'конецпроцедуры|конецфункции', keywords: 'конецпроцедуры конецфункции'}\n    ],\n    contains: [\n      {\n        begin: '\\\\(', end: '\\\\)', endsParent : true,\n        contains: [\n          {\n            className: 'params',\n            begin: UNDERSCORE_IDENT_RE, end: ',', excludeEnd: true, endsWithParent: true,\n            keywords: {\n              $pattern: UNDERSCORE_IDENT_RE,\n              keyword: 'знач',\n              literal: LITERAL\n            },\n            contains: [\n              NUMBERS,\n              STRINGS,\n              DATE\n            ]\n          },\n          COMMENTS\n        ]\n      },\n      hljs.inherit(hljs.TITLE_MODE, {begin: UNDERSCORE_IDENT_RE})\n    ]\n  };\n\n  return {\n    name: '1C:Enterprise',\n    case_insensitive: true,\n    keywords: {\n      $pattern: UNDERSCORE_IDENT_RE,\n      keyword: KEYWORD,\n      built_in: BUILTIN,\n      class: CLASS,\n      type: TYPE,\n      literal: LITERAL\n    },\n    contains: [\n      META,\n      FUNCTION,\n      COMMENTS,\n      SYMBOL,\n      NUMBERS,\n      STRINGS,\n      DATE\n    ]\n  };\n}\n\nmodule.exports = _1c;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASA,GAAGA,CAACC,IAAI,EAAE;EAEjB;EACA,IAAIC,mBAAmB,GAAG,wCAAwC;;EAElE;EACA,IAAIC,WAAW,GACf,QAAQ;;EAER;EACA,IAAIC,WAAW,GACf,qGAAqG,GACrG,wGAAwG;;EAExG;EACA,IAAIC,OAAO,GAAGF,WAAW,GAAGC,WAAW;;EAEvC;EACA,IAAIE,gBAAgB,GACpB,mBAAmB;;EAEnB;EACA,IAAIC,gBAAgB,GACpB,6GAA6G,GAC7G,4GAA4G,GAC5G,8FAA8F;;EAE9F;EACA,IAAIC,WAAW,GAAGF,gBAAgB,GAAGC,gBAAgB;;EAErD;EACA,IAAIE,mBAAmB,GACvB,sDAAsD;;EAEtD;EACA,IAAIC,yBAAyB,GAC7B,6GAA6G,GAC7G,8GAA8G,GAC9G,8GAA8G,GAC9G,wGAAwG,GACxG,sGAAsG,GACtG,iGAAiG,GACjG,iGAAiG,GACjG,gGAAgG,GAChG,kGAAkG,GAClG,yGAAyG,GACzG,qGAAqG;;EAErG;EACA,IAAIC,yBAAyB,GAC7B,sGAAsG,GACtG,iHAAiH,GACjH,4GAA4G,GAC5G,kHAAkH,GAClH,uHAAuH,GACvH,+GAA+G,GAC/G,wHAAwH,GACxH,kHAAkH,GAClH,qHAAqH,GACrH,mHAAmH,GACnH,sHAAsH,GACtH,6GAA6G,GAC7G,0GAA0G,GAC1G,iHAAiH,GACjH,2HAA2H,GAC3H,0GAA0G,GAC1G,8GAA8G,GAC9G,6HAA6H,GAC7H,8HAA8H,GAC9H,uHAAuH,GACvH,qHAAqH,GACrH,+HAA+H,GAC/H,sGAAsG,GACtG,oHAAoH,GACpH,iHAAiH,GACjH,gHAAgH,GAChH,2EAA2E,GAC3E,uHAAuH,GACvH,2GAA2G,GAC3G,6GAA6G,GAC7G,yHAAyH,GACzH,gIAAgI,GAChI,oGAAoG,GACpG,uHAAuH,GACvH,2GAA2G,GAC3G,6FAA6F,GAC7F,4GAA4G,GAC5G,4GAA4G,GAC5G,iHAAiH,GACjH,yHAAyH,GACzH,yHAAyH,GACzH,wGAAwG,GACxG,4FAA4F,GAC5F,iHAAiH,GACjH,iGAAiG,GACjG,2GAA2G,GAC3G,kHAAkH,GAClH,8GAA8G,GAC9G,2HAA2H,GAC3H,4HAA4H,GAC5H,8HAA8H,GAC9H,2HAA2H,GAC3H,mIAAmI,GACnI,6GAA6G,GAC7G,0HAA0H,GAC1H,kIAAkI,GAClI,sHAAsH,GACtH,wHAAwH,GACxH,yHAAyH,GACzH,uHAAuH,GACvH,wGAAwG,GACxG,oGAAoG,GACpG,0HAA0H,GAC1H,gHAAgH,GAChH,wFAAwF,GACxF,qHAAqH,GACrH,qGAAqG,GACrG,oFAAoF,GACpF,2FAA2F,GAC3F,sHAAsH,GACtH,kHAAkH,GAClH,+FAA+F;;EAE/F;EACA,IAAIC,0BAA0B,GAC9B,0GAA0G,GAC1G,wGAAwG,GACxG,qHAAqH,GACrH,6HAA6H,GAC7H,8GAA8G,GAC9G,0HAA0H,GAC1H,6GAA6G,GAC7G,wHAAwH,GACxH,yHAAyH,GACzH,oIAAoI,GACpI,sEAAsE;;EAEtE;EACA,IAAIC,OAAO,GACXJ,mBAAmB,GACnBC,yBAAyB,GAAGC,yBAAyB,GACrDC,0BAA0B;;EAE1B;EACA,IAAIE,wBAAwB,GAC5B,mGAAmG;;EAEnG;EACA,IAAIC,yBAAyB,GAC7B,0FAA0F,GAC1F,4FAA4F,GAC5F,gGAAgG,GAChG,kGAAkG,GAClG,wGAAwG,GACxG,0FAA0F,GAC1F,yGAAyG,GACzG,gFAAgF,GAChF,qGAAqG,GACrG,0GAA0G,GAC1G,uGAAuG,GACvG,yGAAyG,GACzG,0FAA0F,GAC1F,gFAAgF,GAChF,0GAA0G,GAC1G,wGAAwG,GACxG,yGAAyG,GACzG,qFAAqF,GACrF,2GAA2G,GAC3G,kGAAkG,GAClG,qFAAqF,GACrF,gHAAgH,GAChH,kHAAkH,GAClH,kHAAkH,GAClH,uGAAuG,GACvG,2FAA2F,GAC3F,oGAAoG,GACpG,2GAA2G,GAC3G,mGAAmG,GACnG,2FAA2F,GAC3F,wGAAwG,GACxG,6GAA6G,GAC7G,6FAA6F,GAC7F,iFAAiF,GACjF,kGAAkG,GAClG,8FAA8F,GAC9F,8GAA8G,GAC9G,yEAAyE,GACzE,8FAA8F,GAC9F,0GAA0G,GAC1G,6FAA6F,GAC7F,kFAAkF,GAClF,4FAA4F,GAC5F,qGAAqG,GACrG,wGAAwG,GACxG,iDAAiD;;EAEjD;EACA,IAAIC,kCAAkC,GACtC,iHAAiH,GACjH,qGAAqG,GACrG,0HAA0H;;EAE1H;EACA,IAAIC,8BAA8B,GAClC,mGAAmG;;EAEnG;EACA,IAAIC,gCAAgC,GACpC,sGAAsG,GACtG,6HAA6H,GAC7H,wGAAwG,GACxG,sGAAsG,GACtG,yHAAyH,GACzH,sGAAsG,GACtG,wGAAwG;;EAExG;EACA,IAAIC,wBAAwB,GAC5B,0CAA0C;;EAE1C;EACA,IAAIC,kCAAkC,GACtC,oCAAoC;;EAEpC;EACA,IAAIC,qBAAqB,GACzB,0CAA0C;;EAE1C;EACA,IAAIC,8BAA8B,GAClC,yHAAyH;;EAEzH;EACA,IAAIC,qBAAqB,GACzB,0DAA0D;;EAE1D;EACA,IAAIC,6BAA6B,GACjC,iCAAiC;;EAEjC;EACA,IAAIC,6BAA6B,GACjC,0HAA0H,GAC1H,wHAAwH,GACxH,qHAAqH,GACrH,0GAA0G,GAC1G,kIAAkI,GAClI,sFAAsF;;EAEtF;EACA,IAAIC,uCAAuC,GAC3C,oHAAoH,GACpH,iIAAiI,GACjI,yIAAyI,GACzI,2IAA2I,GAC3I,0IAA0I,GAC1I,+HAA+H,GAC/H,2CAA2C;;EAE3C;EACA,IAAIC,uCAAuC,GAC3C,kHAAkH,GAClH,sIAAsI,GACtI,uHAAuH,GACvH,gHAAgH,GAChH,+GAA+G,GAC/G,qIAAqI,GACrI,6IAA6I,GAC7I,qIAAqI,GACrI,mIAAmI,GACnI,uGAAuG,GACvG,0HAA0H,GAC1H,mDAAmD;;EAEnD;EACA,IAAIC,qBAAqB,GACzB,0HAA0H,GAC1H,+GAA+G,GAC/G,kCAAkC;;EAElC;EACA,IAAIC,uBAAuB,GAC3B,6GAA6G;;EAE7G;EACA,IAAIC,4BAA4B,GAChC,4HAA4H,GAC5H,uCAAuC;;EAEvC;EACA,IAAIC,mBAAmB,GACvB,iIAAiI,GACjI,2CAA2C;;EAE3C;EACA;EACA;EACA,IAAIC,qBAAqB,GACzB,oIAAoI,GACpI,qIAAqI;;EAErI;EACA,IAAIC,8BAA8B,GAClC,+HAA+H,GAC/H,qEAAqE;;EAErE;EACA,IAAIC,8CAA8C,GAClD,+HAA+H,GAC/H,6GAA6G,GAC7G,sHAAsH,GACtH,0GAA0G,GAC1G,0HAA0H,GAC1H,6HAA6H,GAC7H,+HAA+H,GAC/H,4HAA4H,GAC5H,kHAAkH,GAClH,0HAA0H,GAC1H,+EAA+E,GAC/E,8GAA8G,GAC9G,6FAA6F,GAC7F,sGAAsG,GACtG,8GAA8G,GAC9G,wHAAwH,GACxH,+DAA+D;;EAE/D;EACA,IAAIC,0BAA0B,GAC9B,mIAAmI,GACnI,6HAA6H,GAC7H,mIAAmI,GACnI,oIAAoI,GACpI,uIAAuI,GACvI,gJAAgJ,GAChJ,sIAAsI,GACtI,oIAAoI,GACpI,iHAAiH;;EAEjH;EACA,IAAIC,KAAK,GACTtB,wBAAwB,GACxBC,yBAAyB,GACzBC,kCAAkC,GAClCC,8BAA8B,GAC9BC,gCAAgC,GAChCC,wBAAwB,GACxBC,kCAAkC,GAClCC,qBAAqB,GACrBC,8BAA8B,GAC9BC,qBAAqB,GACrBC,6BAA6B,GAC7BC,6BAA6B,GAC7BC,uCAAuC,GACvCC,uCAAuC,GACvCC,qBAAqB,GACrBC,uBAAuB,GACvBC,4BAA4B,GAC5BC,mBAAmB,GACnBC,qBAAqB,GACrBC,8BAA8B,GAC9BC,8CAA8C,GAC9CC,0BAA0B;;EAE1B;EACA,IAAIE,gBAAgB,GACpB,0HAA0H,GAC1H,yHAAyH,GACzH,yHAAyH,GACzH,uHAAuH,GACvH,mHAAmH,GACnH,sHAAsH,GACtH,sHAAsH,GACtH,gHAAgH,GAChH,2GAA2G,GAC3G,iHAAiH,GACjH,oHAAoH,GACpH,0HAA0H,GAC1H,gIAAgI,GAChI,iHAAiH,GACjH,4EAA4E,GAC5E,2GAA2G,GAC3G,kGAAkG,GAClG,yHAAyH,GACzH,+GAA+G,GAC/G,4GAA4G,GAC5G,0GAA0G,GAC1G,kHAAkH,GAClH,0HAA0H,GAC1H,oHAAoH,GACpH,uHAAuH,GACvH,gHAAgH,GAChH,8GAA8G,GAC9G,6GAA6G,GAC7G,8GAA8G,GAC9G,6FAA6F,GAC7F,2GAA2G,GAC3G,iHAAiH,GACjH,6GAA6G,GAC7G,uHAAuH,GACvH,mFAAmF;;EAEnF;EACA,IAAIC,uBAAuB,GAC3B,kHAAkH,GAClH,gDAAgD;;EAEhD;EACA,IAAIC,IAAI,GACRF,gBAAgB,GAChBC,uBAAuB;;EAEvB;EACA,IAAIE,OAAO,GAAG,+BAA+B;;EAE7C;EACA,IAAIC,OAAO,GAAGxC,IAAI,CAACyC,OAAO,CAACzC,IAAI,CAAC0C,WAAW,CAAC;;EAE5C;EACA,IAAIC,OAAO,GAAG;IACZC,SAAS,EAAE,QAAQ;IACnBC,KAAK,EAAE,OAAO;IAAEC,GAAG,EAAE,KAAK;IAC1BC,QAAQ,EAAE,CAAC;MAACF,KAAK,EAAE;IAAI,CAAC;EAC1B,CAAC;;EAED;EACA,IAAIG,IAAI,GAAG;IACTH,KAAK,EAAE,GAAG;IAAEC,GAAG,EAAE,GAAG;IAAEG,YAAY,EAAE,IAAI;IAAEC,UAAU,EAAE,IAAI;IAC1DH,QAAQ,EAAE,CACR;MACEH,SAAS,EAAE,QAAQ;MACnBC,KAAK,EAAE;IACT,CAAC;EAEL,CAAC;;EAED;EACA,IAAIM,QAAQ,GAAGnD,IAAI,CAACyC,OAAO,CAACzC,IAAI,CAACoD,mBAAmB,CAAC;;EAErD;EACA,IAAIC,IAAI,GAAG;IACTT,SAAS,EAAE,MAAM;IAEjBC,KAAK,EAAE,KAAK;IAAEC,GAAG,EAAE,GAAG;IACtBQ,QAAQ,EAAE;MACRC,QAAQ,EAAEtD,mBAAmB;MAC7B,cAAc,EAAEG,OAAO,GAAGG;IAC5B,CAAC;IACDwC,QAAQ,EAAE,CACRI,QAAQ;EAEZ,CAAC;;EAED;EACA,IAAIK,MAAM,GAAG;IACXZ,SAAS,EAAE,QAAQ;IACnBC,KAAK,EAAE,GAAG;IAAEC,GAAG,EAAE,KAAK;IAAEI,UAAU,EAAE;EACtC,CAAC;;EAED;EACA,IAAIO,QAAQ,GAAG;IACbb,SAAS,EAAE,UAAU;IACrBc,QAAQ,EAAE,CACR;MAACb,KAAK,EAAE,mBAAmB;MAAEC,GAAG,EAAE,KAAK;MAAEQ,QAAQ,EAAE;IAAmB,CAAC,EACvE;MAACT,KAAK,EAAE,6BAA6B;MAAES,QAAQ,EAAE;IAA6B,CAAC,CAChF;IACDP,QAAQ,EAAE,CACR;MACEF,KAAK,EAAE,KAAK;MAAEC,GAAG,EAAE,KAAK;MAAEa,UAAU,EAAG,IAAI;MAC3CZ,QAAQ,EAAE,CACR;QACEH,SAAS,EAAE,QAAQ;QACnBC,KAAK,EAAE5C,mBAAmB;QAAE6C,GAAG,EAAE,GAAG;QAAEI,UAAU,EAAE,IAAI;QAAEU,cAAc,EAAE,IAAI;QAC5EN,QAAQ,EAAE;UACRC,QAAQ,EAAEtD,mBAAmB;UAC7B4D,OAAO,EAAE,MAAM;UACfC,OAAO,EAAEvB;QACX,CAAC;QACDQ,QAAQ,EAAE,CACRP,OAAO,EACPG,OAAO,EACPK,IAAI;MAER,CAAC,EACDG,QAAQ;IAEZ,CAAC,EACDnD,IAAI,CAACyC,OAAO,CAACzC,IAAI,CAAC+D,UAAU,EAAE;MAAClB,KAAK,EAAE5C;IAAmB,CAAC,CAAC;EAE/D,CAAC;EAED,OAAO;IACL+D,IAAI,EAAE,eAAe;IACrBC,gBAAgB,EAAE,IAAI;IACtBX,QAAQ,EAAE;MACRC,QAAQ,EAAEtD,mBAAmB;MAC7B4D,OAAO,EAAEzD,OAAO;MAChB8D,QAAQ,EAAEtD,OAAO;MACjBuD,KAAK,EAAEhC,KAAK;MACZiC,IAAI,EAAE9B,IAAI;MACVwB,OAAO,EAAEvB;IACX,CAAC;IACDQ,QAAQ,EAAE,CACRM,IAAI,EACJI,QAAQ,EACRN,QAAQ,EACRK,MAAM,EACNhB,OAAO,EACPG,OAAO,EACPK,IAAI;EAER,CAAC;AACH;AAEAqB,MAAM,CAACC,OAAO,GAAGvE,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}