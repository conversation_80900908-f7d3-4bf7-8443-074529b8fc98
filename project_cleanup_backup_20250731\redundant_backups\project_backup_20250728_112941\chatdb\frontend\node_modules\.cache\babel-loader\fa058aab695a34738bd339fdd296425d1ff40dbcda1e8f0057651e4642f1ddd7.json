{"ast": null, "code": "import _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport { PresetColors } from '../theme/interface';\nconst inverseColors = PresetColors.map(color => `${color}-inverse`);\nexport const PresetStatusColorTypes = ['success', 'processing', 'error', 'default', 'warning'];\n/**\n * determine if the color keyword belongs to the `Ant Design` {@link PresetColors}.\n * @param color color to be judged\n * @param includeInverse whether to include reversed colors\n */\nexport function isPresetColor(color) {\n  let includeInverse = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : true;\n  if (includeInverse) {\n    return [].concat(_toConsumableArray(inverseColors), _toConsumableArray(PresetColors)).includes(color);\n  }\n  return PresetColors.includes(color);\n}\nexport function isPresetStatusColor(color) {\n  return PresetStatusColorTypes.includes(color);\n}", "map": {"version": 3, "names": ["_toConsumableArray", "PresetColors", "inverseColors", "map", "color", "PresetStatusColorTypes", "isPresetColor", "includeInverse", "arguments", "length", "undefined", "concat", "includes", "isPresetStatusColor"], "sources": ["C:/Users/<USER>/PycharmProjects/智能数据分析系统/chatdb/frontend/node_modules/antd/es/_util/colors.js"], "sourcesContent": ["import _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport { PresetColors } from '../theme/interface';\nconst inverseColors = PresetColors.map(color => `${color}-inverse`);\nexport const PresetStatusColorTypes = ['success', 'processing', 'error', 'default', 'warning'];\n/**\n * determine if the color keyword belongs to the `Ant Design` {@link PresetColors}.\n * @param color color to be judged\n * @param includeInverse whether to include reversed colors\n */\nexport function isPresetColor(color) {\n  let includeInverse = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : true;\n  if (includeInverse) {\n    return [].concat(_toConsumableArray(inverseColors), _toConsumableArray(PresetColors)).includes(color);\n  }\n  return PresetColors.includes(color);\n}\nexport function isPresetStatusColor(color) {\n  return PresetStatusColorTypes.includes(color);\n}"], "mappings": "AAAA,OAAOA,kBAAkB,MAAM,8CAA8C;AAC7E,SAASC,YAAY,QAAQ,oBAAoB;AACjD,MAAMC,aAAa,GAAGD,YAAY,CAACE,GAAG,CAACC,KAAK,IAAI,GAAGA,KAAK,UAAU,CAAC;AACnE,OAAO,MAAMC,sBAAsB,GAAG,CAAC,SAAS,EAAE,YAAY,EAAE,OAAO,EAAE,SAAS,EAAE,SAAS,CAAC;AAC9F;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,aAAaA,CAACF,KAAK,EAAE;EACnC,IAAIG,cAAc,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI;EAC7F,IAAID,cAAc,EAAE;IAClB,OAAO,EAAE,CAACI,MAAM,CAACX,kBAAkB,CAACE,aAAa,CAAC,EAAEF,kBAAkB,CAACC,YAAY,CAAC,CAAC,CAACW,QAAQ,CAACR,KAAK,CAAC;EACvG;EACA,OAAOH,YAAY,CAACW,QAAQ,CAACR,KAAK,CAAC;AACrC;AACA,OAAO,SAASS,mBAAmBA,CAACT,KAAK,EAAE;EACzC,OAAOC,sBAAsB,CAACO,QAAQ,CAACR,KAAK,CAAC;AAC/C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}