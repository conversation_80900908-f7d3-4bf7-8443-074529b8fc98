import logging
from sqlalchemy.orm import Session

from app import crud, schemas
from app.db.base import Base
from app.db.session import engine

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def init_db(db: Session) -> None:
    # Create tables
    Base.metadata.create_all(bind=engine)
    logger.info("Tables created")


def create_initial_data(db: Session) -> None:
    # Check if we already have connections
    connection = crud.db_connection.get_by_name(db, name="Sample SQLite Database")
    if not connection:
        from app.core.config import settings
        connection_in = schemas.DBConnectionCreate(
            name="Sample SQLite Database",
            db_type="sqlite",
            host="localhost",  # SQLite不需要host，但保留字段
            port=0,  # SQLite不需要port
            username="",  # SQLite不需要用户名
            password="",  # SQLite不需要密码
            database_name=settings.SQLITE_DB_PATH
        )
        connection = crud.db_connection.create(db=db, obj_in=connection_in)
        logger.info(f"Created sample connection: {connection.name}")


if __name__ == "__main__":
    from app.db.session import SessionLocal

    db = SessionLocal()
    try:
        init_db(db)
        create_initial_data(db)
    finally:
        db.close()
