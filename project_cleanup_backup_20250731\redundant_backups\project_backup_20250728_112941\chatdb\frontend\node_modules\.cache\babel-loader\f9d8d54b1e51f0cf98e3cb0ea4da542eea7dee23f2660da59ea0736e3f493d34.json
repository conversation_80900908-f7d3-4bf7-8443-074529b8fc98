{"ast": null, "code": "import formatDistance from \"./_lib/formatDistance/index.js\";\nimport formatLong from \"./_lib/formatLong/index.js\";\nimport formatRelative from \"./_lib/formatRelative/index.js\";\nimport localize from \"./_lib/localize/index.js\";\nimport match from \"./_lib/match/index.js\";\n/**\n * @type {Locale}\n * @category Locales\n * @summary Spanish locale.\n * @language Spanish\n * @iso-639-2 spa\n * <AUTHOR> [@juanangosto]{@link https://github.com/juanangosto}\n * <AUTHOR> [@guigrpa]{@link https://github.com/guigrpa}\n * <AUTHOR> [@fjaguero]{@link https://github.com/fjaguero}\n * <AUTHOR> [@harogaston]{@link https://github.com/harogaston}\n * <AUTHOR> [@YagoCarballo]{@link https://github.com/YagoCarballo}\n */\nvar locale = {\n  code: 'es',\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 1 /* Monday */,\n    firstWeekContainsDate: 1\n  }\n};\nexport default locale;", "map": {"version": 3, "names": ["formatDistance", "formatLong", "formatRelative", "localize", "match", "locale", "code", "options", "weekStartsOn", "firstWeekContainsDate"], "sources": ["C:/Users/<USER>/PycharmProjects/智能数据分析系统/chatdb/frontend/node_modules/date-fns/esm/locale/es/index.js"], "sourcesContent": ["import formatDistance from \"./_lib/formatDistance/index.js\";\nimport formatLong from \"./_lib/formatLong/index.js\";\nimport formatRelative from \"./_lib/formatRelative/index.js\";\nimport localize from \"./_lib/localize/index.js\";\nimport match from \"./_lib/match/index.js\";\n/**\n * @type {Locale}\n * @category Locales\n * @summary Spanish locale.\n * @language Spanish\n * @iso-639-2 spa\n * <AUTHOR> [@juanangosto]{@link https://github.com/juanangosto}\n * <AUTHOR> [@guigrpa]{@link https://github.com/guigrpa}\n * <AUTHOR> [@fjaguero]{@link https://github.com/fjaguero}\n * <AUTHOR> [@harogaston]{@link https://github.com/harogaston}\n * <AUTHOR> [@YagoCarballo]{@link https://github.com/YagoCarballo}\n */\nvar locale = {\n  code: 'es',\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 1 /* Monday */,\n    firstWeekContainsDate: 1\n  }\n};\nexport default locale;"], "mappings": "AAAA,OAAOA,cAAc,MAAM,gCAAgC;AAC3D,OAAOC,UAAU,MAAM,4BAA4B;AACnD,OAAOC,cAAc,MAAM,gCAAgC;AAC3D,OAAOC,QAAQ,MAAM,0BAA0B;AAC/C,OAAOC,KAAK,MAAM,uBAAuB;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,MAAM,GAAG;EACXC,IAAI,EAAE,IAAI;EACVN,cAAc,EAAEA,cAAc;EAC9BC,UAAU,EAAEA,UAAU;EACtBC,cAAc,EAAEA,cAAc;EAC9BC,QAAQ,EAAEA,QAAQ;EAClBC,KAAK,EAAEA,KAAK;EACZG,OAAO,EAAE;IACPC,YAAY,EAAE,CAAC,CAAC;IAChBC,qBAAqB,EAAE;EACzB;AACF,CAAC;AACD,eAAeJ,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}