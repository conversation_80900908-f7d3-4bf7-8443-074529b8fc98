{"ast": null, "code": "// Note: types exported from `index.d.ts`.\nexport { fromMarkdown } from './lib/index.js';", "map": {"version": 3, "names": ["fromMarkdown"], "sources": ["C:/Users/<USER>/PycharmProjects/智能数据分析系统/chatdb/frontend/node_modules/mdast-util-from-markdown/dev/index.js"], "sourcesContent": ["// Note: types exported from `index.d.ts`.\nexport {fromMarkdown} from './lib/index.js'\n"], "mappings": "AAAA;AACA,SAAQA,YAAY,QAAO,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}