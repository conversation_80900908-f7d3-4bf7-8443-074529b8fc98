#!/usr/bin/env python3
"""
测试SCHEMA_RETRIEVER的回退方案
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_fallback_schema():
    """测试回退方案的Schema检索"""
    print("🧪 测试回退方案Schema检索")
    print("=" * 60)
    
    try:
        from app.db.session import SessionLocal
        from app.services.text2sql_utils import get_value_mappings
        from app import crud
        
        # 测试参数
        connection_id = 1  # fin_data连接
        
        print(f"📋 测试参数:")
        print(f"  连接ID: {connection_id}")
        
        # 创建数据库会话
        db = SessionLocal()
        
        try:
            # 🔧 回退方案：简单可靠的直接查询
            print(f"\n🔄 执行回退方案：直接数据库查询")

            # 获取所有表
            tables = crud.schema_table.get_by_connection(db=db, connection_id=connection_id)
            tables_list = []
            columns_list = []

            for table in tables:
                tables_list.append({
                    "id": table.id,
                    "name": table.table_name,
                    "description": table.description or ""
                })

                # 获取表的所有列
                table_columns = crud.schema_column.get_by_table(db=db, table_id=table.id)
                for column in table_columns:
                    columns_list.append({
                        "id": column.id,
                        "name": column.column_name,
                        "type": column.data_type,
                        "description": column.description,
                        "is_primary_key": column.is_primary_key,
                        "is_foreign_key": column.is_foreign_key,
                        "table_id": table.id,
                        "table_name": table.table_name
                    })

            schema_context = {
                "tables": tables_list,
                "columns": columns_list,
                "relationships": []
            }

            print(f"  回退方案成功：{len(tables_list)} 表，{len(columns_list)} 字段")

            # 🔍 调试：检查schema_context内容
            print(f"\n📊 Schema context详情:")
            print(f"  Tables: {len(schema_context.get('tables', []))}")
            print(f"  Columns: {len(schema_context.get('columns', []))}")
            
            # 显示关键列信息
            key_columns = [col for col in schema_context.get('columns', []) if col.get('name') in ['accounting_unit_name', 'accounting_organization', 'account_full_name', 'account_name']]
            if key_columns:
                print(f"  ✅ 关键列:")
                for col in key_columns:
                    print(f"    ID:{col.get('id')} - {col.get('name')} ({col.get('table_name')})")
            else:
                print(f"  ❌ 未找到关键列")

            # 获取值映射
            print(f"\n🔗 获取值映射...")
            value_mappings = get_value_mappings(db, schema_context)

            print(f"  Value mappings: {len(value_mappings)} fields")
            for table_col, mappings in value_mappings.items():
                print(f"    {table_col}: {len(mappings)} mappings")
                # 显示前几个映射作为示例
                for i, (nl_term, db_value) in enumerate(mappings.items()):
                    if i < 2:  # 只显示前2个
                        print(f"      '{nl_term}' → '{db_value}'")

            # 生成映射字符串
            mappings_str = ""
            if value_mappings:
                mappings_str = "-- Value Mappings:\n"
                for column, mappings in value_mappings.items():
                    mappings_str += f"-- For {column}:\n"
                    for nl_term, db_value in mappings.items():
                        mappings_str += f"--   '{nl_term}' in natural language refers to '{db_value}' in the database\n"
                mappings_str += "\n"

                print(f"\n✅ 生成映射字符串长度: {len(mappings_str)}")
                
                # 显示映射字符串预览
                print(f"\n📄 映射字符串预览 (前500字符):")
                print(mappings_str[:500] + "..." if len(mappings_str) > 500 else mappings_str)
            else:
                print(f"\n❌ 无值映射，映射字符串为空")

            return len(value_mappings) > 0
            
        finally:
            db.close()
            
    except Exception as e:
        print(f"❌ 测试回退方案失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 测试SCHEMA_RETRIEVER回退方案")
    print("=" * 80)
    
    success = test_fallback_schema()
    
    print("\n" + "=" * 80)
    if success:
        print("✅ 回退方案测试成功！")
        print("\n💡 这证明了回退方案能够正确获取值映射。")
        print("现在SCHEMA_RETRIEVER应该能正常工作了。")
        print("\n🔄 请重启后端服务并测试前端功能。")
    else:
        print("❌ 回退方案测试失败！")
        print("\n🔧 需要进一步调试回退方案逻辑。")

if __name__ == "__main__":
    main()
