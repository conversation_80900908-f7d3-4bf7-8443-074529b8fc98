{"ast": null, "code": "import { useStyleRegister } from '@ant-design/cssinjs';\nimport { genIconStyle } from '../../style';\nimport useToken from '../useToken';\nconst useResetIconStyle = (iconPrefixCls, csp) => {\n  const [theme, token] = useToken();\n  // Generate style for icons\n  return useStyleRegister({\n    theme,\n    token,\n    hashId: '',\n    path: ['ant-design-icons', iconPrefixCls],\n    nonce: () => csp === null || csp === void 0 ? void 0 : csp.nonce,\n    layer: {\n      name: 'antd'\n    }\n  }, () => [genIconStyle(iconPrefixCls)]);\n};\nexport default useResetIconStyle;", "map": {"version": 3, "names": ["useStyleRegister", "genIconStyle", "useToken", "useResetIconStyle", "iconPrefixCls", "csp", "theme", "token", "hashId", "path", "nonce", "layer", "name"], "sources": ["C:/Users/<USER>/PycharmProjects/智能数据分析系统/chatdb/frontend/node_modules/antd/es/theme/util/useResetIconStyle.js"], "sourcesContent": ["import { useStyleRegister } from '@ant-design/cssinjs';\nimport { genIconStyle } from '../../style';\nimport useToken from '../useToken';\nconst useResetIconStyle = (iconPrefixCls, csp) => {\n  const [theme, token] = useToken();\n  // Generate style for icons\n  return useStyleRegister({\n    theme,\n    token,\n    hashId: '',\n    path: ['ant-design-icons', iconPrefixCls],\n    nonce: () => csp === null || csp === void 0 ? void 0 : csp.nonce,\n    layer: {\n      name: 'antd'\n    }\n  }, () => [genIconStyle(iconPrefixCls)]);\n};\nexport default useResetIconStyle;"], "mappings": "AAAA,SAASA,gBAAgB,QAAQ,qBAAqB;AACtD,SAASC,YAAY,QAAQ,aAAa;AAC1C,OAAOC,QAAQ,MAAM,aAAa;AAClC,MAAMC,iBAAiB,GAAGA,CAACC,aAAa,EAAEC,GAAG,KAAK;EAChD,MAAM,CAACC,KAAK,EAAEC,KAAK,CAAC,GAAGL,QAAQ,CAAC,CAAC;EACjC;EACA,OAAOF,gBAAgB,CAAC;IACtBM,KAAK;IACLC,KAAK;IACLC,MAAM,EAAE,EAAE;IACVC,IAAI,EAAE,CAAC,kBAAkB,EAAEL,aAAa,CAAC;IACzCM,KAAK,EAAEA,CAAA,KAAML,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,GAAG,CAACK,KAAK;IAChEC,KAAK,EAAE;MACLC,IAAI,EAAE;IACR;EACF,CAAC,EAAE,MAAM,CAACX,YAAY,CAACG,aAAa,CAAC,CAAC,CAAC;AACzC,CAAC;AACD,eAAeD,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}