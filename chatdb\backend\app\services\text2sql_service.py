"""
Text2SQL服务 - 重构后的版本
提供向后兼容的接口，内部使用新的工具模块
集成缓存机制提升性能
集成全面的日志监控系统
"""
from typing import Dict, Any
from sqlalchemy.orm import Session
import logging
import time

from app.models.db_connection import DBConnection
from app.schemas.query import QueryResponse
from app.services.db_service import execute_query
from app.services.text2sql_utils import (
    retrieve_relevant_schema, get_value_mappings, format_schema_for_prompt,
    process_sql_with_value_mappings, validate_sql, extract_sql_from_llm_response,
    get_financial_metadata, enhance_schema_with_metadata
)
from app.services.enhanced_prompt_service import EnhancedPromptService
from app.core.llms import model_client
from app.core.config import settings
from app.services.cache_service import cache_metadata, cache_query_result, cache_service
from app.utils.text2sql_logger import text2sql_logger
from app.utils.pipeline_monitor import pipeline_monitor

logger = logging.getLogger(__name__)


def detect_subject_query_intent(query: str) -> Dict[str, Any]:
    """
    检测查询中的科目意图，返回科目相关信息
    """
    import re
    from app.config.subject_keywords import get_subject_keywords

    # 从配置文件获取科目关键词映射
    subject_keywords = get_subject_keywords()

    detected_subjects = []
    for standard_name, keywords in subject_keywords.items():
        for keyword in keywords:
            if keyword in query:
                detected_subjects.append({
                    'standard_name': standard_name,
                    'keyword': keyword,
                    'condition': f"account_full_name LIKE '%{standard_name}%'"
                })
                break  # 找到一个就够了

    return {
        'has_subject_query': len(detected_subjects) > 0,
        'subjects': detected_subjects,
        'requires_subject_filter': len(detected_subjects) > 0
    }


def construct_prompt(query: str, schema_context: Dict[str, Any], value_mappings: Dict[str, Dict[str, str]], metadata: Dict[str, Any] = None) -> str:
    """
    为LLM构建增强上下文和指令的提示 - 支持元数据增强
    """
    # 检查是否启用增强提示
    if getattr(settings, 'ENABLE_ENHANCED_PROMPTS', True):
        try:
            # 使用增强提示服务
            enhanced_prompt_service = EnhancedPromptService()
            enhanced_prompt = enhanced_prompt_service.build_enhanced_prompt(query, schema_context)

            # 添加值映射信息
            if value_mappings:
                mappings_str = "\n### 🔄 值映射信息:\n"
                for column, mappings in value_mappings.items():
                    mappings_str += f"**{column}字段映射**:\n"
                    for nl_term, db_value in mappings.items():
                        mappings_str += f"- '{nl_term}' → '{db_value}'\n"
                enhanced_prompt += mappings_str

            # 检测科目查询意图并添加特殊提示
            subject_intent = detect_subject_query_intent(query)
            if subject_intent['has_subject_query']:
                subject_prompt = "\n### 🚨 科目查询特别提醒:\n"
                subject_prompt += "检测到您的查询涉及特定科目，请注意：\n"
                subject_prompt += "- financial_data表包含所有科目的数据，不仅仅是您要查询的科目\n"
                subject_prompt += "- 必须添加科目筛选条件，否则会返回所有科目的汇总数据\n"
                subject_prompt += "- 检测到的科目及对应条件：\n"

                for subject in subject_intent['subjects']:
                    subject_prompt += f"  * '{subject['keyword']}' → 必须添加条件: `{subject['condition']}`\n"

                subject_prompt += "\n⚠️ **强制要求**: 生成的SQL必须包含上述科目筛选条件！\n"
                enhanced_prompt += subject_prompt

            if getattr(settings, 'DEBUG_ENHANCED_PROMPTS', False):
                logger.info(f"使用增强提示，版本: {getattr(settings, 'ENHANCED_PROMPT_VERSION', 'v2.0')}")

            return enhanced_prompt

        except Exception as e:
            logger.warning(f"增强提示生成失败，回退到标准提示: {e}")
            # 回退到原有的提示构建逻辑

    # 原有的提示构建逻辑（作为回退方案）
    return construct_legacy_prompt(query, schema_context, value_mappings, metadata)


def construct_legacy_prompt(query: str, schema_context: Dict[str, Any], value_mappings: Dict[str, Dict[str, str]], metadata: Dict[str, Any] = None) -> str:
    """
    原有的提示构建逻辑（作为回退方案）
    """
    # 格式化表结构信息
    schema_str = format_schema_for_prompt(schema_context)

    # 如果有值映射，添加到提示中
    mappings_str = ""
    if value_mappings:
        mappings_str = "-- 值映射:\n"
        for column, mappings in value_mappings.items():
            mappings_str += f"-- 对于 {column}:\n"
            for nl_term, db_value in mappings.items():
                mappings_str += f"--   自然语言中的'{nl_term}'指数据库中的'{db_value}'\n"
        mappings_str += "\n"

    # 构建元数据增强部分
    metadata_enhancement = ""
    if metadata and metadata.get("has_metadata"):
        metadata_enhancement = build_metadata_enhancement_prompt(metadata)

    prompt = f"""
你是一名专业的SQL开发专家，专门将自然语言问题转换为精确的SQL查询。

### 数据库结构:
```sql
{schema_str}
{mappings_str}
```

{metadata_enhancement}

### 自然语言问题:
"{query}"

### 指令:
1. 分析问题并识别相关的表和列。
2. 考虑表之间的关系以确定必要的连接。
3. 如果问题提到的术语可能与实际数据库值不同（例如，"中石化" vs "中国石化"），使用提供的值映射或考虑使用LIKE操作符。
4. 生成回答问题的有效SQL查询。
5. 只使用结构中提供的表和列。
6. 如果需要，使用适当的聚合函数（COUNT、SUM、AVG等）。
7. 根据需要包含适当的GROUP BY、ORDER BY和LIMIT子句。
8. 如果查询与时间相关，适当处理日期/时间比较。
9. 简要解释你的推理。

### SQL查询:
"""

    return prompt


def build_metadata_enhancement_prompt(metadata: Dict[str, Any]) -> str:
    """构建元数据增强的prompt部分"""

    enhancement = "\n### 🎯 财务业务元数据 (重要!):\n\n"

    # 表描述
    if metadata.get("table_description"):
        table_desc = metadata["table_description"]
        enhancement += f"**表说明**: {table_desc['description']}\n"
        enhancement += f"**业务用途**: {table_desc['business_purpose']}\n\n"

    # 关键业务规则
    if metadata.get("meta_business_rules"):
        enhancement += "### ⚠️ 关键业务规则 (必须遵守!):\n\n"
        for rule in metadata["meta_business_rules"]:
            if rule["importance"] in ["CRITICAL", "HIGH"]:
                enhancement += f"**【{rule['importance']}】{rule['category']}**:\n"
                enhancement += f"- {rule['description']}\n"
                if rule["sql_example"]:
                    enhancement += f"- 示例: `{rule['sql_example']}`\n"
                enhancement += "\n"

    # 关键字段说明
    if metadata.get("meta_column_descriptions"):
        enhancement += "### 📋 关键字段说明:\n\n"

        # 重点显示金额字段
        amount_fields = []
        for col in metadata["meta_column_descriptions"]:
            col_name = col["column_name"]
            if "amount" in col_name or col_name == "balance":
                chinese_name = col["chinese_name"]
                ai_points = col["ai_understanding_points"]
                amount_fields.append(f"- **{col_name}** ({chinese_name}): {ai_points}")

        if amount_fields:
            enhancement += "**💰 金额字段 (关键)**:\n"
            for field in amount_fields:
                enhancement += f"{field}\n"
            enhancement += "\n"

    return enhancement


def call_llm_api(prompt: str) -> str:
    """
    调用LLM API使用model_client生成SQL
    """
    try:
        system_message = """
        你是一名专业的SQL开发专家，专门将自然语言问题转换为精确的SQL查询。
        你的专长包括:
        1. 理解复杂的数据库结构和关系
        2. 将自然语言意图转换为正确的SQL语法
        3. 处理连接、聚合和复杂的过滤条件
        4. 确保查询优化并遵循最佳实践
        
        ## 财务数据库专业知识（重要）：
        对于main.financial_data表的查询，必须严格遵循以下规则：
        
        ### 科目分类与字段使用规则：
        1. **资产负债类科目查询** (银行存款,应付账款,应收账款等)：
           - 必须使用 balance 字段进行金额汇总
           - 示例：SELECT SUM(CAST(balance AS REAL)) FROM financial_data WHERE account_code LIKE '1%'
        
        2. **收入类科目查询** (主营业务收入,其他业务收入)：
           - 必须使用 credit_amount 字段（当期）
           - 示例：SELECT SUM(credit_amount) FROM financial_data WHERE account_code LIKE '60%'
        
        3. **成本费用类科目查询** (主营业务成本,管理费用,财务费用,销售费用,其他业务成本等其他支出)：
           - 必须使用 debit_amount 字段（当期）
           - 示例：SELECT SUM(debit_amount) FROM financial_data WHERE account_code LIKE '66%'
        
        ### 重要提醒：
        - 错误使用字段将导致分析结果完全错误
        - balance字段为TEXT类型，需要CAST(balance AS REAL)转换
        - 根据根据科目名称找到对应科目编号，通过科目编号自动识别科目类别并选择正确字段

        始终生成遵循标准SQL语法的有效SQL。专注于准确性和精确性。
        """

        # 直接使用model_client以保持一致性，使用配置文件中的参数
        response = model_client.complete(
            prompt=prompt,
            system_prompt=system_message,
            temperature=settings.LLM_TEMPERATURE,  # 使用配置文件中的温度参数
            max_tokens=settings.LLM_MAX_TOKENS     # 使用配置文件中的最大token参数
        )

        # 确保返回字符串
        return response if isinstance(response, str) else response.content
    except Exception as e:
        raise Exception(f"调用LLM API时出错: {str(e)}")


@cache_query_result(ttl=1800)  # 缓存30分钟
def process_text2sql_query(db: Session, connection: DBConnection, natural_language_query: str) -> QueryResponse:
    """
    处理自然语言查询并转换为SQL
    集成缓存机制提升性能
    集成全面的流水线监控系统
    """
    start_time = time.time()

    # 🔍 开始流水线监控
    query_id = pipeline_monitor.log_pipeline_start(natural_language_query, connection.name)
    step_results = []

    try:
        logger.info(f"开始处理Text2SQL查询: {natural_language_query[:100]}...")

        # 步骤1: 检索相关表结构（使用缓存）
        schema_context = get_cached_schema_context(db, connection.id, natural_language_query)

        # 🔍 监控步骤1：Schema获取
        step1_result = pipeline_monitor.monitor_step1_schema_retrieval(
            query_id, connection.id, schema_context
        )
        step_results.append(step1_result)

        if step1_result['status'] == 'error':
            pipeline_monitor.log_pipeline_summary(query_id, step_results, time.time() - start_time, False)
            return QueryResponse(
                sql="",
                results=None,
                error=f"步骤1失败: {step1_result['error']}",
                context={"query_id": query_id, "step_results": step_results}
            )

        # 如果没有找到相关表结构，返回错误
        if not schema_context["tables"]:
            pipeline_monitor.log_pipeline_summary(query_id, step_results, time.time() - start_time, False)
            return QueryResponse(
                sql="",
                results=None,
                error="无法为此查询识别相关表。",
                context={"query_id": query_id, "step_results": step_results}
            )

        # 步骤2: 获取值映射（使用缓存）
        value_mappings = get_cached_value_mappings(db, schema_context)

        # 🔍 监控步骤2：字段映射获取
        step2_result = pipeline_monitor.monitor_step2_value_mappings(
            query_id, schema_context, value_mappings
        )
        step_results.append(step2_result)

        # 步骤3: 获取财务元数据（使用缓存）
        metadata = None
        if any(table.get("name") == "financial_data" for table in schema_context["tables"]):
            metadata = get_cached_financial_metadata("financial_data")

        # 🔍 监控步骤3：元数据获取
        step3_result = pipeline_monitor.monitor_step3_metadata_retrieval(query_id, metadata)
        step_results.append(step3_result)

        # 步骤4: Schema增强
        original_schema = schema_context.copy()
        if metadata and metadata.get("has_metadata"):
            # 使用元数据增强schema上下文
            schema_context = enhance_schema_with_metadata(schema_context, metadata)

        # 🔍 监控步骤4：Schema增强
        step4_result = pipeline_monitor.monitor_step4_schema_enhancement(
            query_id, original_schema, schema_context, metadata or {}
        )
        step_results.append(step4_result)

        # 步骤5: 构建增强提示
        prompt = construct_prompt(natural_language_query, schema_context, value_mappings, metadata)

        # 🔍 监控步骤5：提示构建
        step5_result = pipeline_monitor.monitor_step5_prompt_construction(
            query_id, prompt, value_mappings, metadata
        )
        step_results.append(step5_result)

        # 步骤6: 调用LLM API（考虑缓存相似查询）
        llm_start_time = time.time()
        llm_response = call_llm_api_with_cache(prompt, natural_language_query)
        llm_time = time.time() - llm_start_time

        # 🔍 监控步骤6：LLM生成
        step6_result = pipeline_monitor.monitor_step6_llm_generation(
            query_id, prompt, llm_response, llm_time
        )
        step_results.append(step6_result)

        # 如果LLM生成有问题，记录并返回错误
        if step6_result['status'] == 'error':
            pipeline_monitor.log_pipeline_summary(query_id, step_results, time.time() - start_time, False)
            return QueryResponse(
                sql="",
                results=None,
                error=f"步骤6失败: LLM生成了禁止的字段名 {step6_result.get('problem_fields_found', [])}",
                context={
                    "query_id": query_id,
                    "step_results": step_results,
                    "llm_response": llm_response,
                    "prompt": prompt
                }
            )

        # 从响应中提取SQL
        sql = extract_sql_from_llm_response(llm_response)

        # 步骤7: 使用值映射处理SQL
        processed_sql = process_sql_with_value_mappings(sql, value_mappings)

        # 🔍 监控步骤7：SQL后处理
        step7_result = pipeline_monitor.monitor_step7_sql_processing(
            query_id, sql, processed_sql, value_mappings
        )
        step_results.append(step7_result)

        # 验证SQL和字段名
        validation_error = validate_sql_with_schema(processed_sql, schema_context)
        if validation_error:
            pipeline_monitor.log_pipeline_summary(query_id, step_results, time.time() - start_time, False)
            return QueryResponse(
                sql=processed_sql,
                results=None,
                error=f"SQL验证失败: {validation_error}",
                context={
                    "query_id": query_id,
                    "step_results": step_results,
                    "schema_context": schema_context,
                    "metadata_enhanced": metadata.get("has_metadata", False) if metadata else False,
                    "prompt": prompt,
                    "llm_response": llm_response
                }
            )

        # 执行SQL
        try:
            results = execute_query(connection, processed_sql)

            # 🔍 记录流水线成功完成
            pipeline_monitor.log_pipeline_summary(query_id, step_results, time.time() - start_time, True)

            return QueryResponse(
                sql=processed_sql,
                results=results,
                error=None,
                context={
                    "query_id": query_id,
                    "step_results": step_results,
                    "schema_context": schema_context,
                    "metadata_enhanced": metadata.get("has_metadata", False) if metadata else False,
                    "business_rules_applied": len(metadata.get("meta_business_rules", [])) if metadata else 0,
                    "prompt": prompt,
                    "llm_response": llm_response
                }
            )
        except Exception as e:
            # 🔍 记录流水线失败
            pipeline_monitor.log_pipeline_summary(query_id, step_results, time.time() - start_time, False)

            return QueryResponse(
                sql=processed_sql,
                results=None,
                error=f"SQL执行失败: {str(e)}",
                context={
                    "query_id": query_id,
                    "step_results": step_results,
                    "schema_context": schema_context,
                    "prompt": prompt,
                    "llm_response": llm_response
                }
            )
    except Exception as e:
        # 🔍 记录整体错误和流水线失败
        pipeline_monitor.log_pipeline_summary(query_id, step_results, time.time() - start_time, False)

        return QueryResponse(
            sql="",
            results=None,
            error=f"处理查询时出错: {str(e)}",
            context={"query_id": query_id, "step_results": step_results}
        )
    finally:
        # 记录处理时间
        processing_time = time.time() - start_time
        logger.info(f"Text2SQL查询处理完成，耗时: {processing_time:.2f}秒")


# 缓存辅助函数
@cache_metadata(ttl=3600)  # 缓存1小时
def get_cached_schema_context(db: Session, connection_id: int, query: str) -> Dict[str, Any]:
    """获取缓存的表结构上下文"""
    return retrieve_relevant_schema(db, connection_id, query)


@cache_metadata(ttl=3600)  # 缓存1小时
def get_cached_value_mappings(db: Session, schema_context: Dict[str, Any]) -> Dict[str, Dict[str, str]]:
    """获取缓存的值映射"""
    return get_value_mappings(db, schema_context)


@cache_metadata(ttl=7200)  # 缓存2小时
def get_cached_financial_metadata(table_name: str) -> Dict[str, Any]:
    """获取缓存的财务元数据"""
    return get_financial_metadata(table_name)


def validate_sql_with_schema(sql: str, schema_context: Dict[str, Any]) -> str:
    """
    验证SQL中的字段名是否都存在于schema中
    返回错误信息，如果没有错误返回None
    """
    if not sql or not schema_context.get("columns"):
        return None

    # 获取schema中所有有效的字段名
    valid_columns = set()
    for col in schema_context["columns"]:
        valid_columns.add(col.get("column_name", ""))

    # 定义明确禁止的字段名
    forbidden_fields = {
        'company_id', 'company_name', 'date', 'sales_expense',
        'management_expenses', 'admin_expense', 'expense_amount'
    }

    # 简单的字段名提取（可以改进为更精确的SQL解析）
    import re

    # 提取SQL中可能的字段名
    # 匹配 SELECT 后的字段、WHERE 条件中的字段等
    field_patterns = [
        r'\b(\w+)\s*[=<>!]',  # WHERE条件中的字段
        r'\b(\w+)\s+BETWEEN',  # BETWEEN语句中的字段
        r'SELECT\s+.*?(\w+)',  # SELECT中的字段
        r'GROUP\s+BY\s+(\w+)',  # GROUP BY中的字段
        r'ORDER\s+BY\s+(\w+)',  # ORDER BY中的字段
        r'SUM\s*\(\s*CAST\s*\(\s*(\w+)\s*AS',  # SUM(CAST(field AS ...))
        r'SUM\s*\(\s*(\w+)\s*\)',  # 聚合函数中的字段
        r'COUNT\s*\(\s*(\w+)\s*\)',
        r'AVG\s*\(\s*(\w+)\s*\)',
        r'MAX\s*\(\s*(\w+)\s*\)',
        r'MIN\s*\(\s*(\w+)\s*\)',
        r'CAST\s*\(\s*(\w+)\s+AS',  # CAST中的字段
    ]

    found_fields = set()
    for pattern in field_patterns:
        matches = re.findall(pattern, sql, re.IGNORECASE)
        found_fields.update(matches)

    # 检查禁止的字段
    for field in found_fields:
        if field.lower() in [f.lower() for f in forbidden_fields]:
            return f"禁止使用字段 '{field}'。请检查字段映射指导。"

    # 检查字段是否在schema中存在
    for field in found_fields:
        if field not in valid_columns and field.lower() not in ['distinct', 'as', 'real', 'integer', 'text']:
            return f"字段 '{field}' 不存在于表结构中。可用字段: {', '.join(sorted(valid_columns))}"

    return None


def call_llm_api_with_cache(prompt: str, query: str) -> str:
    """
    带缓存的LLM API调用
    对于相似的查询，可以复用LLM响应
    """
    # 生成查询的特征哈希
    import hashlib
    query_hash = hashlib.md5(query.lower().encode()).hexdigest()
    cache_key = f"llm_response:{query_hash}"

    # 尝试从缓存获取
    cached_response = cache_service.get(cache_key)
    if cached_response:
        logger.info(f"LLM响应缓存命中: {query[:50]}...")
        return cached_response

    # 调用LLM API
    response = call_llm_api(prompt)

    # 缓存响应（较短的TTL，因为LLM响应可能需要更新）
    cache_service.set(cache_key, response, ttl=1800)  # 30分钟

    return response
