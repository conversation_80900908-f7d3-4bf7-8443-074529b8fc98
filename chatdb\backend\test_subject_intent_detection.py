#!/usr/bin/env python3
"""
测试科目意图检测功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_subject_intent_detection():
    """测试科目意图检测"""
    print("🧪 测试科目意图检测功能")
    print("=" * 60)
    
    try:
        from app.services.text2sql_service import detect_subject_query_intent
        
        # 测试用例
        test_queries = [
            "分析2024年11月各公司的管理费用",
            "查询各公司2024年的销售费用情况",
            "统计营业收入最高的公司",
            "2024年财务费用分析",
            "各部门的研发费用明细",
            "查询2024年各公司的总收入",  # 这个应该不会被检测为科目查询
            "分析公司的盈利情况"  # 这个也不应该被检测
        ]
        
        for i, query in enumerate(test_queries, 1):
            print(f"\n🔸 测试 {i}: {query}")
            
            intent = detect_subject_query_intent(query)
            
            print(f"  检测结果:")
            print(f"    有科目查询: {'✅' if intent['has_subject_query'] else '❌'}")
            print(f"    需要筛选: {'✅' if intent['requires_subject_filter'] else '❌'}")
            
            if intent['subjects']:
                print(f"    检测到的科目:")
                for subject in intent['subjects']:
                    print(f"      - 关键词: '{subject['keyword']}'")
                    print(f"        标准名称: {subject['standard_name']}")
                    print(f"        筛选条件: {subject['condition']}")
            else:
                print(f"    检测到的科目: 无")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试科目意图检测失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_enhanced_prompt_with_subject():
    """测试包含科目意图的增强提示"""
    print(f"\n🧪 测试科目查询的增强提示")
    print("=" * 60)
    
    try:
        from app.services.text2sql_service import construct_prompt
        
        # 模拟schema_context
        schema_context = {
            "tables": [{"name": "financial_data", "description": "财务数据表"}],
            "columns": [
                {"name": "accounting_unit_name", "table_name": "financial_data"},
                {"name": "account_full_name", "table_name": "financial_data"},
                {"name": "debit_amount", "table_name": "financial_data"},
                {"name": "year", "table_name": "financial_data"},
                {"name": "month", "table_name": "financial_data"}
            ]
        }
        
        # 模拟value_mappings
        value_mappings = {
            "financial_data.accounting_unit_name": {"公司": "accounting_unit_name"},
            "financial_data.account_full_name": {"科目名称": "account_full_name"}
        }
        
        # 测试管理费用查询
        query = "分析2024年11月各公司的管理费用"
        
        print(f"📋 测试查询: {query}")
        
        prompt = construct_prompt(query, schema_context, value_mappings, {})
        
        print(f"\n📄 生成的提示长度: {len(prompt)} 字符")
        
        # 检查关键内容
        has_subject_reminder = "科目查询特别提醒" in prompt
        has_filter_requirement = "必须添加科目筛选条件" in prompt
        has_management_expense = "管理费用" in prompt and "account_full_name LIKE" in prompt
        
        print(f"\n✅ 检查结果:")
        print(f"  包含科目提醒: {'✅' if has_subject_reminder else '❌'}")
        print(f"  包含筛选要求: {'✅' if has_filter_requirement else '❌'}")
        print(f"  包含管理费用条件: {'✅' if has_management_expense else '❌'}")
        
        if has_subject_reminder:
            # 显示科目提醒部分
            start = prompt.find("科目查询特别提醒")
            end = prompt.find("\n\n", start) if prompt.find("\n\n", start) > start else len(prompt)
            subject_section = prompt[start:end]
            print(f"\n📋 科目提醒内容:")
            print(subject_section)
        
        return has_subject_reminder and has_filter_requirement
        
    except Exception as e:
        print(f"❌ 测试增强提示失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 测试科目意图检测和增强提示")
    print("=" * 80)
    
    # 测试科目意图检测
    intent_success = test_subject_intent_detection()
    
    # 测试增强提示
    prompt_success = test_enhanced_prompt_with_subject()
    
    print("\n" + "=" * 80)
    print("📊 测试结果总结:")
    print(f"科目意图检测: {'✅ 成功' if intent_success else '❌ 失败'}")
    print(f"增强提示生成: {'✅ 成功' if prompt_success else '❌ 失败'}")
    
    if intent_success and prompt_success:
        print("\n🎉 所有测试通过！")
        print("💡 现在LLM应该能够:")
        print("1. 自动检测科目查询意图")
        print("2. 收到明确的科目筛选提醒")
        print("3. 生成包含正确筛选条件的SQL")
        print("\n🔄 请重启服务并测试前端功能")
    else:
        print("\n⚠️ 部分测试失败，需要进一步调试")

if __name__ == "__main__":
    main()
