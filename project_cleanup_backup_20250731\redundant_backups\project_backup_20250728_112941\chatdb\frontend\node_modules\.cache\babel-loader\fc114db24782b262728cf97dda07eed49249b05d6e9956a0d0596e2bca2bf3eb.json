{"ast": null, "code": "import addDays from \"../addDays/index.js\";\nimport differenceInCalendarDays from \"../differenceInCalendarDays/index.js\";\nimport isSameDay from \"../isSameDay/index.js\";\nimport isValid from \"../isValid/index.js\";\nimport isWeekend from \"../isWeekend/index.js\";\nimport toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\nimport toInteger from \"../_lib/toInteger/index.js\";\n/**\n * @name differenceInBusinessDays\n * @category Day Helpers\n * @summary Get the number of business days between the given dates.\n *\n * @description\n * Get the number of business day periods between the given dates.\n * Business days being days that arent in the weekend.\n * Like `differenceInCalendarDays`, the function removes the times from\n * the dates before calculating the difference.\n *\n * @param {Date|Number} dateLeft - the later date\n * @param {Date|Number} dateRight - the earlier date\n * @returns {Number} the number of business days\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // How many business days are between\n * // 10 January 2014 and 20 July 2014?\n * const result = differenceInBusinessDays(\n *   new Date(2014, 6, 20),\n *   new Date(2014, 0, 10)\n * )\n * //=> 136\n *\n * // How many business days are between\n * // 30 November 2021 and 1 November 2021?\n * const result = differenceInBusinessDays(\n *   new Date(2021, 10, 30),\n *   new Date(2021, 10, 1)\n * )\n * //=> 21\n *\n * // How many business days are between\n * // 1 November 2021 and 1 December 2021?\n * const result = differenceInBusinessDays(\n *   new Date(2021, 10, 1),\n *   new Date(2021, 11, 1)\n * )\n * //=> -22\n *\n * // How many business days are between\n * // 1 November 2021 and 1 November 2021 ?\n * const result = differenceInBusinessDays(\n *   new Date(2021, 10, 1),\n *   new Date(2021, 10, 1)\n * )\n * //=> 0\n */\nexport default function differenceInBusinessDays(dirtyDateLeft, dirtyDateRight) {\n  requiredArgs(2, arguments);\n  var dateLeft = toDate(dirtyDateLeft);\n  var dateRight = toDate(dirtyDateRight);\n  if (!isValid(dateLeft) || !isValid(dateRight)) return NaN;\n  var calendarDifference = differenceInCalendarDays(dateLeft, dateRight);\n  var sign = calendarDifference < 0 ? -1 : 1;\n  var weeks = toInteger(calendarDifference / 7);\n  var result = weeks * 5;\n  dateRight = addDays(dateRight, weeks * 7);\n\n  // the loop below will run at most 6 times to account for the remaining days that don't makeup a full week\n  while (!isSameDay(dateLeft, dateRight)) {\n    // sign is used to account for both negative and positive differences\n    result += isWeekend(dateRight) ? 0 : sign;\n    dateRight = addDays(dateRight, sign);\n  }\n  return result === 0 ? 0 : result;\n}", "map": {"version": 3, "names": ["addDays", "differenceInCalendarDays", "isSameDay", "<PERSON><PERSON><PERSON><PERSON>", "isWeekend", "toDate", "requiredArgs", "toInteger", "differenceInBusinessDays", "dirtyDateLeft", "dirtyDateRight", "arguments", "dateLeft", "dateRight", "NaN", "calendarDifference", "sign", "weeks", "result"], "sources": ["C:/Users/<USER>/PycharmProjects/智能数据分析系统/chatdb/frontend/node_modules/date-fns/esm/differenceInBusinessDays/index.js"], "sourcesContent": ["import addDays from \"../addDays/index.js\";\nimport differenceInCalendarDays from \"../differenceInCalendarDays/index.js\";\nimport isSameDay from \"../isSameDay/index.js\";\nimport isValid from \"../isValid/index.js\";\nimport isWeekend from \"../isWeekend/index.js\";\nimport toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\nimport toInteger from \"../_lib/toInteger/index.js\";\n/**\n * @name differenceInBusinessDays\n * @category Day Helpers\n * @summary Get the number of business days between the given dates.\n *\n * @description\n * Get the number of business day periods between the given dates.\n * Business days being days that arent in the weekend.\n * Like `differenceInCalendarDays`, the function removes the times from\n * the dates before calculating the difference.\n *\n * @param {Date|Number} dateLeft - the later date\n * @param {Date|Number} dateRight - the earlier date\n * @returns {Number} the number of business days\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // How many business days are between\n * // 10 January 2014 and 20 July 2014?\n * const result = differenceInBusinessDays(\n *   new Date(2014, 6, 20),\n *   new Date(2014, 0, 10)\n * )\n * //=> 136\n *\n * // How many business days are between\n * // 30 November 2021 and 1 November 2021?\n * const result = differenceInBusinessDays(\n *   new Date(2021, 10, 30),\n *   new Date(2021, 10, 1)\n * )\n * //=> 21\n *\n * // How many business days are between\n * // 1 November 2021 and 1 December 2021?\n * const result = differenceInBusinessDays(\n *   new Date(2021, 10, 1),\n *   new Date(2021, 11, 1)\n * )\n * //=> -22\n *\n * // How many business days are between\n * // 1 November 2021 and 1 November 2021 ?\n * const result = differenceInBusinessDays(\n *   new Date(2021, 10, 1),\n *   new Date(2021, 10, 1)\n * )\n * //=> 0\n */\nexport default function differenceInBusinessDays(dirtyDateLeft, dirtyDateRight) {\n  requiredArgs(2, arguments);\n  var dateLeft = toDate(dirtyDateLeft);\n  var dateRight = toDate(dirtyDateRight);\n  if (!isValid(dateLeft) || !isValid(dateRight)) return NaN;\n  var calendarDifference = differenceInCalendarDays(dateLeft, dateRight);\n  var sign = calendarDifference < 0 ? -1 : 1;\n  var weeks = toInteger(calendarDifference / 7);\n  var result = weeks * 5;\n  dateRight = addDays(dateRight, weeks * 7);\n\n  // the loop below will run at most 6 times to account for the remaining days that don't makeup a full week\n  while (!isSameDay(dateLeft, dateRight)) {\n    // sign is used to account for both negative and positive differences\n    result += isWeekend(dateRight) ? 0 : sign;\n    dateRight = addDays(dateRight, sign);\n  }\n  return result === 0 ? 0 : result;\n}"], "mappings": "AAAA,OAAOA,OAAO,MAAM,qBAAqB;AACzC,OAAOC,wBAAwB,MAAM,sCAAsC;AAC3E,OAAOC,SAAS,MAAM,uBAAuB;AAC7C,OAAOC,OAAO,MAAM,qBAAqB;AACzC,OAAOC,SAAS,MAAM,uBAAuB;AAC7C,OAAOC,MAAM,MAAM,oBAAoB;AACvC,OAAOC,YAAY,MAAM,+BAA+B;AACxD,OAAOC,SAAS,MAAM,4BAA4B;AAClD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,wBAAwBA,CAACC,aAAa,EAAEC,cAAc,EAAE;EAC9EJ,YAAY,CAAC,CAAC,EAAEK,SAAS,CAAC;EAC1B,IAAIC,QAAQ,GAAGP,MAAM,CAACI,aAAa,CAAC;EACpC,IAAII,SAAS,GAAGR,MAAM,CAACK,cAAc,CAAC;EACtC,IAAI,CAACP,OAAO,CAACS,QAAQ,CAAC,IAAI,CAACT,OAAO,CAACU,SAAS,CAAC,EAAE,OAAOC,GAAG;EACzD,IAAIC,kBAAkB,GAAGd,wBAAwB,CAACW,QAAQ,EAAEC,SAAS,CAAC;EACtE,IAAIG,IAAI,GAAGD,kBAAkB,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;EAC1C,IAAIE,KAAK,GAAGV,SAAS,CAACQ,kBAAkB,GAAG,CAAC,CAAC;EAC7C,IAAIG,MAAM,GAAGD,KAAK,GAAG,CAAC;EACtBJ,SAAS,GAAGb,OAAO,CAACa,SAAS,EAAEI,KAAK,GAAG,CAAC,CAAC;;EAEzC;EACA,OAAO,CAACf,SAAS,CAACU,QAAQ,EAAEC,SAAS,CAAC,EAAE;IACtC;IACAK,MAAM,IAAId,SAAS,CAACS,SAAS,CAAC,GAAG,CAAC,GAAGG,IAAI;IACzCH,SAAS,GAAGb,OAAO,CAACa,SAAS,EAAEG,IAAI,CAAC;EACtC;EACA,OAAOE,MAAM,KAAK,CAAC,GAAG,CAAC,GAAGA,MAAM;AAClC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}