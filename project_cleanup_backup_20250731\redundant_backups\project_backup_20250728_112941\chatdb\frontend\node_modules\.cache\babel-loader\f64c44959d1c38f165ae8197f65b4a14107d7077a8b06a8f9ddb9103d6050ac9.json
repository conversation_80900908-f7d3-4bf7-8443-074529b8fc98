{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport FontColorsOutlinedSvg from \"@ant-design/icons-svg/es/asn/FontColorsOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar FontColorsOutlined = function FontColorsOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: FontColorsOutlinedSvg\n  }));\n};\n\n/**![font-colors](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTkwNCA4MTZIMTIwYy00LjQgMC04IDMuNi04IDh2ODBjMCA0LjQgMy42IDggOCA4aDc4NGM0LjQgMCA4LTMuNiA4LTh2LTgwYzAtNC40LTMuNi04LTgtOHptLTY1MC4zLTgwaDg1YzQuMiAwIDgtMi43IDkuMy02LjhsNTMuNy0xNjZoMjE5LjJsNTMuMiAxNjZjMS4zIDQgNSA2LjggOS4zIDYuOGg4OS4xYzEuMSAwIDIuMi0uMiAzLjItLjVhOS43IDkuNyAwIDAwNi0xMi40TDU3My42IDExOC42YTkuOSA5LjkgMCAwMC05LjItNi42SDQ2Mi4xYy00LjIgMC03LjkgMi42LTkuMiA2LjZMMjQ0LjUgNzIzLjFjLS40IDEtLjUgMi4xLS41IDMuMi0uMSA1LjMgNC4zIDkuNyA5LjcgOS43em0yNTUuOS01MTYuMWg0LjFsODMuOCAyNjMuOEg0MjQuOWw4NC43LTI2My44eiIgLz48L3N2Zz4=) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(FontColorsOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'FontColorsOutlined';\n}\nexport default RefIcon;", "map": {"version": 3, "names": ["_extends", "React", "FontColorsOutlinedSvg", "AntdIcon", "FontColorsOutlined", "props", "ref", "createElement", "icon", "RefIcon", "forwardRef", "process", "env", "NODE_ENV", "displayName"], "sources": ["C:/Users/<USER>/PycharmProjects/智能数据分析系统/chatdb/frontend/node_modules/@ant-design/icons/es/icons/FontColorsOutlined.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport FontColorsOutlinedSvg from \"@ant-design/icons-svg/es/asn/FontColorsOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar FontColorsOutlined = function FontColorsOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: FontColorsOutlinedSvg\n  }));\n};\n\n/**![font-colors](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTkwNCA4MTZIMTIwYy00LjQgMC04IDMuNi04IDh2ODBjMCA0LjQgMy42IDggOCA4aDc4NGM0LjQgMCA4LTMuNiA4LTh2LTgwYzAtNC40LTMuNi04LTgtOHptLTY1MC4zLTgwaDg1YzQuMiAwIDgtMi43IDkuMy02LjhsNTMuNy0xNjZoMjE5LjJsNTMuMiAxNjZjMS4zIDQgNSA2LjggOS4zIDYuOGg4OS4xYzEuMSAwIDIuMi0uMiAzLjItLjVhOS43IDkuNyAwIDAwNi0xMi40TDU3My42IDExOC42YTkuOSA5LjkgMCAwMC05LjItNi42SDQ2Mi4xYy00LjIgMC03LjkgMi42LTkuMiA2LjZMMjQ0LjUgNzIzLjFjLS40IDEtLjUgMi4xLS41IDMuMi0uMSA1LjMgNC4zIDkuNyA5LjcgOS43em0yNTUuOS01MTYuMWg0LjFsODMuOCAyNjMuOEg0MjQuOWw4NC43LTI2My44eiIgLz48L3N2Zz4=) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(FontColorsOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'FontColorsOutlined';\n}\nexport default RefIcon;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD;AACA;;AAEA,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,qBAAqB,MAAM,iDAAiD;AACnF,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,IAAIC,kBAAkB,GAAG,SAASA,kBAAkBA,CAACC,KAAK,EAAEC,GAAG,EAAE;EAC/D,OAAO,aAAaL,KAAK,CAACM,aAAa,CAACJ,QAAQ,EAAEH,QAAQ,CAAC,CAAC,CAAC,EAAEK,KAAK,EAAE;IACpEC,GAAG,EAAEA,GAAG;IACRE,IAAI,EAAEN;EACR,CAAC,CAAC,CAAC;AACL,CAAC;;AAED;AACA,IAAIO,OAAO,GAAG,aAAaR,KAAK,CAACS,UAAU,CAACN,kBAAkB,CAAC;AAC/D,IAAIO,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCJ,OAAO,CAACK,WAAW,GAAG,oBAAoB;AAC5C;AACA,eAAeL,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}