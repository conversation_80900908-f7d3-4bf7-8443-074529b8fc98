from typing import Optional
from datetime import datetime
from pydantic import BaseModel, validator


# Shared properties
class DBConnectionBase(BaseModel):
    name: str
    db_type: str
    host: Optional[str] = None
    port: Optional[int] = None
    username: Optional[str] = None
    database_name: str

    @validator('host', pre=True, always=True)
    def set_host_default(cls, v, values):
        if values.get('db_type') == 'sqlite':
            return v or 'localhost'
        return v

    @validator('port', pre=True, always=True)
    def set_port_default(cls, v, values):
        if values.get('db_type') == 'sqlite':
            return v or 0
        return v

    @validator('username', pre=True, always=True)
    def set_username_default(cls, v, values):
        if values.get('db_type') == 'sqlite':
            return v or ''
        return v


# Properties to receive on connection creation
class DBConnectionCreate(DBConnectionBase):
    password: Optional[str] = None

    @validator('password', pre=True, always=True)
    def set_password_default(cls, v, values):
        if values.get('db_type') == 'sqlite':
            return v or ''
        return v


# Properties to receive on connection update
class DBConnectionUpdate(BaseModel):
    name: Optional[str] = None
    db_type: Optional[str] = None
    host: Optional[str] = None
    port: Optional[int] = None
    username: Optional[str] = None
    password: Optional[str] = None
    database_name: Optional[str] = None


# Properties shared by models stored in DB
class DBConnectionInDBBase(DBConnectionBase):
    id: int
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True


# Properties to return to client
class DBConnection(DBConnectionInDBBase):
    pass


# Properties stored in DB
class DBConnectionInDB(DBConnectionInDBBase):
    password_encrypted: str
