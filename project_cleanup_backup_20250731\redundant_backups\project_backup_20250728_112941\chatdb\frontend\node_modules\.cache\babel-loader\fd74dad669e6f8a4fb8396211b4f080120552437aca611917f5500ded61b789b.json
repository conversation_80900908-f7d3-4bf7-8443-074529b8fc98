{"ast": null, "code": "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { Popup } from 'rc-tooltip';\nimport { ConfigContext } from '../config-provider';\nimport useStyle from './style';\nimport { parseColor } from './util';\n/** @private Internal Component. Do not use in your production. */\nconst PurePanel = props => {\n  const {\n    prefixCls: customizePrefixCls,\n    className,\n    placement = 'top',\n    title,\n    color,\n    overlayInnerStyle\n  } = props;\n  const {\n    getPrefixCls\n  } = React.useContext(ConfigContext);\n  const prefixCls = getPrefixCls('tooltip', customizePrefixCls);\n  const [wrapCSSVar, hashId, cssVarCls] = useStyle(prefixCls);\n  // Color\n  const colorInfo = parseColor(prefixCls, color);\n  const arrowContentStyle = colorInfo.arrowStyle;\n  const formattedOverlayInnerStyle = Object.assign(Object.assign({}, overlayInnerStyle), colorInfo.overlayStyle);\n  const cls = classNames(hashId, cssVarCls, prefixCls, `${prefixCls}-pure`, `${prefixCls}-placement-${placement}`, className, colorInfo.className);\n  return wrapCSSVar(/*#__PURE__*/React.createElement(\"div\", {\n    className: cls,\n    style: arrowContentStyle\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: `${prefixCls}-arrow`\n  }), /*#__PURE__*/React.createElement(Popup, Object.assign({}, props, {\n    className: hashId,\n    prefixCls: prefixCls,\n    overlayInnerStyle: formattedOverlayInnerStyle\n  }), title)));\n};\nexport default PurePanel;", "map": {"version": 3, "names": ["React", "classNames", "Popup", "ConfigContext", "useStyle", "parseColor", "PurePanel", "props", "prefixCls", "customizePrefixCls", "className", "placement", "title", "color", "overlayInnerStyle", "getPrefixCls", "useContext", "wrapCSSVar", "hashId", "cssVarCls", "colorInfo", "arrowContentStyle", "arrowStyle", "formattedOverlayInnerStyle", "Object", "assign", "overlayStyle", "cls", "createElement", "style"], "sources": ["C:/Users/<USER>/PycharmProjects/智能数据分析系统/chatdb/frontend/node_modules/antd/es/tooltip/PurePanel.js"], "sourcesContent": ["\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { Popup } from 'rc-tooltip';\nimport { ConfigContext } from '../config-provider';\nimport useStyle from './style';\nimport { parseColor } from './util';\n/** @private Internal Component. Do not use in your production. */\nconst PurePanel = props => {\n  const {\n    prefixCls: customizePrefixCls,\n    className,\n    placement = 'top',\n    title,\n    color,\n    overlayInnerStyle\n  } = props;\n  const {\n    getPrefixCls\n  } = React.useContext(ConfigContext);\n  const prefixCls = getPrefixCls('tooltip', customizePrefixCls);\n  const [wrapCSSVar, hashId, cssVarCls] = useStyle(prefixCls);\n  // Color\n  const colorInfo = parseColor(prefixCls, color);\n  const arrowContentStyle = colorInfo.arrowStyle;\n  const formattedOverlayInnerStyle = Object.assign(Object.assign({}, overlayInnerStyle), colorInfo.overlayStyle);\n  const cls = classNames(hashId, cssVarCls, prefixCls, `${prefixCls}-pure`, `${prefixCls}-placement-${placement}`, className, colorInfo.className);\n  return wrapCSSVar(/*#__PURE__*/React.createElement(\"div\", {\n    className: cls,\n    style: arrowContentStyle\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: `${prefixCls}-arrow`\n  }), /*#__PURE__*/React.createElement(Popup, Object.assign({}, props, {\n    className: hashId,\n    prefixCls: prefixCls,\n    overlayInnerStyle: formattedOverlayInnerStyle\n  }), title)));\n};\nexport default PurePanel;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,SAASC,KAAK,QAAQ,YAAY;AAClC,SAASC,aAAa,QAAQ,oBAAoB;AAClD,OAAOC,QAAQ,MAAM,SAAS;AAC9B,SAASC,UAAU,QAAQ,QAAQ;AACnC;AACA,MAAMC,SAAS,GAAGC,KAAK,IAAI;EACzB,MAAM;IACJC,SAAS,EAAEC,kBAAkB;IAC7BC,SAAS;IACTC,SAAS,GAAG,KAAK;IACjBC,KAAK;IACLC,KAAK;IACLC;EACF,CAAC,GAAGP,KAAK;EACT,MAAM;IACJQ;EACF,CAAC,GAAGf,KAAK,CAACgB,UAAU,CAACb,aAAa,CAAC;EACnC,MAAMK,SAAS,GAAGO,YAAY,CAAC,SAAS,EAAEN,kBAAkB,CAAC;EAC7D,MAAM,CAACQ,UAAU,EAAEC,MAAM,EAAEC,SAAS,CAAC,GAAGf,QAAQ,CAACI,SAAS,CAAC;EAC3D;EACA,MAAMY,SAAS,GAAGf,UAAU,CAACG,SAAS,EAAEK,KAAK,CAAC;EAC9C,MAAMQ,iBAAiB,GAAGD,SAAS,CAACE,UAAU;EAC9C,MAAMC,0BAA0B,GAAGC,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEX,iBAAiB,CAAC,EAAEM,SAAS,CAACM,YAAY,CAAC;EAC9G,MAAMC,GAAG,GAAG1B,UAAU,CAACiB,MAAM,EAAEC,SAAS,EAAEX,SAAS,EAAE,GAAGA,SAAS,OAAO,EAAE,GAAGA,SAAS,cAAcG,SAAS,EAAE,EAAED,SAAS,EAAEU,SAAS,CAACV,SAAS,CAAC;EAChJ,OAAOO,UAAU,CAAC,aAAajB,KAAK,CAAC4B,aAAa,CAAC,KAAK,EAAE;IACxDlB,SAAS,EAAEiB,GAAG;IACdE,KAAK,EAAER;EACT,CAAC,EAAE,aAAarB,KAAK,CAAC4B,aAAa,CAAC,KAAK,EAAE;IACzClB,SAAS,EAAE,GAAGF,SAAS;EACzB,CAAC,CAAC,EAAE,aAAaR,KAAK,CAAC4B,aAAa,CAAC1B,KAAK,EAAEsB,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAElB,KAAK,EAAE;IACnEG,SAAS,EAAEQ,MAAM;IACjBV,SAAS,EAAEA,SAAS;IACpBM,iBAAiB,EAAES;EACrB,CAAC,CAAC,EAAEX,KAAK,CAAC,CAAC,CAAC;AACd,CAAC;AACD,eAAeN,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}