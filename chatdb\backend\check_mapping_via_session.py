#!/usr/bin/env python3
"""
通过SessionLocal检查字段映射状态
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def check_mapping_via_session():
    """通过SessionLocal检查映射状态"""
    print("🔍 通过SessionLocal检查映射状态")
    print("=" * 60)
    
    try:
        from app.db.session import SessionLocal
        from sqlalchemy import text
        
        db = SessionLocal()
        
        # 检查数据库连接
        db_url = str(db.bind.url)
        print(f"📁 数据库连接: {db_url}")
        
        # 检查表是否存在
        result = db.execute(text("SELECT name FROM sqlite_master WHERE type='table'"))
        tables = [row[0] for row in result.fetchall()]
        print(f"📋 数据库表: {tables}")
        
        # 检查关键表
        key_tables = ['valuemapping', 'schemacolumn', 'schematable', 'dbconnection']
        for table in key_tables:
            exists = table in tables
            status = "✅" if exists else "❌"
            print(f"  {table}: {status}")
        
        if 'valuemapping' in tables:
            # 检查valuemapping表的记录数
            result = db.execute(text("SELECT COUNT(*) FROM valuemapping"))
            count = result.fetchone()[0]
            print(f"\n📊 valuemapping表记录数: {count}")
            
            if count > 0:
                # 检查科目相关的映射
                result = db.execute(text("""
                    SELECT vm.nl_term, vm.db_value, sc.column_name
                    FROM valuemapping vm
                    JOIN schemacolumn sc ON vm.column_id = sc.id
                    WHERE sc.column_name IN ('account_full_name', 'account_name', 'account_code')
                    ORDER BY sc.column_name, vm.nl_term
                """))
                
                mappings = result.fetchall()
                
                print(f"\n📋 科目相关字段映射:")
                current_field = None
                for nl_term, db_value, col_name in mappings:
                    if col_name != current_field:
                        current_field = col_name
                        print(f"\n🔸 {col_name}:")
                    print(f"    '{nl_term}' → '{db_value}'")
                
                # 检查管理费用相关映射
                result = db.execute(text("""
                    SELECT vm.nl_term, vm.db_value, sc.column_name
                    FROM valuemapping vm
                    JOIN schemacolumn sc ON vm.column_id = sc.id
                    WHERE vm.nl_term LIKE '%管理费用%' OR vm.db_value LIKE '%管理费用%'
                """))
                
                mgmt_mappings = result.fetchall()
                
                print(f"\n📋 管理费用相关映射:")
                if mgmt_mappings:
                    for nl_term, db_value, col_name in mgmt_mappings:
                        print(f"    '{nl_term}' → '{db_value}' ({col_name})")
                else:
                    print(f"    ❌ 无管理费用相关映射")
                
                # 检查是否有1000这样的值
                result = db.execute(text("""
                    SELECT vm.nl_term, vm.db_value, sc.column_name
                    FROM valuemapping vm
                    JOIN schemacolumn sc ON vm.column_id = sc.id
                    WHERE vm.db_value = '1000'
                """))
                
                code_1000_mappings = result.fetchall()
                
                print(f"\n📋 值为'1000'的映射:")
                if code_1000_mappings:
                    for nl_term, db_value, col_name in code_1000_mappings:
                        print(f"    '{nl_term}' → '{db_value}' ({col_name})")
                        print(f"      ⚠️ 这可能是问题的根源！")
                else:
                    print(f"    ✅ 无'1000'值映射")
        
        db.close()
        
    except Exception as e:
        print(f"❌ 检查映射状态失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    print("🚀 通过SessionLocal检查映射状态")
    print("=" * 80)
    
    check_mapping_via_session()

if __name__ == "__main__":
    main()
