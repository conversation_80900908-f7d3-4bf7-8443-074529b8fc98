"""
Neo4j连接池优化服务
解决频繁创建连接的性能问题
修复异步处理和资源管理问题
"""
import asyncio
import logging
from typing import Dict, Any, Optional, List
from contextlib import asynccontextmanager
from neo4j import GraphDatabase, AsyncGraphDatabase
from neo4j.exceptions import ServiceUnavailable

from app.core.config import settings

logger = logging.getLogger(__name__)


class Neo4jConnectionPool:
    """Neo4j连接池管理器"""
    
    def __init__(self):
        self.driver = None
        self._initialized = False
        self._connection_config = {
            'max_connection_lifetime': 3600,  # 1小时
            'max_connection_pool_size': 50,   # 最大连接数
            'connection_acquisition_timeout': 60,  # 获取连接超时
            'keep_alive': True,
            'trust': 'TRUST_ALL_CERTIFICATES'  # 生产环境需要配置证书
        }
    
    async def initialize(self):
        """初始化连接池"""
        if self._initialized:
            return
            
        try:
            self.driver = AsyncGraphDatabase.driver(
                settings.NEO4J_URI,
                auth=(settings.NEO4J_USER, settings.NEO4J_PASSWORD),
                **self._connection_config
            )
            
            # 测试连接
            async with self.driver.session() as session:
                await session.run("RETURN 1")
            
            self._initialized = True
            logger.info("Neo4j连接池初始化成功")
            
        except Exception as e:
            logger.error(f"Neo4j连接池初始化失败: {str(e)}")
            raise
    
    @asynccontextmanager
    async def get_session(self):
        """获取数据库会话的上下文管理器 - 修复异步处理"""
        if not self._initialized:
            await self.initialize()

        session = None
        try:
            # 修复：使用异步会话创建
            session = self.driver.session()
            yield session
        except Exception as e:
            logger.error(f"Neo4j会话错误: {str(e)}")
            raise
        finally:
            if session:
                # 修复：正确的异步会话关闭
                await session.close()

    async def execute_read_query(self, query: str, parameters: Dict = None) -> List[Dict]:
        """执行只读查询 - 增强错误处理和超时控制"""
        import asyncio

        try:
            # 添加超时控制，防止长时间阻塞
            async with asyncio.timeout(30):  # 30秒超时
                async with self.get_session() as session:
                    result = await session.run(query, parameters or {})
                    data = await result.data()
                    logger.debug(f"Neo4j查询成功，返回{len(data)}条记录")
                    return data
        except asyncio.TimeoutError:
            logger.error(f"Neo4j查询超时: {query[:100]}...")
            raise Exception("Neo4j查询超时，请检查查询复杂度或网络连接")
        except Exception as e:
            logger.error(f"Neo4j查询失败: {str(e)}, 查询: {query[:100]}...")
            raise

    async def execute_write_query(self, query: str, parameters: Dict = None) -> List[Dict]:
        """执行写入查询 - 增强错误处理和超时控制"""
        import asyncio

        try:
            # 添加超时控制，防止长时间阻塞
            async with asyncio.timeout(30):  # 30秒超时
                async with self.get_session() as session:
                    result = await session.run(query, parameters or {})
                    data = await result.data()
                    logger.debug(f"Neo4j写入成功，影响{len(data)}条记录")
                    return data
        except asyncio.TimeoutError:
            logger.error(f"Neo4j写入超时: {query[:100]}...")
            raise Exception("Neo4j写入超时，请检查查询复杂度或网络连接")
        except Exception as e:
            logger.error(f"Neo4j写入失败: {str(e)}, 查询: {query[:100]}...")
            raise
    
    async def health_check(self) -> bool:
        """连接池健康检查"""
        try:
            if not self._initialized:
                return False

            # 执行简单查询测试连接
            async with asyncio.timeout(5):  # 5秒超时
                async with self.get_session() as session:
                    await session.run("RETURN 1 as test")
                    return True
        except Exception as e:
            logger.error(f"Neo4j连接池健康检查失败: {str(e)}")
            return False

    async def get_connection_stats(self) -> Dict[str, Any]:
        """获取连接池统计信息"""
        if not self.driver:
            return {"status": "not_initialized"}

        try:
            # 获取连接池统计信息（如果驱动支持）
            return {
                "status": "healthy" if self._initialized else "not_initialized",
                "max_pool_size": self._connection_config.get('max_connection_pool_size', 50),
                "connection_timeout": self._connection_config.get('connection_acquisition_timeout', 60),
                "initialized": self._initialized
            }
        except Exception as e:
            logger.error(f"获取连接池统计信息失败: {str(e)}")
            return {"status": "error", "error": str(e)}

    async def close(self):
        """关闭连接池"""
        if self.driver:
            try:
                await self.driver.close()
                self._initialized = False
                logger.info("Neo4j连接池已关闭")
            except Exception as e:
                logger.error(f"关闭Neo4j连接池时出错: {str(e)}")
                raise


# 全局连接池实例
neo4j_pool = Neo4jConnectionPool()


async def get_neo4j_pool() -> Neo4jConnectionPool:
    """获取Neo4j连接池实例"""
    if not neo4j_pool._initialized:
        await neo4j_pool.initialize()
    return neo4j_pool
