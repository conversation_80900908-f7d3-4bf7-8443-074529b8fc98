{"ast": null, "code": "\"use client\";\n\nimport React, { useContext } from 'react';\nimport { AppConfigContext } from '../app/context';\nimport ConfigProvider, { ConfigContext, globalConfig, warnContext } from '../config-provider';\nimport { getReactRender } from '../config-provider/UnstableContext';\nimport PurePanel from './PurePanel';\nimport useNotification, { useInternalNotification } from './useNotification';\nlet notification = null;\nlet act = callback => callback();\nlet taskQueue = [];\nlet defaultGlobalConfig = {};\nfunction getGlobalContext() {\n  const {\n    getContainer,\n    rtl,\n    maxCount,\n    top,\n    bottom,\n    showProgress,\n    pauseOnHover\n  } = defaultGlobalConfig;\n  const mergedContainer = (getContainer === null || getContainer === void 0 ? void 0 : getContainer()) || document.body;\n  return {\n    getContainer: () => mergedContainer,\n    rtl,\n    maxCount,\n    top,\n    bottom,\n    showProgress,\n    pauseOnHover\n  };\n}\nconst GlobalHolder = /*#__PURE__*/React.forwardRef((props, ref) => {\n  const {\n    notificationConfig,\n    sync\n  } = props;\n  const {\n    getPrefixCls\n  } = useContext(ConfigContext);\n  const prefixCls = defaultGlobalConfig.prefixCls || getPrefixCls('notification');\n  const appConfig = useContext(AppConfigContext);\n  const [api, holder] = useInternalNotification(Object.assign(Object.assign(Object.assign({}, notificationConfig), {\n    prefixCls\n  }), appConfig.notification));\n  React.useEffect(sync, []);\n  React.useImperativeHandle(ref, () => {\n    const instance = Object.assign({}, api);\n    Object.keys(instance).forEach(method => {\n      instance[method] = function () {\n        sync();\n        return api[method].apply(api, arguments);\n      };\n    });\n    return {\n      instance,\n      sync\n    };\n  });\n  return holder;\n});\nconst GlobalHolderWrapper = /*#__PURE__*/React.forwardRef((_, ref) => {\n  const [notificationConfig, setNotificationConfig] = React.useState(getGlobalContext);\n  const sync = () => {\n    setNotificationConfig(getGlobalContext);\n  };\n  React.useEffect(sync, []);\n  const global = globalConfig();\n  const rootPrefixCls = global.getRootPrefixCls();\n  const rootIconPrefixCls = global.getIconPrefixCls();\n  const theme = global.getTheme();\n  const dom = /*#__PURE__*/React.createElement(GlobalHolder, {\n    ref: ref,\n    sync: sync,\n    notificationConfig: notificationConfig\n  });\n  return /*#__PURE__*/React.createElement(ConfigProvider, {\n    prefixCls: rootPrefixCls,\n    iconPrefixCls: rootIconPrefixCls,\n    theme: theme\n  }, global.holderRender ? global.holderRender(dom) : dom);\n});\nfunction flushNotice() {\n  if (!notification) {\n    const holderFragment = document.createDocumentFragment();\n    const newNotification = {\n      fragment: holderFragment\n    };\n    notification = newNotification;\n    // Delay render to avoid sync issue\n    act(() => {\n      const reactRender = getReactRender();\n      reactRender(/*#__PURE__*/React.createElement(GlobalHolderWrapper, {\n        ref: node => {\n          const {\n            instance,\n            sync\n          } = node || {};\n          Promise.resolve().then(() => {\n            if (!newNotification.instance && instance) {\n              newNotification.instance = instance;\n              newNotification.sync = sync;\n              flushNotice();\n            }\n          });\n        }\n      }), holderFragment);\n    });\n    return;\n  }\n  // Notification not ready\n  if (!notification.instance) {\n    return;\n  }\n  // >>> Execute task\n  taskQueue.forEach(task => {\n    switch (task.type) {\n      case 'open':\n        {\n          act(() => {\n            notification.instance.open(Object.assign(Object.assign({}, defaultGlobalConfig), task.config));\n          });\n          break;\n        }\n      case 'destroy':\n        act(() => {\n          notification === null || notification === void 0 ? void 0 : notification.instance.destroy(task.key);\n        });\n        break;\n    }\n  });\n  // Clean up\n  taskQueue = [];\n}\n// ==============================================================================\n// ==                                  Export                                  ==\n// ==============================================================================\nfunction setNotificationGlobalConfig(config) {\n  defaultGlobalConfig = Object.assign(Object.assign({}, defaultGlobalConfig), config);\n  // Trigger sync for it\n  act(() => {\n    var _a;\n    (_a = notification === null || notification === void 0 ? void 0 : notification.sync) === null || _a === void 0 ? void 0 : _a.call(notification);\n  });\n}\nfunction open(config) {\n  const global = globalConfig();\n  if (process.env.NODE_ENV !== 'production' && !global.holderRender) {\n    warnContext('notification');\n  }\n  taskQueue.push({\n    type: 'open',\n    config\n  });\n  flushNotice();\n}\nconst destroy = key => {\n  taskQueue.push({\n    type: 'destroy',\n    key\n  });\n  flushNotice();\n};\nconst methods = ['success', 'info', 'warning', 'error'];\nconst baseStaticMethods = {\n  open,\n  destroy,\n  config: setNotificationGlobalConfig,\n  useNotification,\n  _InternalPanelDoNotUseOrYouWillBeFired: PurePanel\n};\nconst staticMethods = baseStaticMethods;\nmethods.forEach(type => {\n  staticMethods[type] = config => open(Object.assign(Object.assign({}, config), {\n    type\n  }));\n});\n// ==============================================================================\n// ==                                   Test                                   ==\n// ==============================================================================\nconst noop = () => {};\n/** @internal Only Work in test env */\n// eslint-disable-next-line import/no-mutable-exports\nexport let actWrapper = noop;\nif (process.env.NODE_ENV === 'test') {\n  actWrapper = wrapper => {\n    act = wrapper;\n  };\n}\n/** @internal Only Work in test env */\n// eslint-disable-next-line import/no-mutable-exports\nexport let actDestroy = noop;\nif (process.env.NODE_ENV === 'test') {\n  actDestroy = () => {\n    notification = null;\n  };\n}\nexport default staticMethods;", "map": {"version": 3, "names": ["React", "useContext", "AppConfigContext", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ConfigContext", "globalConfig", "warnContext", "getReactRender", "PurePanel", "useNotification", "useInternalNotification", "notification", "act", "callback", "taskQueue", "defaultGlobalConfig", "getGlobalContext", "getContainer", "rtl", "maxCount", "top", "bottom", "showProgress", "pauseOnHover", "mergedContainer", "document", "body", "GlobalHolder", "forwardRef", "props", "ref", "notificationConfig", "sync", "getPrefixCls", "prefixCls", "appConfig", "api", "holder", "Object", "assign", "useEffect", "useImperativeHandle", "instance", "keys", "for<PERSON>ach", "method", "apply", "arguments", "GlobalHolderWrapper", "_", "setNotificationConfig", "useState", "global", "rootPrefixCls", "getRootPrefixCls", "rootIconPrefixCls", "getIconPrefixCls", "theme", "getTheme", "dom", "createElement", "iconPrefixCls", "<PERSON><PERSON><PERSON>", "flushNotice", "holderFragment", "createDocumentFragment", "newNotification", "fragment", "reactRender", "node", "Promise", "resolve", "then", "task", "type", "open", "config", "destroy", "key", "setNotificationGlobalConfig", "_a", "call", "process", "env", "NODE_ENV", "push", "methods", "baseStaticMethods", "_InternalPanelDoNotUseOrYouWillBeFired", "staticMethods", "noop", "actWrapper", "wrapper", "actDestroy"], "sources": ["C:/Users/<USER>/PycharmProjects/智能数据分析系统/chatdb/frontend/node_modules/antd/es/notification/index.js"], "sourcesContent": ["\"use client\";\n\nimport React, { useContext } from 'react';\nimport { AppConfigContext } from '../app/context';\nimport ConfigProvider, { ConfigContext, globalConfig, warnContext } from '../config-provider';\nimport { getReactRender } from '../config-provider/UnstableContext';\nimport PurePanel from './PurePanel';\nimport useNotification, { useInternalNotification } from './useNotification';\nlet notification = null;\nlet act = callback => callback();\nlet taskQueue = [];\nlet defaultGlobalConfig = {};\nfunction getGlobalContext() {\n  const {\n    getContainer,\n    rtl,\n    maxCount,\n    top,\n    bottom,\n    showProgress,\n    pauseOnHover\n  } = defaultGlobalConfig;\n  const mergedContainer = (getContainer === null || getContainer === void 0 ? void 0 : getContainer()) || document.body;\n  return {\n    getContainer: () => mergedContainer,\n    rtl,\n    maxCount,\n    top,\n    bottom,\n    showProgress,\n    pauseOnHover\n  };\n}\nconst GlobalHolder = /*#__PURE__*/React.forwardRef((props, ref) => {\n  const {\n    notificationConfig,\n    sync\n  } = props;\n  const {\n    getPrefixCls\n  } = useContext(ConfigContext);\n  const prefixCls = defaultGlobalConfig.prefixCls || getPrefixCls('notification');\n  const appConfig = useContext(AppConfigContext);\n  const [api, holder] = useInternalNotification(Object.assign(Object.assign(Object.assign({}, notificationConfig), {\n    prefixCls\n  }), appConfig.notification));\n  React.useEffect(sync, []);\n  React.useImperativeHandle(ref, () => {\n    const instance = Object.assign({}, api);\n    Object.keys(instance).forEach(method => {\n      instance[method] = function () {\n        sync();\n        return api[method].apply(api, arguments);\n      };\n    });\n    return {\n      instance,\n      sync\n    };\n  });\n  return holder;\n});\nconst GlobalHolderWrapper = /*#__PURE__*/React.forwardRef((_, ref) => {\n  const [notificationConfig, setNotificationConfig] = React.useState(getGlobalContext);\n  const sync = () => {\n    setNotificationConfig(getGlobalContext);\n  };\n  React.useEffect(sync, []);\n  const global = globalConfig();\n  const rootPrefixCls = global.getRootPrefixCls();\n  const rootIconPrefixCls = global.getIconPrefixCls();\n  const theme = global.getTheme();\n  const dom = /*#__PURE__*/React.createElement(GlobalHolder, {\n    ref: ref,\n    sync: sync,\n    notificationConfig: notificationConfig\n  });\n  return /*#__PURE__*/React.createElement(ConfigProvider, {\n    prefixCls: rootPrefixCls,\n    iconPrefixCls: rootIconPrefixCls,\n    theme: theme\n  }, global.holderRender ? global.holderRender(dom) : dom);\n});\nfunction flushNotice() {\n  if (!notification) {\n    const holderFragment = document.createDocumentFragment();\n    const newNotification = {\n      fragment: holderFragment\n    };\n    notification = newNotification;\n    // Delay render to avoid sync issue\n    act(() => {\n      const reactRender = getReactRender();\n      reactRender(/*#__PURE__*/React.createElement(GlobalHolderWrapper, {\n        ref: node => {\n          const {\n            instance,\n            sync\n          } = node || {};\n          Promise.resolve().then(() => {\n            if (!newNotification.instance && instance) {\n              newNotification.instance = instance;\n              newNotification.sync = sync;\n              flushNotice();\n            }\n          });\n        }\n      }), holderFragment);\n    });\n    return;\n  }\n  // Notification not ready\n  if (!notification.instance) {\n    return;\n  }\n  // >>> Execute task\n  taskQueue.forEach(task => {\n    switch (task.type) {\n      case 'open':\n        {\n          act(() => {\n            notification.instance.open(Object.assign(Object.assign({}, defaultGlobalConfig), task.config));\n          });\n          break;\n        }\n      case 'destroy':\n        act(() => {\n          notification === null || notification === void 0 ? void 0 : notification.instance.destroy(task.key);\n        });\n        break;\n    }\n  });\n  // Clean up\n  taskQueue = [];\n}\n// ==============================================================================\n// ==                                  Export                                  ==\n// ==============================================================================\nfunction setNotificationGlobalConfig(config) {\n  defaultGlobalConfig = Object.assign(Object.assign({}, defaultGlobalConfig), config);\n  // Trigger sync for it\n  act(() => {\n    var _a;\n    (_a = notification === null || notification === void 0 ? void 0 : notification.sync) === null || _a === void 0 ? void 0 : _a.call(notification);\n  });\n}\nfunction open(config) {\n  const global = globalConfig();\n  if (process.env.NODE_ENV !== 'production' && !global.holderRender) {\n    warnContext('notification');\n  }\n  taskQueue.push({\n    type: 'open',\n    config\n  });\n  flushNotice();\n}\nconst destroy = key => {\n  taskQueue.push({\n    type: 'destroy',\n    key\n  });\n  flushNotice();\n};\nconst methods = ['success', 'info', 'warning', 'error'];\nconst baseStaticMethods = {\n  open,\n  destroy,\n  config: setNotificationGlobalConfig,\n  useNotification,\n  _InternalPanelDoNotUseOrYouWillBeFired: PurePanel\n};\nconst staticMethods = baseStaticMethods;\nmethods.forEach(type => {\n  staticMethods[type] = config => open(Object.assign(Object.assign({}, config), {\n    type\n  }));\n});\n// ==============================================================================\n// ==                                   Test                                   ==\n// ==============================================================================\nconst noop = () => {};\n/** @internal Only Work in test env */\n// eslint-disable-next-line import/no-mutable-exports\nexport let actWrapper = noop;\nif (process.env.NODE_ENV === 'test') {\n  actWrapper = wrapper => {\n    act = wrapper;\n  };\n}\n/** @internal Only Work in test env */\n// eslint-disable-next-line import/no-mutable-exports\nexport let actDestroy = noop;\nif (process.env.NODE_ENV === 'test') {\n  actDestroy = () => {\n    notification = null;\n  };\n}\nexport default staticMethods;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,KAAK,IAAIC,UAAU,QAAQ,OAAO;AACzC,SAASC,gBAAgB,QAAQ,gBAAgB;AACjD,OAAOC,cAAc,IAAIC,aAAa,EAAEC,YAAY,EAAEC,WAAW,QAAQ,oBAAoB;AAC7F,SAASC,cAAc,QAAQ,oCAAoC;AACnE,OAAOC,SAAS,MAAM,aAAa;AACnC,OAAOC,eAAe,IAAIC,uBAAuB,QAAQ,mBAAmB;AAC5E,IAAIC,YAAY,GAAG,IAAI;AACvB,IAAIC,GAAG,GAAGC,QAAQ,IAAIA,QAAQ,CAAC,CAAC;AAChC,IAAIC,SAAS,GAAG,EAAE;AAClB,IAAIC,mBAAmB,GAAG,CAAC,CAAC;AAC5B,SAASC,gBAAgBA,CAAA,EAAG;EAC1B,MAAM;IACJC,YAAY;IACZC,GAAG;IACHC,QAAQ;IACRC,GAAG;IACHC,MAAM;IACNC,YAAY;IACZC;EACF,CAAC,GAAGR,mBAAmB;EACvB,MAAMS,eAAe,GAAG,CAACP,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,YAAY,CAAC,CAAC,KAAKQ,QAAQ,CAACC,IAAI;EACrH,OAAO;IACLT,YAAY,EAAEA,CAAA,KAAMO,eAAe;IACnCN,GAAG;IACHC,QAAQ;IACRC,GAAG;IACHC,MAAM;IACNC,YAAY;IACZC;EACF,CAAC;AACH;AACA,MAAMI,YAAY,GAAG,aAAa3B,KAAK,CAAC4B,UAAU,CAAC,CAACC,KAAK,EAAEC,GAAG,KAAK;EACjE,MAAM;IACJC,kBAAkB;IAClBC;EACF,CAAC,GAAGH,KAAK;EACT,MAAM;IACJI;EACF,CAAC,GAAGhC,UAAU,CAACG,aAAa,CAAC;EAC7B,MAAM8B,SAAS,GAAGnB,mBAAmB,CAACmB,SAAS,IAAID,YAAY,CAAC,cAAc,CAAC;EAC/E,MAAME,SAAS,GAAGlC,UAAU,CAACC,gBAAgB,CAAC;EAC9C,MAAM,CAACkC,GAAG,EAAEC,MAAM,CAAC,GAAG3B,uBAAuB,CAAC4B,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAER,kBAAkB,CAAC,EAAE;IAC/GG;EACF,CAAC,CAAC,EAAEC,SAAS,CAACxB,YAAY,CAAC,CAAC;EAC5BX,KAAK,CAACwC,SAAS,CAACR,IAAI,EAAE,EAAE,CAAC;EACzBhC,KAAK,CAACyC,mBAAmB,CAACX,GAAG,EAAE,MAAM;IACnC,MAAMY,QAAQ,GAAGJ,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEH,GAAG,CAAC;IACvCE,MAAM,CAACK,IAAI,CAACD,QAAQ,CAAC,CAACE,OAAO,CAACC,MAAM,IAAI;MACtCH,QAAQ,CAACG,MAAM,CAAC,GAAG,YAAY;QAC7Bb,IAAI,CAAC,CAAC;QACN,OAAOI,GAAG,CAACS,MAAM,CAAC,CAACC,KAAK,CAACV,GAAG,EAAEW,SAAS,CAAC;MAC1C,CAAC;IACH,CAAC,CAAC;IACF,OAAO;MACLL,QAAQ;MACRV;IACF,CAAC;EACH,CAAC,CAAC;EACF,OAAOK,MAAM;AACf,CAAC,CAAC;AACF,MAAMW,mBAAmB,GAAG,aAAahD,KAAK,CAAC4B,UAAU,CAAC,CAACqB,CAAC,EAAEnB,GAAG,KAAK;EACpE,MAAM,CAACC,kBAAkB,EAAEmB,qBAAqB,CAAC,GAAGlD,KAAK,CAACmD,QAAQ,CAACnC,gBAAgB,CAAC;EACpF,MAAMgB,IAAI,GAAGA,CAAA,KAAM;IACjBkB,qBAAqB,CAAClC,gBAAgB,CAAC;EACzC,CAAC;EACDhB,KAAK,CAACwC,SAAS,CAACR,IAAI,EAAE,EAAE,CAAC;EACzB,MAAMoB,MAAM,GAAG/C,YAAY,CAAC,CAAC;EAC7B,MAAMgD,aAAa,GAAGD,MAAM,CAACE,gBAAgB,CAAC,CAAC;EAC/C,MAAMC,iBAAiB,GAAGH,MAAM,CAACI,gBAAgB,CAAC,CAAC;EACnD,MAAMC,KAAK,GAAGL,MAAM,CAACM,QAAQ,CAAC,CAAC;EAC/B,MAAMC,GAAG,GAAG,aAAa3D,KAAK,CAAC4D,aAAa,CAACjC,YAAY,EAAE;IACzDG,GAAG,EAAEA,GAAG;IACRE,IAAI,EAAEA,IAAI;IACVD,kBAAkB,EAAEA;EACtB,CAAC,CAAC;EACF,OAAO,aAAa/B,KAAK,CAAC4D,aAAa,CAACzD,cAAc,EAAE;IACtD+B,SAAS,EAAEmB,aAAa;IACxBQ,aAAa,EAAEN,iBAAiB;IAChCE,KAAK,EAAEA;EACT,CAAC,EAAEL,MAAM,CAACU,YAAY,GAAGV,MAAM,CAACU,YAAY,CAACH,GAAG,CAAC,GAAGA,GAAG,CAAC;AAC1D,CAAC,CAAC;AACF,SAASI,WAAWA,CAAA,EAAG;EACrB,IAAI,CAACpD,YAAY,EAAE;IACjB,MAAMqD,cAAc,GAAGvC,QAAQ,CAACwC,sBAAsB,CAAC,CAAC;IACxD,MAAMC,eAAe,GAAG;MACtBC,QAAQ,EAAEH;IACZ,CAAC;IACDrD,YAAY,GAAGuD,eAAe;IAC9B;IACAtD,GAAG,CAAC,MAAM;MACR,MAAMwD,WAAW,GAAG7D,cAAc,CAAC,CAAC;MACpC6D,WAAW,CAAC,aAAapE,KAAK,CAAC4D,aAAa,CAACZ,mBAAmB,EAAE;QAChElB,GAAG,EAAEuC,IAAI,IAAI;UACX,MAAM;YACJ3B,QAAQ;YACRV;UACF,CAAC,GAAGqC,IAAI,IAAI,CAAC,CAAC;UACdC,OAAO,CAACC,OAAO,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM;YAC3B,IAAI,CAACN,eAAe,CAACxB,QAAQ,IAAIA,QAAQ,EAAE;cACzCwB,eAAe,CAACxB,QAAQ,GAAGA,QAAQ;cACnCwB,eAAe,CAAClC,IAAI,GAAGA,IAAI;cAC3B+B,WAAW,CAAC,CAAC;YACf;UACF,CAAC,CAAC;QACJ;MACF,CAAC,CAAC,EAAEC,cAAc,CAAC;IACrB,CAAC,CAAC;IACF;EACF;EACA;EACA,IAAI,CAACrD,YAAY,CAAC+B,QAAQ,EAAE;IAC1B;EACF;EACA;EACA5B,SAAS,CAAC8B,OAAO,CAAC6B,IAAI,IAAI;IACxB,QAAQA,IAAI,CAACC,IAAI;MACf,KAAK,MAAM;QACT;UACE9D,GAAG,CAAC,MAAM;YACRD,YAAY,CAAC+B,QAAQ,CAACiC,IAAI,CAACrC,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAExB,mBAAmB,CAAC,EAAE0D,IAAI,CAACG,MAAM,CAAC,CAAC;UAChG,CAAC,CAAC;UACF;QACF;MACF,KAAK,SAAS;QACZhE,GAAG,CAAC,MAAM;UACRD,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,YAAY,CAAC+B,QAAQ,CAACmC,OAAO,CAACJ,IAAI,CAACK,GAAG,CAAC;QACrG,CAAC,CAAC;QACF;IACJ;EACF,CAAC,CAAC;EACF;EACAhE,SAAS,GAAG,EAAE;AAChB;AACA;AACA;AACA;AACA,SAASiE,2BAA2BA,CAACH,MAAM,EAAE;EAC3C7D,mBAAmB,GAAGuB,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAExB,mBAAmB,CAAC,EAAE6D,MAAM,CAAC;EACnF;EACAhE,GAAG,CAAC,MAAM;IACR,IAAIoE,EAAE;IACN,CAACA,EAAE,GAAGrE,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,YAAY,CAACqB,IAAI,MAAM,IAAI,IAAIgD,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACC,IAAI,CAACtE,YAAY,CAAC;EACjJ,CAAC,CAAC;AACJ;AACA,SAASgE,IAAIA,CAACC,MAAM,EAAE;EACpB,MAAMxB,MAAM,GAAG/C,YAAY,CAAC,CAAC;EAC7B,IAAI6E,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAI,CAAChC,MAAM,CAACU,YAAY,EAAE;IACjExD,WAAW,CAAC,cAAc,CAAC;EAC7B;EACAQ,SAAS,CAACuE,IAAI,CAAC;IACbX,IAAI,EAAE,MAAM;IACZE;EACF,CAAC,CAAC;EACFb,WAAW,CAAC,CAAC;AACf;AACA,MAAMc,OAAO,GAAGC,GAAG,IAAI;EACrBhE,SAAS,CAACuE,IAAI,CAAC;IACbX,IAAI,EAAE,SAAS;IACfI;EACF,CAAC,CAAC;EACFf,WAAW,CAAC,CAAC;AACf,CAAC;AACD,MAAMuB,OAAO,GAAG,CAAC,SAAS,EAAE,MAAM,EAAE,SAAS,EAAE,OAAO,CAAC;AACvD,MAAMC,iBAAiB,GAAG;EACxBZ,IAAI;EACJE,OAAO;EACPD,MAAM,EAAEG,2BAA2B;EACnCtE,eAAe;EACf+E,sCAAsC,EAAEhF;AAC1C,CAAC;AACD,MAAMiF,aAAa,GAAGF,iBAAiB;AACvCD,OAAO,CAAC1C,OAAO,CAAC8B,IAAI,IAAI;EACtBe,aAAa,CAACf,IAAI,CAAC,GAAGE,MAAM,IAAID,IAAI,CAACrC,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEqC,MAAM,CAAC,EAAE;IAC5EF;EACF,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AACF;AACA;AACA;AACA,MAAMgB,IAAI,GAAGA,CAAA,KAAM,CAAC,CAAC;AACrB;AACA;AACA,OAAO,IAAIC,UAAU,GAAGD,IAAI;AAC5B,IAAIR,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,MAAM,EAAE;EACnCO,UAAU,GAAGC,OAAO,IAAI;IACtBhF,GAAG,GAAGgF,OAAO;EACf,CAAC;AACH;AACA;AACA;AACA,OAAO,IAAIC,UAAU,GAAGH,IAAI;AAC5B,IAAIR,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,MAAM,EAAE;EACnCS,UAAU,GAAGA,CAAA,KAAM;IACjBlF,YAAY,GAAG,IAAI;EACrB,CAAC;AACH;AACA,eAAe8E,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}