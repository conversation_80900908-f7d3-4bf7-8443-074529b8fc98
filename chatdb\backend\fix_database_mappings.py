#!/usr/bin/env python3
"""
修复数据库中的字段映射数据
确保优先字段映射正确配置
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import sqlite3
try:
    from app.core.config import settings
except ImportError:
    # 如果无法导入settings，使用默认路径
    class Settings:
        SQLITE_DB_PATH = "chatdb.db"
    settings = Settings()

def get_field_id(cursor, field_name, table_name='financial_data', connection_name='fin_data'):
    """获取字段ID - 需要指定连接名称"""
    cursor.execute('''
        SELECT sc.id
        FROM schemacolumn sc
        JOIN schematable st ON sc.table_id = st.id
        JOIN dbconnection dc ON st.connection_id = dc.id
        WHERE sc.column_name = ? AND st.table_name = ? AND dc.name = ?
    ''', (field_name, table_name, connection_name))

    result = cursor.fetchone()
    return result[0] if result else None

def add_mapping(cursor, field_name, nl_term, db_value, table_name='financial_data', connection_name='fin_data'):
    """添加字段映射"""
    field_id = get_field_id(cursor, field_name, table_name, connection_name)
    if not field_id:
        print(f"❌ 字段 '{field_name}' 不存在于连接 '{connection_name}' 的表 '{table_name}' 中")
        return False
    
    # 检查映射是否已存在
    cursor.execute('''
        SELECT id FROM valuemapping 
        WHERE column_id = ? AND nl_term = ?
    ''', (field_id, nl_term))
    
    if cursor.fetchone():
        print(f"⚠️ 映射 '{nl_term}' -> {field_name} 已存在，跳过")
        return True
    
    # 添加新映射
    cursor.execute('''
        INSERT INTO valuemapping (column_id, nl_term, db_value, created_at)
        VALUES (?, ?, ?, datetime('now'))
    ''', (field_id, nl_term, db_value))
    
    print(f"✅ 成功添加映射: '{nl_term}' -> {field_name}")
    return True

def clear_conflicting_mappings(cursor):
    """清除冲突的映射"""
    print("🧹 清除可能冲突的映射...")

    # 获取可能冲突的映射
    conflicting_terms = [
        '公司', '企业', 'company', 'company_name', 'company_id',
        '科目名称', '会计科目名称', '科目全称', 'account_name', '科目'
    ]

    for term in conflicting_terms:
        cursor.execute('''
            SELECT vm.id, vm.nl_term, sc.column_name, st.table_name
            FROM valuemapping vm
            JOIN schemacolumn sc ON vm.column_id = sc.id
            JOIN schematable st ON sc.table_id = st.id
            JOIN dbconnection dc ON st.connection_id = dc.id
            WHERE vm.nl_term = ? AND st.table_name = 'financial_data' AND dc.name = 'fin_data'
        ''', (term,))

        existing_mappings = cursor.fetchall()
        if len(existing_mappings) > 1:
            print(f"⚠️ 发现术语 '{term}' 的冲突映射:")
            for mapping_id, nl_term, column_name, table_name in existing_mappings:
                print(f"   ID:{mapping_id} '{nl_term}' -> {column_name}")

            # 删除所有现有映射，稍后重新添加优先映射
            for mapping_id, _, _, _ in existing_mappings:
                cursor.execute('DELETE FROM valuemapping WHERE id = ?', (mapping_id,))
            print(f"🗑️ 已删除 {len(existing_mappings)} 个冲突映射")
        elif len(existing_mappings) == 1:
            print(f"📋 术语 '{term}' 已有映射: {existing_mappings[0][2]}")

def add_priority_mappings(cursor):
    """添加优先级映射"""
    print("\n📋 添加优先级字段映射...")
    
    # 优先级映射配置 - 只映射到优先字段
    priority_mappings = {
        # 公司相关 - 只映射到 accounting_unit_name
        'accounting_unit_name': [
            '公司', '公司名称', '企业', '单位', 
            'company', 'company_name', 'company_id'
        ],
        
        # 科目相关 - 只映射到 account_full_name  
        'account_full_name': [
            '科目名称', '会计科目名称', '科目全称', 
            'account_name', '科目'
        ],
        
        # 其他重要字段
        'year': ['年份', '年', 'year'],
        'month': ['月份', '月', 'month'],
        'debit_amount': ['借方金额', '借方', '支出', '费用金额', 'debit', '费用', '管理费用'],
        'credit_amount': ['贷方金额', '贷方', '收入', '收入金额', 'credit', '营业收入'],
        'account_code': ['科目代码', '会计科目代码', '科目编码', 'account_code'],
        'balance': ['余额', '结余', 'balance']
    }
    
    success_count = 0
    total_count = 0
    
    for field_name, terms in priority_mappings.items():
        print(f"\n🔸 配置字段: {field_name}")
        for term in terms:
            total_count += 1
            if add_mapping(cursor, field_name, term, field_name):
                success_count += 1
    
    print(f"\n📊 映射添加结果: {success_count}/{total_count} 成功")
    return success_count, total_count

def verify_mappings(cursor):
    """验证映射配置"""
    print("\n🔍 验证映射配置...")

    # 检查关键映射
    key_checks = [
        ('公司', 'accounting_unit_name'),
        ('企业', 'accounting_unit_name'),
        ('科目名称', 'account_full_name'),
        ('会计科目名称', 'account_full_name'),
        ('account_name', 'account_full_name')  # 这个很重要！
    ]

    print("📋 关键映射验证:")
    all_correct = True

    for nl_term, expected_field in key_checks:
        cursor.execute('''
            SELECT sc.column_name
            FROM valuemapping vm
            JOIN schemacolumn sc ON vm.column_id = sc.id
            JOIN schematable st ON sc.table_id = st.id
            JOIN dbconnection dc ON st.connection_id = dc.id
            WHERE vm.nl_term = ? AND st.table_name = 'financial_data' AND dc.name = 'fin_data'
        ''', (nl_term,))

        result = cursor.fetchone()
        if result:
            actual_field = result[0]
            if actual_field == expected_field:
                print(f"  ✅ '{nl_term}' → {actual_field}")
            else:
                print(f"  ❌ '{nl_term}' → {actual_field} (期望: {expected_field})")
                all_correct = False
        else:
            print(f"  ❌ '{nl_term}' → 未找到映射")
            all_correct = False

    return all_correct

def main():
    """主函数"""
    print("🚀 修复数据库字段映射")
    print("=" * 60)
    
    try:
        # 连接到resource.db数据库
        db_path = getattr(settings, 'SQLITE_DB_PATH', 'resource.db')

        # 如果路径不是绝对路径，尝试在项目根目录查找
        if not os.path.isabs(db_path):
            project_root = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
            db_path = os.path.join(project_root, db_path)

        print(f"📁 连接到resource.db数据库: {db_path}")

        if not os.path.exists(db_path):
            print(f"❌ 数据库文件不存在: {db_path}")
            print("请确保resource.db文件存在于正确位置")
            return

        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查必要的表是否存在
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='valuemapping'")
        if not cursor.fetchone():
            print("❌ valuemapping表不存在，请先运行数据库初始化")
            return
        
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='schemacolumn'")
        if not cursor.fetchone():
            print("❌ schemacolumn表不存在，请先运行数据库初始化")
            return
        
        # 执行修复步骤
        print("\n🔧 开始修复过程...")
        
        # 1. 清除冲突映射
        clear_conflicting_mappings(cursor)
        
        # 2. 添加优先级映射
        success_count, total_count = add_priority_mappings(cursor)
        
        # 3. 提交更改
        conn.commit()
        print(f"\n💾 已提交 {success_count} 个映射到数据库")
        
        # 4. 验证映射
        if verify_mappings(cursor):
            print("\n✅ 所有关键映射验证通过！")
        else:
            print("\n⚠️ 部分映射验证失败，请检查")
        
        conn.close()
        
        print("\n" + "=" * 60)
        print("🎉 数据库字段映射修复完成！")
        print("\n📝 下一步:")
        print("1. 重启后端服务以清除缓存")
        print("2. 测试智能查询功能")
        print("3. 验证是否使用了正确的优先字段")
        
    except Exception as e:
        print(f"❌ 修复过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
