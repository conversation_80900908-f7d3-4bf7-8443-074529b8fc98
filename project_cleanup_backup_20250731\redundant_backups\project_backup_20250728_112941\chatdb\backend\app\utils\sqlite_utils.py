"""
SQLite兼容性工具函数
提供MySQL到SQLite的SQL语法转换和兼容性处理
"""

import re
from typing import Dict, List, Any


def convert_mysql_to_sqlite_sql(sql: str) -> str:
    """
    将MySQL特有的SQL语法转换为SQLite兼容的语法
    
    Args:
        sql (str): MySQL SQL语句
        
    Returns:
        str: SQLite兼容的SQL语句
    """
    # 转换为小写进行匹配，但保持原始大小写
    sql_lower = sql.lower()

    # 1. 转换复杂的日期函数（必须在简单函数之前）
    # DATE_SUB(CURDATE(), INTERVAL n DAY) -> date('now', '-n days')
    sql = re.sub(
        r'\bDATE_SUB\s*\(\s*CURDATE\s*\(\s*\)\s*,\s*INTERVAL\s+(\d+)\s+DAY\s*\)',
        r"date('now', '-\1 days')",
        sql,
        flags=re.IGNORECASE
    )

    # DATE_ADD(CURDATE(), INTERVAL n DAY) -> date('now', '+n days')
    sql = re.sub(
        r'\bDATE_ADD\s*\(\s*CURDATE\s*\(\s*\)\s*,\s*INTERVAL\s+(\d+)\s+DAY\s*\)',
        r"date('now', '+\1 days')",
        sql,
        flags=re.IGNORECASE
    )

    # 2. 转换简单的日期函数
    # CURDATE() -> date('now')
    sql = re.sub(r'\bCURDATE\s*\(\s*\)', "date('now')", sql, flags=re.IGNORECASE)

    # NOW() -> datetime('now')
    sql = re.sub(r'\bNOW\s*\(\s*\)', "datetime('now')", sql, flags=re.IGNORECASE)
    
    # 3. 转换LIMIT语法 (SQLite已支持，无需转换)

    # 4. 转换字符串函数
    # CONCAT() -> ||
    sql = re.sub(
        r'\bCONCAT\s*\(\s*([^)]+)\s*\)',
        lambda m: ' || '.join([arg.strip() for arg in m.group(1).split(',')]),
        sql,
        flags=re.IGNORECASE
    )
    
    # 5. 转换AUTO_INCREMENT (在CREATE TABLE中)
    sql = re.sub(r'\bAUTO_INCREMENT\b', 'AUTOINCREMENT', sql, flags=re.IGNORECASE)

    # 6. 移除ENGINE=InnoDB等MySQL特有的表选项
    sql = re.sub(r'\s+ENGINE\s*=\s*\w+', '', sql, flags=re.IGNORECASE)
    sql = re.sub(r'\s+DEFAULT\s+CHARSET\s*=\s*\w+', '', sql, flags=re.IGNORECASE)
    sql = re.sub(r'\s+COLLATE\s*=\s*\w+', '', sql, flags=re.IGNORECASE)
    
    # 7. 转换数据类型
    # TINYINT -> INTEGER
    sql = re.sub(r'\bTINYINT\b', 'INTEGER', sql, flags=re.IGNORECASE)
    # MEDIUMINT -> INTEGER
    sql = re.sub(r'\bMEDIUMINT\b', 'INTEGER', sql, flags=re.IGNORECASE)
    # BIGINT -> INTEGER
    sql = re.sub(r'\bBIGINT\b', 'INTEGER', sql, flags=re.IGNORECASE)
    # DOUBLE -> REAL
    sql = re.sub(r'\bDOUBLE\b', 'REAL', sql, flags=re.IGNORECASE)
    # FLOAT -> REAL
    sql = re.sub(r'\bFLOAT\b', 'REAL', sql, flags=re.IGNORECASE)
    # DATETIME -> TEXT (SQLite推荐用TEXT存储日期时间，但要避免影响datetime()函数)
    sql = re.sub(r'\bDATETIME\b(?!\s*\()', 'TEXT', sql, flags=re.IGNORECASE)
    # TIMESTAMP -> TEXT
    sql = re.sub(r'\bTIMESTAMP\b', 'TEXT', sql, flags=re.IGNORECASE)
    
    return sql


def get_sqlite_data_type_mapping() -> Dict[str, str]:
    """
    获取MySQL到SQLite的数据类型映射
    
    Returns:
        Dict[str, str]: 数据类型映射字典
    """
    return {
        # 整数类型
        'TINYINT': 'INTEGER',
        'SMALLINT': 'INTEGER',
        'MEDIUMINT': 'INTEGER',
        'INT': 'INTEGER',
        'INTEGER': 'INTEGER',
        'BIGINT': 'INTEGER',
        
        # 浮点类型
        'FLOAT': 'REAL',
        'DOUBLE': 'REAL',
        'DECIMAL': 'REAL',
        'NUMERIC': 'REAL',
        
        # 字符串类型
        'CHAR': 'TEXT',
        'VARCHAR': 'TEXT',
        'TINYTEXT': 'TEXT',
        'TEXT': 'TEXT',
        'MEDIUMTEXT': 'TEXT',
        'LONGTEXT': 'TEXT',
        
        # 日期时间类型
        'DATE': 'TEXT',
        'TIME': 'TEXT',
        'DATETIME': 'TEXT',
        'TIMESTAMP': 'TEXT',
        'YEAR': 'INTEGER',
        
        # 二进制类型
        'BINARY': 'BLOB',
        'VARBINARY': 'BLOB',
        'TINYBLOB': 'BLOB',
        'BLOB': 'BLOB',
        'MEDIUMBLOB': 'BLOB',
        'LONGBLOB': 'BLOB',
        
        # 布尔类型
        'BOOLEAN': 'INTEGER',
        'BOOL': 'INTEGER',
        
        # JSON类型 (SQLite 3.38+支持JSON，否则用TEXT)
        'JSON': 'TEXT',
    }


def convert_mysql_data_type(mysql_type: str) -> str:
    """
    转换MySQL数据类型为SQLite兼容类型
    
    Args:
        mysql_type (str): MySQL数据类型
        
    Returns:
        str: SQLite兼容的数据类型
    """
    # 提取基本类型名（去除长度限制等）
    base_type = re.match(r'^(\w+)', mysql_type.upper())
    if base_type:
        base_type = base_type.group(1)
        mapping = get_sqlite_data_type_mapping()
        return mapping.get(base_type, 'TEXT')  # 默认使用TEXT
    return 'TEXT'


def get_sqlite_date_functions() -> Dict[str, str]:
    """
    获取SQLite日期函数映射
    
    Returns:
        Dict[str, str]: 日期函数映射
    """
    return {
        'CURDATE()': "date('now')",
        'CURRENT_DATE()': "date('now')",
        'NOW()': "datetime('now')",
        'CURRENT_TIMESTAMP()': "datetime('now')",
        'UNIX_TIMESTAMP()': "strftime('%s', 'now')",
    }


def validate_sqlite_sql(sql: str) -> tuple[bool, str]:
    """
    验证SQL是否与SQLite兼容
    
    Args:
        sql (str): SQL语句
        
    Returns:
        tuple[bool, str]: (是否兼容, 错误信息)
    """
    # 检查不支持的MySQL特性
    unsupported_patterns = [
        (r'\bFULLTEXT\b', 'SQLite不支持FULLTEXT索引'),
        (r'\bENGINE\s*=', 'SQLite不支持ENGINE选项'),
        (r'\bPARTITION\s+BY\b', 'SQLite不支持表分区'),
        (r'\bSTORED\s+PROCEDURE\b', 'SQLite不支持存储过程'),
        (r'\bTRIGGER\s+.*\bFOR\s+EACH\s+ROW\b', '需要检查触发器语法'),
    ]
    
    for pattern, message in unsupported_patterns:
        if re.search(pattern, sql, re.IGNORECASE):
            return False, message
    
    return True, ""


def optimize_sqlite_query(sql: str) -> str:
    """
    优化SQL查询以适应SQLite性能特点
    增强版本：包含更多优化策略

    Args:
        sql (str): 原始SQL查询

    Returns:
        str: 优化后的SQL查询
    """
    optimized_sql = sql.strip()

    # 1. 智能添加LIMIT限制大结果集
    if (re.search(r'^\s*SELECT\b', optimized_sql, re.IGNORECASE) and
        not re.search(r'\bLIMIT\b', optimized_sql, re.IGNORECASE)):

        # 根据查询复杂度决定LIMIT大小
        if any(keyword in optimized_sql.upper() for keyword in ['GROUP BY', 'DISTINCT', 'HAVING']):
            limit = 500  # 聚合查询限制更小
        elif 'JOIN' in optimized_sql.upper():
            limit = 800  # JOIN查询中等限制
        else:
            limit = 1000  # 普通查询

        optimized_sql = f"{optimized_sql.rstrip(';')} LIMIT {limit}"

    # 2. 优化WHERE子句中的函数调用
    # 将 WHERE UPPER(column) = 'VALUE' 优化为使用COLLATE NOCASE
    optimized_sql = re.sub(
        r'\bWHERE\s+UPPER\s*\(\s*(\w+)\s*\)\s*=\s*[\'"]([^\'"]+)[\'"]',
        r"WHERE \1 = '\2' COLLATE NOCASE",
        optimized_sql,
        flags=re.IGNORECASE
    )

    # 3. 优化ORDER BY子句
    # 确保ORDER BY的列在SELECT中或有索引
    order_by_match = re.search(r'\bORDER\s+BY\s+([^;]+?)(?:\s+LIMIT|\s*$)', optimized_sql, re.IGNORECASE)
    if order_by_match:
        order_columns = order_by_match.group(1)
        # 如果ORDER BY列很多，建议只保留主要排序列
        if order_columns.count(',') > 2:
            # 只保留前两个排序列
            main_columns = ','.join(order_columns.split(',')[:2])
            optimized_sql = optimized_sql.replace(order_columns, main_columns)

    # 4. 优化JOIN查询
    if 'JOIN' in optimized_sql.upper():
        # 确保JOIN条件使用索引列
        # 建议将小表放在左边（SQLite的查询优化器会处理，但明确更好）
        pass

    # 5. 优化子查询
    # 将相关子查询转换为JOIN（如果可能）
    if re.search(r'\bEXISTS\s*\(', optimized_sql, re.IGNORECASE):
        # EXISTS子查询通常比IN子查询更高效
        pass

    return optimized_sql


def analyze_query_performance(sql: str) -> Dict[str, Any]:
    """
    分析查询性能并提供优化建议

    Args:
        sql (str): SQL查询语句

    Returns:
        Dict[str, Any]: 性能分析结果和建议
    """
    analysis = {
        'complexity_score': 0,
        'performance_issues': [],
        'optimization_suggestions': [],
        'estimated_cost': 'low'
    }

    sql_upper = sql.upper()

    # 计算复杂度分数
    complexity_factors = {
        'SELECT': 1,
        'JOIN': 3,
        'INNER JOIN': 3,
        'LEFT JOIN': 4,
        'RIGHT JOIN': 4,
        'OUTER JOIN': 5,
        'SUBQUERY': 5,
        'GROUP BY': 2,
        'ORDER BY': 2,
        'HAVING': 3,
        'DISTINCT': 2,
        'UNION': 4,
        'CASE WHEN': 2,
    }

    for keyword, weight in complexity_factors.items():
        count = sql_upper.count(keyword)
        analysis['complexity_score'] += count * weight

    # 检查性能问题
    if not re.search(r'\bLIMIT\b', sql_upper):
        analysis['performance_issues'].append('缺少LIMIT子句，可能返回大量数据')
        analysis['optimization_suggestions'].append('添加适当的LIMIT限制结果集大小')

    if re.search(r'SELECT\s+\*', sql_upper):
        analysis['performance_issues'].append('使用SELECT *，可能获取不必要的列')
        analysis['optimization_suggestions'].append('明确指定需要的列名')

    if re.search(r'WHERE.*\bLIKE\s+[\'"]%.*%[\'"]', sql_upper):
        analysis['performance_issues'].append('使用前后通配符的LIKE查询，无法使用索引')
        analysis['optimization_suggestions'].append('考虑使用全文搜索或重新设计查询')

    if sql_upper.count('JOIN') > 3:
        analysis['performance_issues'].append('过多的JOIN操作可能影响性能')
        analysis['optimization_suggestions'].append('考虑分解查询或使用临时表')

    if re.search(r'WHERE.*\b(UPPER|LOWER|SUBSTR)\s*\(', sql_upper):
        analysis['performance_issues'].append('WHERE子句中使用函数，可能无法使用索引')
        analysis['optimization_suggestions'].append('考虑使用函数索引或重写查询条件')

    # 估算查询成本
    if analysis['complexity_score'] > 20:
        analysis['estimated_cost'] = 'high'
    elif analysis['complexity_score'] > 10:
        analysis['estimated_cost'] = 'medium'
    else:
        analysis['estimated_cost'] = 'low'

    return analysis


def get_sqlite_pragma_settings() -> List[str]:
    """
    获取推荐的SQLite PRAGMA设置
    
    Returns:
        List[str]: PRAGMA设置列表
    """
    return [
        "PRAGMA foreign_keys = ON;",  # 启用外键约束
        "PRAGMA journal_mode = WAL;",  # 使用WAL模式提高并发性能
        "PRAGMA synchronous = NORMAL;",  # 平衡性能和安全性
        "PRAGMA cache_size = 10000;",  # 增加缓存大小
        "PRAGMA temp_store = MEMORY;",  # 临时表存储在内存中
    ]


def apply_sqlite_optimizations(engine):
    """
    应用SQLite性能优化设置

    Args:
        engine: SQLAlchemy引擎对象
    """
    if 'sqlite' in str(engine.url):
        from sqlalchemy import text
        with engine.connect() as conn:
            for pragma in get_sqlite_pragma_settings():
                try:
                    conn.execute(text(pragma))
                    conn.commit()
                except Exception as e:
                    print(f"Warning: Failed to apply pragma {pragma}: {e}")
