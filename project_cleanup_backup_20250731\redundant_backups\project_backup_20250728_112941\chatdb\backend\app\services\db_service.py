import pymysql
import sqlalchemy
from sqlalchemy import create_engine, inspect, text
from typing import Dict, Any, List, Optional
import urllib.parse
import logging
import time
from contextlib import contextmanager

from app.core.security import verify_password
from app.models.db_connection import DBConnection
from app.services.monitoring_service import log_performance, monitoring_service
from app.utils.sqlite_utils import convert_mysql_to_sqlite_sql, optimize_sqlite_query

logger = logging.getLogger(__name__)

# 连接池缓存
_engine_cache: Dict[str, sqlalchemy.Engine] = {}


@log_performance("数据库引擎创建")
def get_db_engine(connection: DBConnection, password: str = None, use_cache: bool = True) -> sqlalchemy.Engine:
    """
    Create a SQLAlchemy engine for the given database connection with caching and optimization.
    """
    try:
        # 生成缓存键
        cache_key = f"{connection.db_type}_{connection.host}_{connection.port}_{connection.database_name}_{connection.username}"

        # 检查缓存
        if use_cache and cache_key in _engine_cache:
            logger.debug(f"使用缓存的数据库引擎: {cache_key}")
            return _engine_cache[cache_key]

        # 获取密码
        actual_password = _get_connection_password(connection, password)
        encoded_password = urllib.parse.quote_plus(actual_password)

        # 构建连接字符串和引擎
        engine = _create_engine_by_type(connection, encoded_password)

        # 缓存引擎
        if use_cache:
            _engine_cache[cache_key] = engine
            logger.info(f"数据库引擎已缓存: {cache_key}")

        return engine

    except Exception as e:
        error_msg = f"创建数据库引擎失败: {str(e)}"
        logger.error(error_msg)
        monitoring_service.record_error(e, {'connection_id': getattr(connection, 'id', 'unknown')})
        raise Exception(error_msg)


def _get_connection_password(connection: DBConnection, password: str = None) -> str:
    """获取连接密码"""
    if hasattr(connection, 'password') and connection.password:
        return connection.password
    elif password:
        return password
    else:
        # 假设password_encrypted存储的是明文密码
        return getattr(connection, 'password_encrypted', '')


def _create_engine_by_type(connection: DBConnection, encoded_password: str) -> sqlalchemy.Engine:
    """根据数据库类型创建引擎"""
    db_type = connection.db_type.lower()

    # 通用引擎配置
    engine_config = {
        'pool_size': 10,
        'max_overflow': 20,
        'pool_timeout': 30,
        'pool_recycle': 3600,
        'pool_pre_ping': True,
    }

    if db_type == "sqlite":
        conn_str = f"sqlite:///{connection.database_name}"
        engine_config.update({
            'connect_args': {
                "check_same_thread": False,
                "timeout": 30,
            }
        })
        logger.info(f"连接SQLite数据库: {connection.database_name}")

    elif db_type == "mysql":
        conn_str = (
            f"mysql+pymysql://{connection.username}:"
            f"{encoded_password}@"
            f"{connection.host}:{connection.port}/{connection.database_name}"
        )
        logger.info(f"连接MySQL数据库: {connection.host}:{connection.port}/{connection.database_name}")

    elif db_type == "postgresql":
        conn_str = (
            f"postgresql://{connection.username}:"
            f"{encoded_password}@"
            f"{connection.host}:{connection.port}/{connection.database_name}"
        )
        logger.info(f"连接PostgreSQL数据库: {connection.host}:{connection.port}/{connection.database_name}")

    else:
        raise ValueError(f"不支持的数据库类型: {connection.db_type}")

    return create_engine(conn_str, **engine_config)


def test_db_connection(connection: DBConnection) -> bool:
    """
    Test if a database connection is valid.
    """
    try:
        print(f"Testing connection to {connection.db_type} database at {connection.host}:{connection.port}/{connection.database_name}")
        engine = get_db_engine(connection)
        with engine.connect() as conn:
            result = conn.execute(sqlalchemy.text("SELECT 1"))
            print(f"Connection test successful: {result.fetchone()}")
        return True
    except Exception as e:
        error_msg = f"Connection test failed: {str(e)}"
        print(error_msg)
        raise Exception(error_msg)


@log_performance("SQL查询执行")
def execute_query(connection: DBConnection, query: str, optimize: bool = True) -> List[Dict[str, Any]]:
    """
    Execute a SQL query on the target database and return the results with optimization.
    """
    start_time = time.time()

    try:
        # 查询优化
        if optimize:
            query = optimize_query_for_database(query, connection.db_type)

        logger.info(f"执行SQL查询: {query[:200]}...")

        engine = get_db_engine(connection)

        with get_db_connection(engine) as conn:
            result = conn.execute(text(query))
            columns = result.keys()
            rows = result.fetchall()

            # 转换结果
            results = [dict(zip(columns, row)) for row in rows]

            execution_time = time.time() - start_time
            logger.info(f"查询执行成功，返回 {len(results)} 条记录，耗时: {execution_time:.3f}秒")

            return results

    except Exception as e:
        execution_time = time.time() - start_time
        error_msg = f"查询执行失败: {str(e)}"
        logger.error(f"{error_msg}，耗时: {execution_time:.3f}秒")
        monitoring_service.record_error(e, {
            'query': query[:500],
            'connection_id': getattr(connection, 'id', 'unknown'),
            'execution_time': execution_time
        })
        raise Exception(error_msg)


@contextmanager
def get_db_connection(engine: sqlalchemy.Engine):
    """数据库连接上下文管理器"""
    conn = None
    try:
        conn = engine.connect()
        yield conn
    except Exception as e:
        if conn:
            conn.rollback()
        raise
    finally:
        if conn:
            conn.close()


def optimize_query_for_database(query: str, db_type: str) -> str:
    """根据数据库类型优化查询"""
    if db_type.lower() == "sqlite":
        # 转换MySQL语法到SQLite
        query = convert_mysql_to_sqlite_sql(query)
        query = optimize_sqlite_query(query)

    return query


def clear_engine_cache():
    """清空引擎缓存"""
    global _engine_cache
    for engine in _engine_cache.values():
        engine.dispose()
    _engine_cache.clear()
    logger.info("数据库引擎缓存已清空")
