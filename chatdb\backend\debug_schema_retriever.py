#!/usr/bin/env python3
"""
调试SCHEMA_RETRIEVER组件的值映射检索问题
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import sqlite3
from sqlalchemy.orm import Session
from sqlalchemy import create_engine
from app.core.config import settings
from app.db.session import SessionLocal

def debug_schema_retriever_database_connection():
    """调试SCHEMA_RETRIEVER的数据库连接"""
    print("🔍 调试SCHEMA_RETRIEVER数据库连接")
    print("=" * 60)
    
    try:
        # 1. 检查SessionLocal连接的数据库
        db = SessionLocal()
        
        # 获取数据库连接信息
        db_url = str(db.bind.url)
        print(f"📁 SessionLocal连接的数据库: {db_url}")
        
        # 2. 检查数据库中的表
        from sqlalchemy import text
        result = db.execute(text("SELECT name FROM sqlite_master WHERE type='table'"))
        tables = [row[0] for row in result.fetchall()]
        print(f"📋 数据库中的表: {tables}")
        
        # 3. 检查关键表是否存在
        key_tables = ['dbconnection', 'schematable', 'schemacolumn', 'valuemapping']
        for table in key_tables:
            exists = table in tables
            status = "✅" if exists else "❌"
            print(f"  {table}: {status}")
        
        db.close()
        return True
        
    except Exception as e:
        print(f"❌ 数据库连接调试失败: {e}")
        return False

def debug_connection_and_schema_data():
    """调试连接和Schema数据"""
    print("\n🔗 调试连接和Schema数据")
    print("=" * 60)
    
    try:
        db = SessionLocal()
        
        # 1. 检查fin_data连接
        from sqlalchemy import text
        result = db.execute(text("SELECT id, name, database_name FROM dbconnection WHERE name = 'fin_data'"))
        fin_data_conn = result.fetchone()
        
        if fin_data_conn:
            conn_id, conn_name, db_name = fin_data_conn
            print(f"✅ 找到fin_data连接: ID={conn_id}, 名称={conn_name}, 数据库={db_name}")
        else:
            print("❌ 未找到fin_data连接")
            db.close()
            return False
        
        # 2. 检查financial_data表
        result = db.execute(text("""
            SELECT id, table_name, description
            FROM schematable
            WHERE connection_id = :conn_id AND table_name = 'financial_data'
        """), {"conn_id": conn_id})
        
        financial_table = result.fetchone()
        if financial_table:
            table_id, table_name, description = financial_table
            print(f"✅ 找到financial_data表: ID={table_id}, 名称={table_name}")
        else:
            print("❌ 未找到financial_data表")
            db.close()
            return False
        
        # 3. 检查关键列
        result = db.execute(text("""
            SELECT id, column_name, data_type
            FROM schemacolumn
            WHERE table_id = :table_id AND column_name IN ('accounting_unit_name', 'accounting_organization', 'account_full_name', 'account_name')
            ORDER BY column_name
        """), {"table_id": table_id})
        
        key_columns = result.fetchall()
        print(f"\n📋 关键列信息:")
        column_ids = {}
        for col_id, col_name, data_type in key_columns:
            print(f"  ID:{col_id} - {col_name} ({data_type})")
            column_ids[col_name] = col_id
        
        # 4. 检查这些列的值映射
        print(f"\n🔗 检查值映射:")
        for col_name, col_id in column_ids.items():
            result = db.execute(text("""
                SELECT nl_term, db_value
                FROM valuemapping
                WHERE column_id = :col_id
                ORDER BY nl_term
            """), {"col_id": col_id})
            
            mappings = result.fetchall()
            if mappings:
                print(f"  ✅ {col_name} (ID:{col_id}): {len(mappings)} 个映射")
                for nl_term, db_value in mappings[:3]:  # 只显示前3个
                    print(f"    '{nl_term}' → '{db_value}'")
                if len(mappings) > 3:
                    print(f"    ... 还有 {len(mappings) - 3} 个映射")
            else:
                print(f"  ❌ {col_name} (ID:{col_id}): 无映射")
        
        db.close()
        return conn_id, table_id, column_ids
        
    except Exception as e:
        print(f"❌ 连接和Schema数据调试失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def simulate_schema_retriever_process(connection_id):
    """模拟SCHEMA_RETRIEVER的处理过程"""
    print(f"\n🔄 模拟SCHEMA_RETRIEVER处理过程 (connection_id={connection_id})")
    print("=" * 60)
    
    try:
        from app import crud
        from app.services.text2sql_utils import get_value_mappings
        
        db = SessionLocal()
        
        # 1. 模拟回退方案的表和列获取
        print("🔸 步骤1: 获取所有表")
        tables = crud.schema_table.get_by_connection(db=db, connection_id=connection_id)
        tables_list = []
        columns_list = []
        
        for table in tables:
            tables_list.append({
                "id": table.id,
                "name": table.table_name,
                "description": table.description or ""
            })
            print(f"  表: {table.table_name} (ID: {table.id})")
            
            # 获取表的所有列
            table_columns = crud.schema_column.get_by_table(db=db, table_id=table.id)
            for column in table_columns:
                columns_list.append({
                    "id": column.id,
                    "name": column.column_name,
                    "type": column.data_type,
                    "description": column.description,
                    "is_primary_key": column.is_primary_key,
                    "is_foreign_key": column.is_foreign_key,
                    "table_id": table.id,
                    "table_name": table.table_name
                })
        
        print(f"🔸 步骤2: 构建schema_context")
        schema_context = {
            "tables": tables_list,
            "columns": columns_list,
            "relationships": []
        }
        
        print(f"  表数量: {len(tables_list)}")
        print(f"  列数量: {len(columns_list)}")
        
        # 显示关键列
        key_columns = [col for col in columns_list if col['name'] in ['accounting_unit_name', 'accounting_organization', 'account_full_name', 'account_name']]
        print(f"  关键列:")
        for col in key_columns:
            print(f"    ID:{col['id']} - {col['name']} ({col['table_name']})")
        
        print(f"🔸 步骤3: 获取值映射")
        value_mappings = get_value_mappings(db, schema_context)
        
        print(f"  值映射结果: {len(value_mappings)} 个表.列")
        for table_col, mappings in value_mappings.items():
            print(f"    {table_col}: {len(mappings)} 个映射")
            for nl_term, db_value in list(mappings.items())[:2]:  # 只显示前2个
                print(f"      '{nl_term}' → '{db_value}'")
        
        db.close()
        return len(value_mappings) > 0
        
    except Exception as e:
        print(f"❌ 模拟SCHEMA_RETRIEVER处理失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_get_value_mappings_function():
    """检查get_value_mappings函数的逻辑"""
    print(f"\n🧪 检查get_value_mappings函数逻辑")
    print("=" * 60)
    
    try:
        from app.services.text2sql_utils import get_value_mappings
        from app import crud
        
        db = SessionLocal()
        
        # 创建一个简单的测试schema_context
        test_schema_context = {
            "columns": [
                {"id": 226, "name": "accounting_unit_name", "table_name": "financial_data"},
                {"id": 228, "name": "account_full_name", "table_name": "financial_data"}
            ]
        }
        
        print("📋 测试schema_context:")
        for col in test_schema_context["columns"]:
            print(f"  ID:{col['id']} - {col['name']} ({col['table_name']})")
        
        # 直接检查这些column_id的映射
        print(f"\n🔍 直接检查column_id的映射:")
        for col in test_schema_context["columns"]:
            column_id = col["id"]
            column_mappings = crud.value_mapping.get_by_column(db=db, column_id=column_id)
            
            print(f"  Column ID {column_id} ({col['name']}):")
            if column_mappings:
                print(f"    找到 {len(column_mappings)} 个映射:")
                for mapping in column_mappings[:3]:
                    print(f"      '{mapping.nl_term}' → '{mapping.db_value}'")
            else:
                print(f"    ❌ 未找到映射")
        
        # 调用get_value_mappings
        print(f"\n🔧 调用get_value_mappings:")
        value_mappings = get_value_mappings(db, test_schema_context)
        
        print(f"  返回结果: {len(value_mappings)} 个表.列映射")
        for table_col, mappings in value_mappings.items():
            print(f"    {table_col}: {len(mappings)} 个映射")
        
        db.close()
        return len(value_mappings) > 0
        
    except Exception as e:
        print(f"❌ 检查get_value_mappings函数失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 调试SCHEMA_RETRIEVER值映射检索问题")
    print("=" * 80)
    
    # 执行调试步骤
    steps = [
        ("数据库连接检查", debug_schema_retriever_database_connection),
        ("连接和Schema数据检查", debug_connection_and_schema_data),
        ("get_value_mappings函数检查", check_get_value_mappings_function)
    ]
    
    results = []
    connection_data = None
    
    for step_name, step_func in steps:
        print(f"\n{'='*20} {step_name} {'='*20}")
        try:
            if step_name == "连接和Schema数据检查":
                result = step_func()
                if result:
                    connection_data = result
                    results.append((step_name, True))
                else:
                    results.append((step_name, False))
            else:
                result = step_func()
                results.append((step_name, result))
        except Exception as e:
            print(f"❌ {step_name} 执行失败: {e}")
            results.append((step_name, False))
    
    # 如果有连接数据，运行模拟测试
    if connection_data:
        conn_id, table_id, column_ids = connection_data
        print(f"\n{'='*20} 模拟SCHEMA_RETRIEVER处理 {'='*20}")
        try:
            result = simulate_schema_retriever_process(conn_id)
            results.append(("SCHEMA_RETRIEVER模拟", result))
        except Exception as e:
            print(f"❌ SCHEMA_RETRIEVER模拟失败: {e}")
            results.append(("SCHEMA_RETRIEVER模拟", False))
    
    # 总结结果
    print("\n" + "=" * 80)
    print("📊 调试结果总结")
    print("=" * 80)
    
    for step_name, result in results:
        status = "✅ 成功" if result else "❌ 失败"
        print(f"{step_name}: {status}")
    
    # 提供建议
    all_passed = all(result for _, result in results)
    if all_passed:
        print("\n🎉 所有检查都通过！SCHEMA_RETRIEVER应该能正常工作。")
        print("问题可能在于缓存或其他地方。")
    else:
        print("\n⚠️ 发现问题！请根据上述结果进行修复。")

if __name__ == "__main__":
    main()
