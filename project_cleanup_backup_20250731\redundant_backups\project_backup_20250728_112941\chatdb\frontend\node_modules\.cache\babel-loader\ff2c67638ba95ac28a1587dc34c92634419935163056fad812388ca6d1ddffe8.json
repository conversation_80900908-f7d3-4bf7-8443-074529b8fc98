{"ast": null, "code": "function remove() {\n  var parent = this.parentNode;\n  if (parent) parent.removeChild(this);\n}\nexport default function () {\n  return this.each(remove);\n}", "map": {"version": 3, "names": ["remove", "parent", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "each"], "sources": ["C:/Users/<USER>/PycharmProjects/智能数据分析系统/chatdb/frontend/node_modules/d3-selection/src/selection/remove.js"], "sourcesContent": ["function remove() {\n  var parent = this.parentNode;\n  if (parent) parent.removeChild(this);\n}\n\nexport default function() {\n  return this.each(remove);\n}\n"], "mappings": "AAAA,SAASA,MAAMA,CAAA,EAAG;EAChB,IAAIC,MAAM,GAAG,IAAI,CAACC,UAAU;EAC5B,IAAID,MAAM,EAAEA,MAAM,CAACE,WAAW,CAAC,IAAI,CAAC;AACtC;AAEA,eAAe,YAAW;EACxB,OAAO,IAAI,CAACC,IAAI,CAACJ,MAAM,CAAC;AAC1B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}