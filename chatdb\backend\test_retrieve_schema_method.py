#!/usr/bin/env python3
"""
直接测试retrieve_schema方法的逻辑
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import asyncio

async def test_retrieve_schema_method():
    """直接测试retrieve_schema方法的逻辑"""
    print("🧪 测试retrieve_schema方法逻辑")
    print("=" * 60)
    
    try:
        from app.db.session import SessionLocal
        from app.services.text2sql_service import retrieve_relevant_schema, get_value_mappings
        from app import crud
        
        # 测试参数
        connection_id = 1  # fin_data连接
        query = "分析2024年1月各公司管理费用"
        
        print(f"📋 测试参数:")
        print(f"  连接ID: {connection_id}")
        print(f"  查询: {query}")
        
        # 创建数据库会话
        db = SessionLocal()
        
        try:
            # 🎯 智能Schema检索策略
            use_fallback = False
            schema_context = {}

            try:
                # 尝试使用完整的智能检索系统
                print(f"\n🔄 尝试使用智能Schema检索系统...")
                schema_context = await retrieve_relevant_schema(db=db, connection_id=connection_id, query=query)

                # 🔍 验证检索结果质量
                columns_count = len(schema_context.get('columns', []))
                print(f"  智能检索结果: {columns_count} columns")

                if columns_count == 0:
                    print(f"  ❌ 智能检索返回空结果，切换到回退方案")
                    use_fallback = True
                else:
                    print(f"  ✅ 智能检索成功，使用完整功能")

            except Exception as e:
                print(f"  ❌ 智能检索失败: {e}")
                use_fallback = True

            if use_fallback:
                # 🔧 回退方案：简单可靠的直接查询
                print(f"\n🔄 启用回退方案：直接数据库查询")

                # 获取所有表
                tables = crud.schema_table.get_by_connection(db=db, connection_id=connection_id)
                tables_list = []
                columns_list = []

                for table in tables:
                    tables_list.append({
                        "id": table.id,
                        "name": table.table_name,
                        "description": table.description or ""
                    })

                    # 获取表的所有列
                    table_columns = crud.schema_column.get_by_table(db=db, table_id=table.id)
                    for column in table_columns:
                        columns_list.append({
                            "id": column.id,
                            "name": column.column_name,
                            "type": column.data_type,
                            "description": column.description,
                            "is_primary_key": column.is_primary_key,
                            "is_foreign_key": column.is_foreign_key,
                            "table_id": table.id,
                            "table_name": table.table_name
                        })

                schema_context = {
                    "tables": tables_list,
                    "columns": columns_list,
                    "relationships": []
                }

                print(f"  回退方案成功：{len(tables_list)} 表，{len(columns_list)} 字段")

            # 🔍 调试：检查schema_context内容
            print(f"\n📊 Schema context详情:")
            print(f"  Tables: {len(schema_context.get('tables', []))}")
            print(f"  Columns: {len(schema_context.get('columns', []))}")
            
            # 显示关键列信息
            key_columns = [col for col in schema_context.get('columns', []) if col.get('name') in ['accounting_unit_name', 'accounting_organization', 'account_full_name', 'account_name']]
            if key_columns:
                print(f"  ✅ 关键列:")
                for col in key_columns:
                    print(f"    ID:{col.get('id')} - {col.get('name')} ({col.get('table_name')})")
            else:
                print(f"  ❌ 未找到关键列")

            # 获取值映射
            print(f"\n🔗 获取值映射...")
            value_mappings = get_value_mappings(db, schema_context)

            print(f"  Value mappings: {len(value_mappings)} fields")
            for table_col, mappings in value_mappings.items():
                print(f"    {table_col}: {len(mappings)} mappings")
                # 显示前几个映射作为示例
                for i, (nl_term, db_value) in enumerate(mappings.items()):
                    if i < 2:  # 只显示前2个
                        print(f"      '{nl_term}' → '{db_value}'")

            # 生成映射字符串
            mappings_str = ""
            if value_mappings:
                mappings_str = "-- Value Mappings:\n"
                for column, mappings in value_mappings.items():
                    mappings_str += f"-- For {column}:\n"
                    for nl_term, db_value in mappings.items():
                        mappings_str += f"--   '{nl_term}' in natural language refers to '{db_value}' in the database\n"
                mappings_str += "\n"

                print(f"\n✅ 生成映射字符串长度: {len(mappings_str)}")
            else:
                print(f"\n❌ 无值映射，映射字符串为空")

            return len(value_mappings) > 0
            
        finally:
            db.close()
            
    except Exception as e:
        print(f"❌ 测试retrieve_schema方法失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主函数"""
    print("🚀 测试retrieve_schema方法")
    print("=" * 80)
    
    success = await test_retrieve_schema_method()
    
    print("\n" + "=" * 80)
    if success:
        print("✅ retrieve_schema方法测试成功！")
        print("\n💡 这证明了逻辑是正确的。")
        print("如果前端仍显示映射为0，问题可能在于:")
        print("1. 🔄 服务缓存 - 需要重启后端服务")
        print("2. 📡 消息传递 - Agent间通信问题")
        print("3. 🕐 时序问题 - 异步处理顺序")
    else:
        print("❌ retrieve_schema方法测试失败！")

if __name__ == "__main__":
    asyncio.run(main())
