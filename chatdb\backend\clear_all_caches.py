#!/usr/bin/env python3
"""
清除所有Text2SQL相关的缓存
确保修复后的逻辑能够立即生效
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import hashlib
import time

def clear_llm_response_cache():
    """清除LLM响应缓存"""
    print("🧹 清除LLM响应缓存")
    print("=" * 60)
    
    try:
        from app.services.cache_service import cache_service
        
        # 清除所有以 "llm_response:" 开头的缓存键
        test_queries = [
            "分析2024年1月各公司管理费用",
            "查询各公司2024年的费用情况", 
            "查询科目名称包含管理费用的记录",
            "2024年1月管理费用分析",
            "各公司管理费用统计"
        ]
        
        cleared_count = 0
        for query in test_queries:
            query_hash = hashlib.md5(query.lower().encode()).hexdigest()
            cache_key = f"llm_response:{query_hash}"
            
            if cache_service.delete(cache_key):
                print(f"✅ 清除缓存: {query[:30]}...")
                cleared_count += 1
            else:
                print(f"⚠️ 缓存不存在: {query[:30]}...")
        
        print(f"\n📊 清除了 {cleared_count} 个LLM响应缓存")
        return cleared_count > 0
        
    except Exception as e:
        print(f"❌ 清除LLM响应缓存失败: {e}")
        return False

def clear_query_result_cache():
    """清除查询结果缓存"""
    print("\n🗃️ 清除查询结果缓存")
    print("=" * 60)
    
    try:
        from app.services.cache_service import cache_service
        
        # 尝试清除可能的查询结果缓存键
        # 这些键的格式通常是基于查询内容的哈希
        test_queries = [
            "分析2024年1月各公司管理费用",
            "查询各公司2024年的费用情况",
            "查询科目名称包含管理费用的记录"
        ]
        
        cleared_count = 0
        for query in test_queries:
            # 尝试不同的缓存键格式
            possible_keys = [
                f"query_result:{hashlib.md5(query.encode()).hexdigest()}",
                f"text2sql_result:{hashlib.md5(query.encode()).hexdigest()}",
                f"sql_query:{hashlib.md5(query.encode()).hexdigest()}"
            ]
            
            for cache_key in possible_keys:
                if cache_service.delete(cache_key):
                    print(f"✅ 清除查询缓存: {cache_key[:50]}...")
                    cleared_count += 1
        
        print(f"\n📊 清除了 {cleared_count} 个查询结果缓存")
        return True
        
    except Exception as e:
        print(f"❌ 清除查询结果缓存失败: {e}")
        return False

def clear_value_mappings_cache():
    """清除值映射缓存"""
    print("\n🔗 清除值映射缓存")
    print("=" * 60)
    
    try:
        from app.services.cache_service import cache_service
        
        # 清除值映射相关的缓存
        cache_patterns = [
            "value_mappings:",
            "schema_context:",
            "metadata:",
            "financial_metadata:"
        ]
        
        cleared_count = 0
        for pattern in cache_patterns:
            # 由于我们不知道确切的键，尝试一些可能的键
            for i in range(10):  # 尝试一些可能的ID
                cache_key = f"{pattern}{i}"
                if cache_service.delete(cache_key):
                    print(f"✅ 清除映射缓存: {cache_key}")
                    cleared_count += 1
        
        print(f"\n📊 清除了 {cleared_count} 个值映射缓存")
        return True
        
    except Exception as e:
        print(f"❌ 清除值映射缓存失败: {e}")
        return False

def clear_all_cache_service():
    """尝试清除整个缓存服务"""
    print("\n💥 尝试清除整个缓存服务")
    print("=" * 60)
    
    try:
        from app.services.cache_service import cache_service
        
        # 如果缓存服务支持清除所有缓存
        if hasattr(cache_service, 'clear_all'):
            cache_service.clear_all()
            print("✅ 已清除所有缓存")
            return True
        elif hasattr(cache_service, 'flush_all'):
            cache_service.flush_all()
            print("✅ 已刷新所有缓存")
            return True
        else:
            print("⚠️ 缓存服务不支持批量清除")
            return False
            
    except Exception as e:
        print(f"❌ 清除整个缓存服务失败: {e}")
        return False

def restart_service_recommendation():
    """提供重启服务的建议"""
    print("\n🔄 服务重启建议")
    print("=" * 60)
    
    print("为了确保所有修复完全生效，建议执行以下操作:")
    print()
    print("1. **立即重启后端服务**:")
    print("   - 停止当前的FastAPI服务")
    print("   - 重新启动服务以清除内存缓存")
    print()
    print("2. **清除浏览器缓存**:")
    print("   - 刷新前端页面")
    print("   - 或使用Ctrl+F5强制刷新")
    print()
    print("3. **测试验证**:")
    print("   - 输入: '分析2024年1月各公司管理费用'")
    print("   - 期望: 生成的SQL使用 accounting_unit_name 和 account_full_name")
    print()
    print("4. **观察日志**:")
    print("   - 检查映射字符串长度是否 > 0")
    print("   - 确认没有缓存命中的日志")

def test_current_system_state():
    """测试当前系统状态"""
    print("\n🧪 测试当前系统状态")
    print("=" * 60)
    
    try:
        from app.services.text2sql_service import construct_prompt
        from app.services.text2sql_utils import get_value_mappings
        from sqlalchemy import create_engine
        from sqlalchemy.orm import Session
        from app.core.config import settings
        
        # 创建测试数据
        query = "分析2024年1月各公司管理费用"
        schema_context = {
            "columns": [
                {"id": 226, "name": "accounting_unit_name", "table_name": "financial_data"},
                {"id": 228, "name": "account_full_name", "table_name": "financial_data"}
            ]
        }
        
        # 获取实际的值映射
        engine = create_engine(f"sqlite:///{settings.SQLITE_DB_PATH}")
        db = Session(engine)
        
        value_mappings = get_value_mappings(db, schema_context)
        db.close()
        
        print(f"📊 获取到 {len(value_mappings)} 个表.列的映射")
        
        # 测试提示构建
        prompt = construct_prompt(query, schema_context, value_mappings, {})
        
        print(f"📝 提示长度: {len(prompt)} 字符")
        
        # 检查关键内容
        has_mappings = len(value_mappings) > 0
        has_priority_guidance = "优先级" in prompt or "首选" in prompt
        has_company_mapping = any("公司" in str(mappings) for mappings in value_mappings.values())
        
        print(f"包含值映射: {'✅' if has_mappings else '❌'}")
        print(f"包含优先级指导: {'✅' if has_priority_guidance else '❌'}")
        print(f"包含公司映射: {'✅' if has_company_mapping else '❌'}")
        
        return has_mappings and has_priority_guidance and has_company_mapping
        
    except Exception as e:
        print(f"❌ 测试系统状态失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 清除所有Text2SQL缓存")
    print("=" * 80)
    
    # 执行清除操作
    operations = [
        ("清除LLM响应缓存", clear_llm_response_cache),
        ("清除查询结果缓存", clear_query_result_cache),
        ("清除值映射缓存", clear_value_mappings_cache),
        ("清除整个缓存服务", clear_all_cache_service),
        ("测试当前系统状态", test_current_system_state)
    ]
    
    results = []
    for op_name, op_func in operations:
        try:
            result = op_func()
            results.append((op_name, result))
        except Exception as e:
            print(f"❌ {op_name} 执行失败: {e}")
            results.append((op_name, False))
    
    # 总结结果
    print("\n" + "=" * 80)
    print("📊 缓存清除结果总结")
    print("=" * 80)
    
    for op_name, result in results:
        status = "✅ 成功" if result else "❌ 失败"
        print(f"{op_name}: {status}")
    
    # 提供建议
    restart_service_recommendation()
    
    print("\n🎯 关键提醒:")
    print("缓存清除后，请立即重启后端服务以确保修复完全生效！")

if __name__ == "__main__":
    main()
