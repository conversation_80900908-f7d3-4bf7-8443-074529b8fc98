{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport DropboxCircleFilledSvg from \"@ant-design/icons-svg/es/asn/DropboxCircleFilled\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar DropboxCircleFilled = function DropboxCircleFilled(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: DropboxCircleFilledSvg\n  }));\n};\n\n/**![dropbox-circle](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTY2My44IDQ1NS41em0tMTUxLjUtOTMuOGwtMTUxLjggOTMuOCAxNTEuOCA5My45IDE1MS41LTkzLjl6TTUxMiA2NEMyNjQuNiA2NCA2NCAyNjQuNiA2NCA1MTJzMjAwLjYgNDQ4IDQ0OCA0NDggNDQ4LTIwMC42IDQ0OC00NDhTNzU5LjQgNjQgNTEyIDY0em0xNTEuMiA1OTUuNUw1MTIuNiA3NTBsLTE1MS05MC41di0zMy4xbDQ1LjQgMjkuNCAxMDUuNi04Ny43IDEwNS42IDg3LjcgNDUuMS0yOS40djMzLjF6bS00NS42LTIyLjRsLTEwNS4zLTg3LjdMNDA3IDYzNy4xbC0xNTEtOTkuMiAxMDQuNS04Mi40TDI1NiAzNzEuMiA0MDcgMjc0bDEwNS4zIDg3LjdMNjE3LjYgMjc0IDc2OCAzNzIuMWwtMTA0LjIgODMuNUw3NjggNTM5bC0xNTAuNCA5OC4xeiIgLz48L3N2Zz4=) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(DropboxCircleFilled);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'DropboxCircleFilled';\n}\nexport default RefIcon;", "map": {"version": 3, "names": ["_extends", "React", "DropboxCircleFilledSvg", "AntdIcon", "DropboxCircleFilled", "props", "ref", "createElement", "icon", "RefIcon", "forwardRef", "process", "env", "NODE_ENV", "displayName"], "sources": ["C:/Users/<USER>/PycharmProjects/智能数据分析系统/chatdb/frontend/node_modules/@ant-design/icons/es/icons/DropboxCircleFilled.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport DropboxCircleFilledSvg from \"@ant-design/icons-svg/es/asn/DropboxCircleFilled\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar DropboxCircleFilled = function DropboxCircleFilled(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: DropboxCircleFilledSvg\n  }));\n};\n\n/**![dropbox-circle](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTY2My44IDQ1NS41em0tMTUxLjUtOTMuOGwtMTUxLjggOTMuOCAxNTEuOCA5My45IDE1MS41LTkzLjl6TTUxMiA2NEMyNjQuNiA2NCA2NCAyNjQuNiA2NCA1MTJzMjAwLjYgNDQ4IDQ0OCA0NDggNDQ4LTIwMC42IDQ0OC00NDhTNzU5LjQgNjQgNTEyIDY0em0xNTEuMiA1OTUuNUw1MTIuNiA3NTBsLTE1MS05MC41di0zMy4xbDQ1LjQgMjkuNCAxMDUuNi04Ny43IDEwNS42IDg3LjcgNDUuMS0yOS40djMzLjF6bS00NS42LTIyLjRsLTEwNS4zLTg3LjdMNDA3IDYzNy4xbC0xNTEtOTkuMiAxMDQuNS04Mi40TDI1NiAzNzEuMiA0MDcgMjc0bDEwNS4zIDg3LjdMNjE3LjYgMjc0IDc2OCAzNzIuMWwtMTA0LjIgODMuNUw3NjggNTM5bC0xNTAuNCA5OC4xeiIgLz48L3N2Zz4=) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(DropboxCircleFilled);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'DropboxCircleFilled';\n}\nexport default RefIcon;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD;AACA;;AAEA,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,sBAAsB,MAAM,kDAAkD;AACrF,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,IAAIC,mBAAmB,GAAG,SAASA,mBAAmBA,CAACC,KAAK,EAAEC,GAAG,EAAE;EACjE,OAAO,aAAaL,KAAK,CAACM,aAAa,CAACJ,QAAQ,EAAEH,QAAQ,CAAC,CAAC,CAAC,EAAEK,KAAK,EAAE;IACpEC,GAAG,EAAEA,GAAG;IACRE,IAAI,EAAEN;EACR,CAAC,CAAC,CAAC;AACL,CAAC;;AAED;AACA,IAAIO,OAAO,GAAG,aAAaR,KAAK,CAACS,UAAU,CAACN,mBAAmB,CAAC;AAChE,IAAIO,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCJ,OAAO,CAACK,WAAW,GAAG,qBAAqB;AAC7C;AACA,eAAeL,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}