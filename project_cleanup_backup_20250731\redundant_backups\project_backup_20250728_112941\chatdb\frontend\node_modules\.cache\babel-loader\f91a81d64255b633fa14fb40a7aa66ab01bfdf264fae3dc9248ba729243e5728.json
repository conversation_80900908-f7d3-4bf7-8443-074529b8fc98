{"ast": null, "code": "/*\nLanguage: Dart\nRequires: markdown.js\nAuthor: <PERSON> <<EMAIL>>\nDescription: Dart a modern, object-oriented language developed by Google. For more information see https://www.dartlang.org/\nWebsite: https://dart.dev\nCategory: scripting\n*/\n\n/** @type LanguageFn */\nfunction dart(hljs) {\n  const SUBST = {\n    className: 'subst',\n    variants: [{\n      begin: '\\\\$[A-Za-z0-9_]+'\n    }]\n  };\n  const BRACED_SUBST = {\n    className: 'subst',\n    variants: [{\n      begin: /\\$\\{/,\n      end: /\\}/\n    }],\n    keywords: 'true false null this is new super'\n  };\n  const STRING = {\n    className: 'string',\n    variants: [{\n      begin: 'r\\'\\'\\'',\n      end: '\\'\\'\\''\n    }, {\n      begin: 'r\"\"\"',\n      end: '\"\"\"'\n    }, {\n      begin: 'r\\'',\n      end: '\\'',\n      illegal: '\\\\n'\n    }, {\n      begin: 'r\"',\n      end: '\"',\n      illegal: '\\\\n'\n    }, {\n      begin: '\\'\\'\\'',\n      end: '\\'\\'\\'',\n      contains: [hljs.BACKSLASH_ESCAPE, SUBST, BRACED_SUBST]\n    }, {\n      begin: '\"\"\"',\n      end: '\"\"\"',\n      contains: [hljs.BACKSLASH_ESCAPE, SUBST, BRACED_SUBST]\n    }, {\n      begin: '\\'',\n      end: '\\'',\n      illegal: '\\\\n',\n      contains: [hljs.BACKSLASH_ESCAPE, SUBST, BRACED_SUBST]\n    }, {\n      begin: '\"',\n      end: '\"',\n      illegal: '\\\\n',\n      contains: [hljs.BACKSLASH_ESCAPE, SUBST, BRACED_SUBST]\n    }]\n  };\n  BRACED_SUBST.contains = [hljs.C_NUMBER_MODE, STRING];\n  const BUILT_IN_TYPES = [\n  // dart:core\n  'Comparable', 'DateTime', 'Duration', 'Function', 'Iterable', 'Iterator', 'List', 'Map', 'Match', 'Object', 'Pattern', 'RegExp', 'Set', 'Stopwatch', 'String', 'StringBuffer', 'StringSink', 'Symbol', 'Type', 'Uri', 'bool', 'double', 'int', 'num',\n  // dart:html\n  'Element', 'ElementList'];\n  const NULLABLE_BUILT_IN_TYPES = BUILT_IN_TYPES.map(e => `${e}?`);\n  const KEYWORDS = {\n    keyword: 'abstract as assert async await break case catch class const continue covariant default deferred do ' + 'dynamic else enum export extends extension external factory false final finally for Function get hide if ' + 'implements import in inferface is late library mixin new null on operator part required rethrow return set ' + 'show static super switch sync this throw true try typedef var void while with yield',\n    built_in: BUILT_IN_TYPES.concat(NULLABLE_BUILT_IN_TYPES).concat([\n    // dart:core\n    'Never', 'Null', 'dynamic', 'print',\n    // dart:html\n    'document', 'querySelector', 'querySelectorAll', 'window']),\n    $pattern: /[A-Za-z][A-Za-z0-9_]*\\??/\n  };\n  return {\n    name: 'Dart',\n    keywords: KEYWORDS,\n    contains: [STRING, hljs.COMMENT(/\\/\\*\\*(?!\\/)/, /\\*\\//, {\n      subLanguage: 'markdown',\n      relevance: 0\n    }), hljs.COMMENT(/\\/{3,} ?/, /$/, {\n      contains: [{\n        subLanguage: 'markdown',\n        begin: '.',\n        end: '$',\n        relevance: 0\n      }]\n    }), hljs.C_LINE_COMMENT_MODE, hljs.C_BLOCK_COMMENT_MODE, {\n      className: 'class',\n      beginKeywords: 'class interface',\n      end: /\\{/,\n      excludeEnd: true,\n      contains: [{\n        beginKeywords: 'extends implements'\n      }, hljs.UNDERSCORE_TITLE_MODE]\n    }, hljs.C_NUMBER_MODE, {\n      className: 'meta',\n      begin: '@[A-Za-z]+'\n    }, {\n      begin: '=>' // No markup, just a relevance booster\n    }]\n  };\n}\nmodule.exports = dart;", "map": {"version": 3, "names": ["dart", "hljs", "SUBST", "className", "variants", "begin", "BRACED_SUBST", "end", "keywords", "STRING", "illegal", "contains", "BACKSLASH_ESCAPE", "C_NUMBER_MODE", "BUILT_IN_TYPES", "NULLABLE_BUILT_IN_TYPES", "map", "e", "KEYWORDS", "keyword", "built_in", "concat", "$pattern", "name", "COMMENT", "subLanguage", "relevance", "C_LINE_COMMENT_MODE", "C_BLOCK_COMMENT_MODE", "beginKeywords", "excludeEnd", "UNDERSCORE_TITLE_MODE", "module", "exports"], "sources": ["C:/Users/<USER>/PycharmProjects/智能数据分析系统/chatdb/frontend/node_modules/highlight.js/lib/languages/dart.js"], "sourcesContent": ["/*\nLanguage: Dart\nRequires: markdown.js\nAuthor: <PERSON> <<EMAIL>>\nDescription: Dart a modern, object-oriented language developed by Google. For more information see https://www.dartlang.org/\nWebsite: https://dart.dev\nCategory: scripting\n*/\n\n/** @type LanguageFn */\nfunction dart(hljs) {\n  const SUBST = {\n    className: 'subst',\n    variants: [{\n      begin: '\\\\$[A-Za-z0-9_]+'\n    }]\n  };\n\n  const BRACED_SUBST = {\n    className: 'subst',\n    variants: [{\n      begin: /\\$\\{/,\n      end: /\\}/\n    }],\n    keywords: 'true false null this is new super'\n  };\n\n  const STRING = {\n    className: 'string',\n    variants: [\n      {\n        begin: 'r\\'\\'\\'',\n        end: '\\'\\'\\''\n      },\n      {\n        begin: 'r\"\"\"',\n        end: '\"\"\"'\n      },\n      {\n        begin: 'r\\'',\n        end: '\\'',\n        illegal: '\\\\n'\n      },\n      {\n        begin: 'r\"',\n        end: '\"',\n        illegal: '\\\\n'\n      },\n      {\n        begin: '\\'\\'\\'',\n        end: '\\'\\'\\'',\n        contains: [\n          hljs.BACKSLASH_ESCAPE,\n          SUBST,\n          BRACED_SUBST\n        ]\n      },\n      {\n        begin: '\"\"\"',\n        end: '\"\"\"',\n        contains: [\n          hljs.BACKSLASH_ESCAPE,\n          SUBST,\n          BRACED_SUBST\n        ]\n      },\n      {\n        begin: '\\'',\n        end: '\\'',\n        illegal: '\\\\n',\n        contains: [\n          hljs.BACKSLASH_ESCAPE,\n          SUBST,\n          BRACED_SUBST\n        ]\n      },\n      {\n        begin: '\"',\n        end: '\"',\n        illegal: '\\\\n',\n        contains: [\n          hljs.BACKSLASH_ESCAPE,\n          SUBST,\n          BRACED_SUBST\n        ]\n      }\n    ]\n  };\n  BRACED_SUBST.contains = [\n    hljs.C_NUMBER_MODE,\n    STRING\n  ];\n\n  const BUILT_IN_TYPES = [\n    // dart:core\n    'Comparable',\n    'DateTime',\n    'Duration',\n    'Function',\n    'Iterable',\n    'Iterator',\n    'List',\n    'Map',\n    'Match',\n    'Object',\n    'Pattern',\n    'RegExp',\n    'Set',\n    'Stopwatch',\n    'String',\n    'StringBuffer',\n    'StringSink',\n    'Symbol',\n    'Type',\n    'Uri',\n    'bool',\n    'double',\n    'int',\n    'num',\n    // dart:html\n    'Element',\n    'ElementList'\n  ];\n  const NULLABLE_BUILT_IN_TYPES = BUILT_IN_TYPES.map((e) => `${e}?`);\n\n  const KEYWORDS = {\n    keyword: 'abstract as assert async await break case catch class const continue covariant default deferred do ' +\n      'dynamic else enum export extends extension external factory false final finally for Function get hide if ' +\n      'implements import in inferface is late library mixin new null on operator part required rethrow return set ' +\n      'show static super switch sync this throw true try typedef var void while with yield',\n    built_in:\n      BUILT_IN_TYPES\n        .concat(NULLABLE_BUILT_IN_TYPES)\n        .concat([\n          // dart:core\n          'Never',\n          'Null',\n          'dynamic',\n          'print',\n          // dart:html\n          'document',\n          'querySelector',\n          'querySelectorAll',\n          'window'\n        ]),\n    $pattern: /[A-Za-z][A-Za-z0-9_]*\\??/\n  };\n\n  return {\n    name: 'Dart',\n    keywords: KEYWORDS,\n    contains: [\n      STRING,\n      hljs.COMMENT(\n        /\\/\\*\\*(?!\\/)/,\n        /\\*\\//,\n        {\n          subLanguage: 'markdown',\n          relevance: 0\n        }\n      ),\n      hljs.COMMENT(\n        /\\/{3,} ?/,\n        /$/, {\n          contains: [{\n            subLanguage: 'markdown',\n            begin: '.',\n            end: '$',\n            relevance: 0\n          }]\n        }\n      ),\n      hljs.C_LINE_COMMENT_MODE,\n      hljs.C_BLOCK_COMMENT_MODE,\n      {\n        className: 'class',\n        beginKeywords: 'class interface',\n        end: /\\{/,\n        excludeEnd: true,\n        contains: [\n          {\n            beginKeywords: 'extends implements'\n          },\n          hljs.UNDERSCORE_TITLE_MODE\n        ]\n      },\n      hljs.C_NUMBER_MODE,\n      {\n        className: 'meta',\n        begin: '@[A-Za-z]+'\n      },\n      {\n        begin: '=>' // No markup, just a relevance booster\n      }\n    ]\n  };\n}\n\nmodule.exports = dart;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,SAASA,IAAIA,CAACC,IAAI,EAAE;EAClB,MAAMC,KAAK,GAAG;IACZC,SAAS,EAAE,OAAO;IAClBC,QAAQ,EAAE,CAAC;MACTC,KAAK,EAAE;IACT,CAAC;EACH,CAAC;EAED,MAAMC,YAAY,GAAG;IACnBH,SAAS,EAAE,OAAO;IAClBC,QAAQ,EAAE,CAAC;MACTC,KAAK,EAAE,MAAM;MACbE,GAAG,EAAE;IACP,CAAC,CAAC;IACFC,QAAQ,EAAE;EACZ,CAAC;EAED,MAAMC,MAAM,GAAG;IACbN,SAAS,EAAE,QAAQ;IACnBC,QAAQ,EAAE,CACR;MACEC,KAAK,EAAE,SAAS;MAChBE,GAAG,EAAE;IACP,CAAC,EACD;MACEF,KAAK,EAAE,MAAM;MACbE,GAAG,EAAE;IACP,CAAC,EACD;MACEF,KAAK,EAAE,KAAK;MACZE,GAAG,EAAE,IAAI;MACTG,OAAO,EAAE;IACX,CAAC,EACD;MACEL,KAAK,EAAE,IAAI;MACXE,GAAG,EAAE,GAAG;MACRG,OAAO,EAAE;IACX,CAAC,EACD;MACEL,KAAK,EAAE,QAAQ;MACfE,GAAG,EAAE,QAAQ;MACbI,QAAQ,EAAE,CACRV,IAAI,CAACW,gBAAgB,EACrBV,KAAK,EACLI,YAAY;IAEhB,CAAC,EACD;MACED,KAAK,EAAE,KAAK;MACZE,GAAG,EAAE,KAAK;MACVI,QAAQ,EAAE,CACRV,IAAI,CAACW,gBAAgB,EACrBV,KAAK,EACLI,YAAY;IAEhB,CAAC,EACD;MACED,KAAK,EAAE,IAAI;MACXE,GAAG,EAAE,IAAI;MACTG,OAAO,EAAE,KAAK;MACdC,QAAQ,EAAE,CACRV,IAAI,CAACW,gBAAgB,EACrBV,KAAK,EACLI,YAAY;IAEhB,CAAC,EACD;MACED,KAAK,EAAE,GAAG;MACVE,GAAG,EAAE,GAAG;MACRG,OAAO,EAAE,KAAK;MACdC,QAAQ,EAAE,CACRV,IAAI,CAACW,gBAAgB,EACrBV,KAAK,EACLI,YAAY;IAEhB,CAAC;EAEL,CAAC;EACDA,YAAY,CAACK,QAAQ,GAAG,CACtBV,IAAI,CAACY,aAAa,EAClBJ,MAAM,CACP;EAED,MAAMK,cAAc,GAAG;EACrB;EACA,YAAY,EACZ,UAAU,EACV,UAAU,EACV,UAAU,EACV,UAAU,EACV,UAAU,EACV,MAAM,EACN,KAAK,EACL,OAAO,EACP,QAAQ,EACR,SAAS,EACT,QAAQ,EACR,KAAK,EACL,WAAW,EACX,QAAQ,EACR,cAAc,EACd,YAAY,EACZ,QAAQ,EACR,MAAM,EACN,KAAK,EACL,MAAM,EACN,QAAQ,EACR,KAAK,EACL,KAAK;EACL;EACA,SAAS,EACT,aAAa,CACd;EACD,MAAMC,uBAAuB,GAAGD,cAAc,CAACE,GAAG,CAAEC,CAAC,IAAK,GAAGA,CAAC,GAAG,CAAC;EAElE,MAAMC,QAAQ,GAAG;IACfC,OAAO,EAAE,qGAAqG,GAC5G,2GAA2G,GAC3G,6GAA6G,GAC7G,qFAAqF;IACvFC,QAAQ,EACNN,cAAc,CACXO,MAAM,CAACN,uBAAuB,CAAC,CAC/BM,MAAM,CAAC;IACN;IACA,OAAO,EACP,MAAM,EACN,SAAS,EACT,OAAO;IACP;IACA,UAAU,EACV,eAAe,EACf,kBAAkB,EAClB,QAAQ,CACT,CAAC;IACNC,QAAQ,EAAE;EACZ,CAAC;EAED,OAAO;IACLC,IAAI,EAAE,MAAM;IACZf,QAAQ,EAAEU,QAAQ;IAClBP,QAAQ,EAAE,CACRF,MAAM,EACNR,IAAI,CAACuB,OAAO,CACV,cAAc,EACd,MAAM,EACN;MACEC,WAAW,EAAE,UAAU;MACvBC,SAAS,EAAE;IACb,CACF,CAAC,EACDzB,IAAI,CAACuB,OAAO,CACV,UAAU,EACV,GAAG,EAAE;MACHb,QAAQ,EAAE,CAAC;QACTc,WAAW,EAAE,UAAU;QACvBpB,KAAK,EAAE,GAAG;QACVE,GAAG,EAAE,GAAG;QACRmB,SAAS,EAAE;MACb,CAAC;IACH,CACF,CAAC,EACDzB,IAAI,CAAC0B,mBAAmB,EACxB1B,IAAI,CAAC2B,oBAAoB,EACzB;MACEzB,SAAS,EAAE,OAAO;MAClB0B,aAAa,EAAE,iBAAiB;MAChCtB,GAAG,EAAE,IAAI;MACTuB,UAAU,EAAE,IAAI;MAChBnB,QAAQ,EAAE,CACR;QACEkB,aAAa,EAAE;MACjB,CAAC,EACD5B,IAAI,CAAC8B,qBAAqB;IAE9B,CAAC,EACD9B,IAAI,CAACY,aAAa,EAClB;MACEV,SAAS,EAAE,MAAM;MACjBE,KAAK,EAAE;IACT,CAAC,EACD;MACEA,KAAK,EAAE,IAAI,CAAC;IACd,CAAC;EAEL,CAAC;AACH;AAEA2B,MAAM,CAACC,OAAO,GAAGjC,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}