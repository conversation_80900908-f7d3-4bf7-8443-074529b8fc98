#!/usr/bin/env python3
"""
检查当前字段映射状态，特别是科目相关字段
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import sqlite3

def check_account_field_mappings():
    """检查科目相关字段的映射状态"""
    print("🔍 检查科目相关字段映射状态")
    print("=" * 60)
    
    try:
        # 连接到resource.db
        project_root = os.path.dirname(os.path.dirname(__file__))
        db_path = os.path.join(project_root, 'resource.db')
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 查询科目相关字段的映射
        cursor.execute('''
            SELECT sc.id, sc.column_name, vm.nl_term, vm.db_value
            FROM schemacolumn sc
            JOIN schematable st ON sc.table_id = st.id
            JOIN dbconnection dc ON st.connection_id = dc.id
            LEFT JOIN valuemapping vm ON sc.id = vm.column_id
            WHERE dc.name = 'fin_data' 
            AND st.table_name = 'financial_data'
            AND sc.column_name IN ('account_full_name', 'account_name', 'account_code')
            ORDER BY sc.column_name, vm.nl_term
        ''')
        
        results = cursor.fetchall()
        
        # 按字段分组显示
        current_field = None
        for col_id, col_name, nl_term, db_value in results:
            if col_name != current_field:
                current_field = col_name
                print(f"\n🔸 {col_name} (ID: {col_id}):")
            
            if nl_term:
                print(f"    '{nl_term}' → '{db_value}'")
            else:
                print(f"    ❌ 无映射")
        
        # 检查关键术语的映射情况
        print(f"\n📋 关键术语映射检查:")
        key_terms = ['管理费用', '科目名称', '会计科目名称', 'account_name', '科目']
        
        for term in key_terms:
            cursor.execute('''
                SELECT sc.column_name, vm.db_value
                FROM valuemapping vm
                JOIN schemacolumn sc ON vm.column_id = sc.id
                JOIN schematable st ON sc.table_id = st.id
                JOIN dbconnection dc ON st.connection_id = dc.id
                WHERE vm.nl_term = ? AND dc.name = 'fin_data' AND st.table_name = 'financial_data'
            ''', (term,))
            
            mapping_results = cursor.fetchall()
            if mapping_results:
                for col_name, db_value in mapping_results:
                    print(f"  ✅ '{term}' → {col_name}")
            else:
                print(f"  ❌ '{term}' → 未找到映射")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 检查科目字段映射失败: {e}")

def check_account_code_mappings():
    """检查account_code的映射，看是否有1000这样的值"""
    print(f"\n🔍 检查account_code映射")
    print("=" * 60)
    
    try:
        project_root = os.path.dirname(os.path.dirname(__file__))
        db_path = os.path.join(project_root, 'resource.db')
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 查询account_code的所有映射
        cursor.execute('''
            SELECT vm.nl_term, vm.db_value
            FROM valuemapping vm
            JOIN schemacolumn sc ON vm.column_id = sc.id
            JOIN schematable st ON sc.table_id = st.id
            JOIN dbconnection dc ON st.connection_id = dc.id
            WHERE sc.column_name = 'account_code' 
            AND dc.name = 'fin_data' 
            AND st.table_name = 'financial_data'
            ORDER BY vm.nl_term
        ''')
        
        mappings = cursor.fetchall()
        
        print(f"📋 account_code的映射:")
        if mappings:
            for nl_term, db_value in mappings:
                print(f"    '{nl_term}' → '{db_value}'")
                if db_value == '1000':
                    print(f"      ⚠️ 发现1000映射！")
        else:
            print(f"    ❌ account_code无映射")
        
        # 检查是否有管理费用相关的映射
        cursor.execute('''
            SELECT vm.nl_term, vm.db_value
            FROM valuemapping vm
            JOIN schemacolumn sc ON vm.column_id = sc.id
            JOIN schematable st ON sc.table_id = st.id
            JOIN dbconnection dc ON st.connection_id = dc.id
            WHERE vm.nl_term LIKE '%管理费用%'
            AND dc.name = 'fin_data' 
            AND st.table_name = 'financial_data'
        ''')
        
        mgmt_mappings = cursor.fetchall()
        
        print(f"\n📋 管理费用相关映射:")
        if mgmt_mappings:
            for nl_term, db_value in mgmt_mappings:
                print(f"    '{nl_term}' → '{db_value}'")
        else:
            print(f"    ❌ 无管理费用相关映射")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 检查account_code映射失败: {e}")

def main():
    """主函数"""
    print("🚀 检查当前字段映射状态")
    print("=" * 80)
    
    check_account_field_mappings()
    check_account_code_mappings()
    
    print("\n" + "=" * 80)
    print("📊 分析结论:")
    print("1. 如果account_full_name有正确映射但系统未使用，可能是优先级问题")
    print("2. 如果account_code映射到1000，说明配置有问题")
    print("3. 如果缺少管理费用映射，需要添加相关配置")

if __name__ == "__main__":
    main()
