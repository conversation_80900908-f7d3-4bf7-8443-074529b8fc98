"""
直接测试性能监控API
"""
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from fastapi.testclient import TestClient

# 直接导入main模块
import main
app = main.app

def test_performance_apis():
    """测试性能监控API"""
    print("🧪 测试性能监控API...")
    
    client = TestClient(app)
    
    try:
        # 测试健康检查
        print("测试健康检查API...")
        response = client.get("/api/performance/health")
        print(f"健康检查状态码: {response.status_code}")
        if response.status_code == 200:
            print("✅ 健康检查API正常")
            data = response.json()
            print(f"响应数据: {data}")
        else:
            print(f"❌ 健康检查API失败: {response.text}")
            return False
        
        # 测试性能指标
        print("\n测试性能指标API...")
        response = client.get("/api/performance/metrics")
        print(f"性能指标状态码: {response.status_code}")
        if response.status_code == 200:
            print("✅ 性能指标API正常")
            data = response.json()
            print(f"响应数据: {data}")
        else:
            print(f"❌ 性能指标API失败: {response.text}")
            return False
        
        # 测试系统诊断
        print("\n测试系统诊断API...")
        response = client.get("/api/performance/diagnostics")
        print(f"系统诊断状态码: {response.status_code}")
        if response.status_code == 200:
            print("✅ 系统诊断API正常")
            data = response.json()
            print(f"响应数据: {data}")
        else:
            print(f"❌ 系统诊断API失败: {response.text}")
            return False
        
        # 测试活动查询
        print("\n测试活动查询API...")
        response = client.get("/api/performance/active-queries")
        print(f"活动查询状态码: {response.status_code}")
        if response.status_code == 200:
            print("✅ 活动查询API正常")
            data = response.json()
            print(f"响应数据: {data}")
        else:
            print(f"❌ 活动查询API失败: {response.text}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ API测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_performance_apis()
    if success:
        print("\n🎉 所有性能监控API测试通过！")
        print("\n现在你可以通过以下方式访问API：")
        print("1. 启动后端服务：uvicorn app.main:app --reload --host 0.0.0.0 --port 8000")
        print("2. 访问API文档：http://localhost:8000/docs")
        print("3. 直接访问API端点：")
        print("   - http://localhost:8000/api/performance/health")
        print("   - http://localhost:8000/api/performance/metrics")
        print("   - http://localhost:8000/api/performance/diagnostics")
    else:
        print("\n❌ API测试失败！")
        sys.exit(1)
