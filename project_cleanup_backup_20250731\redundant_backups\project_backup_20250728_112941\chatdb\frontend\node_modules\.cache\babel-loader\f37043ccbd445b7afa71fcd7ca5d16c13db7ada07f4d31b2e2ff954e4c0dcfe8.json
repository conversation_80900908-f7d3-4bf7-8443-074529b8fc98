{"ast": null, "code": "/**\n * @param {string} value\n * @returns {string}\n */\nexport function normalize(value) {\n  return value.toLowerCase();\n}", "map": {"version": 3, "names": ["normalize", "value", "toLowerCase"], "sources": ["C:/Users/<USER>/PycharmProjects/智能数据分析系统/chatdb/frontend/node_modules/react-markdown/node_modules/property-information/lib/normalize.js"], "sourcesContent": ["/**\n * @param {string} value\n * @returns {string}\n */\nexport function normalize(value) {\n  return value.toLowerCase()\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,OAAO,SAASA,SAASA,CAACC,KAAK,EAAE;EAC/B,OAAOA,KAAK,CAACC,WAAW,CAAC,CAAC;AAC5B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}