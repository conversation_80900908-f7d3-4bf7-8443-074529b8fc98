"""
Text2SQL性能监控服务
专门监控Text2SQL管道的7个节点性能
识别和预防系统卡顿问题
"""
import asyncio
import logging
import time
import threading
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
from collections import defaultdict, deque
import psutil
import json

logger = logging.getLogger(__name__)


class Text2SQLPerformanceMonitor:
    """Text2SQL性能监控器"""
    
    def __init__(self):
        # 性能指标存储
        self.pipeline_metrics = {
            'node1_schema_retrieval': deque(maxlen=100),
            'node2_field_mapping': deque(maxlen=100),
            'node3_metadata_retrieval': deque(maxlen=100),
            'node4_schema_enhancement': deque(maxlen=100),
            'node5_prompt_construction': deque(maxlen=100),
            'node6_llm_generation': deque(maxlen=100),
            'node7_sql_processing': deque(maxlen=100),
            'total_pipeline': deque(maxlen=100)
        }
        
        # 错误统计
        self.error_stats = defaultdict(int)
        
        # 系统资源监控
        self.system_metrics = {
            'memory_usage': deque(maxlen=60),  # 最近60分钟
            'cpu_usage': deque(maxlen=60),
            'active_connections': deque(maxlen=60)
        }
        
        # 阈值配置
        self.thresholds = {
            'node_warning_time': 5.0,  # 单节点警告时间（秒）
            'node_critical_time': 10.0,  # 单节点严重时间（秒）
            'total_warning_time': 15.0,  # 总管道警告时间（秒）
            'total_critical_time': 30.0,  # 总管道严重时间（秒）
            'memory_warning': 80.0,  # 内存使用警告阈值（%）
            'memory_critical': 90.0,  # 内存使用严重阈值（%）
        }
        
        # 监控状态
        self.monitoring_active = False
        self.monitor_thread = None
        
        # 当前活动查询
        self.active_queries = {}
        
        # 启动监控
        self.start_monitoring()
    
    def start_monitoring(self):
        """启动性能监控"""
        if not self.monitoring_active:
            self.monitoring_active = True
            self.monitor_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
            self.monitor_thread.start()
            logger.info("Text2SQL性能监控已启动")
    
    def stop_monitoring(self):
        """停止性能监控"""
        self.monitoring_active = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5)
        logger.info("Text2SQL性能监控已停止")
    
    def _monitoring_loop(self):
        """监控循环"""
        while self.monitoring_active:
            try:
                # 收集系统指标
                self._collect_system_metrics()
                
                # 检查性能健康状态
                self._check_performance_health()
                
                # 清理过期数据
                self._cleanup_expired_data()
                
                time.sleep(60)  # 每分钟检查一次
                
            except Exception as e:
                logger.error(f"性能监控循环错误: {e}")
    
    def _collect_system_metrics(self):
        """收集系统指标"""
        try:
            # 内存使用率
            memory_percent = psutil.virtual_memory().percent
            self.system_metrics['memory_usage'].append({
                'timestamp': datetime.now(),
                'value': memory_percent
            })
            
            # CPU使用率
            cpu_percent = psutil.cpu_percent(interval=1)
            self.system_metrics['cpu_usage'].append({
                'timestamp': datetime.now(),
                'value': cpu_percent
            })
            
            # 活动连接数（估算）
            active_count = len(self.active_queries)
            self.system_metrics['active_connections'].append({
                'timestamp': datetime.now(),
                'value': active_count
            })
            
        except Exception as e:
            logger.error(f"收集系统指标失败: {e}")
    
    def _check_performance_health(self):
        """检查性能健康状态"""
        issues = []
        
        # 检查内存使用
        if self.system_metrics['memory_usage']:
            latest_memory = self.system_metrics['memory_usage'][-1]['value']
            if latest_memory > self.thresholds['memory_critical']:
                issues.append(f"内存使用严重过高: {latest_memory:.1f}%")
            elif latest_memory > self.thresholds['memory_warning']:
                issues.append(f"内存使用警告: {latest_memory:.1f}%")
        
        # 检查长时间运行的查询
        current_time = time.time()
        for query_id, query_info in self.active_queries.items():
            duration = current_time - query_info['start_time']
            if duration > self.thresholds['total_critical_time']:
                issues.append(f"查询{query_id}运行时间过长: {duration:.1f}秒")
        
        # 记录问题
        if issues:
            logger.warning(f"性能健康检查发现问题: {'; '.join(issues)}")
    
    def _cleanup_expired_data(self):
        """清理过期数据"""
        try:
            # 清理超过1小时的活动查询
            current_time = time.time()
            expired_queries = [
                query_id for query_id, query_info in self.active_queries.items()
                if current_time - query_info['start_time'] > 3600
            ]
            
            for query_id in expired_queries:
                logger.warning(f"清理过期查询: {query_id}")
                self.active_queries.pop(query_id, None)
                
        except Exception as e:
            logger.error(f"清理过期数据失败: {e}")
    
    def start_query_monitoring(self, query_id: str, query: str) -> str:
        """开始查询监控"""
        self.active_queries[query_id] = {
            'query': query[:200],  # 只保存前200个字符
            'start_time': time.time(),
            'current_node': 'starting',
            'node_times': {},
            'errors': []
        }
        logger.info(f"开始监控查询: {query_id}")
        return query_id
    
    def record_node_performance(self, query_id: str, node_name: str, duration: float, success: bool = True, error: str = None):
        """记录节点性能"""
        # 更新管道指标
        self.pipeline_metrics[node_name].append({
            'timestamp': datetime.now(),
            'duration': duration,
            'success': success,
            'query_id': query_id
        })
        
        # 更新查询信息
        if query_id in self.active_queries:
            self.active_queries[query_id]['node_times'][node_name] = duration
            self.active_queries[query_id]['current_node'] = node_name
            
            if not success and error:
                self.active_queries[query_id]['errors'].append({
                    'node': node_name,
                    'error': error,
                    'timestamp': datetime.now()
                })
                self.error_stats[node_name] += 1
        
        # 检查性能阈值
        if duration > self.thresholds['node_critical_time']:
            logger.error(f"节点{node_name}性能严重: {duration:.2f}秒 (查询: {query_id})")
        elif duration > self.thresholds['node_warning_time']:
            logger.warning(f"节点{node_name}性能警告: {duration:.2f}秒 (查询: {query_id})")
    
    def finish_query_monitoring(self, query_id: str, total_duration: float, success: bool = True):
        """完成查询监控"""
        if query_id in self.active_queries:
            # 记录总时间
            self.pipeline_metrics['total_pipeline'].append({
                'timestamp': datetime.now(),
                'duration': total_duration,
                'success': success,
                'query_id': query_id
            })
            
            # 移除活动查询
            query_info = self.active_queries.pop(query_id)
            
            # 记录完成日志
            if success:
                logger.info(f"查询监控完成: {query_id}, 总时间: {total_duration:.2f}秒")
            else:
                logger.error(f"查询监控完成(失败): {query_id}, 总时间: {total_duration:.2f}秒")
            
            # 检查总时间阈值
            if total_duration > self.thresholds['total_critical_time']:
                logger.error(f"查询总时间严重过长: {total_duration:.2f}秒 (查询: {query_id})")
            elif total_duration > self.thresholds['total_warning_time']:
                logger.warning(f"查询总时间警告: {total_duration:.2f}秒 (查询: {query_id})")
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """获取性能摘要"""
        summary = {
            'active_queries': len(self.active_queries),
            'node_performance': {},
            'system_metrics': {},
            'error_stats': dict(self.error_stats),
            'health_status': 'healthy'
        }
        
        # 节点性能统计
        for node_name, metrics in self.pipeline_metrics.items():
            if metrics:
                durations = [m['duration'] for m in metrics if m['success']]
                if durations:
                    summary['node_performance'][node_name] = {
                        'avg_duration': sum(durations) / len(durations),
                        'max_duration': max(durations),
                        'min_duration': min(durations),
                        'success_rate': len(durations) / len(metrics) * 100,
                        'total_calls': len(metrics)
                    }
        
        # 系统指标
        for metric_name, values in self.system_metrics.items():
            if values:
                latest_values = [v['value'] for v in values[-10:]]  # 最近10个值
                summary['system_metrics'][metric_name] = {
                    'current': latest_values[-1] if latest_values else 0,
                    'avg': sum(latest_values) / len(latest_values),
                    'max': max(latest_values),
                    'min': min(latest_values)
                }
        
        # 健康状态评估
        if summary['system_metrics'].get('memory_usage', {}).get('current', 0) > self.thresholds['memory_critical']:
            summary['health_status'] = 'critical'
        elif summary['system_metrics'].get('memory_usage', {}).get('current', 0) > self.thresholds['memory_warning']:
            summary['health_status'] = 'warning'
        
        return summary


# 全局监控实例
text2sql_monitor = Text2SQLPerformanceMonitor()
